#!/usr/bin/env node

/**
 * 查找可能缺少await的数据库操作调用
 * 该脚本会扫描所有JavaScript文件，找出可能的异步数据库调用但缺少await关键字的地方
 */

const fs = require('fs');
const path = require('path');

// 数据库操作的正则表达式
const DB_PATTERNS = [
    /db\.(servers|load|traffic|setting|groups|ssh_scripts|notifications|licenses|users|sessions|cache)\.(get|set|all|ins|upd|del|update|insert|delete|batchInsert)\(/g
];

// 排除的目录
const EXCLUDE_DIRS = ['node_modules', '.git', 'tdd-workspace', 'tests', 'scripts'];

// 可能的问题列表
const issues = [];

/**
 * 检查文件中的数据库调用
 */
function checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
        const lineNum = index + 1;
        
        // 跳过注释行
        if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
            return;
        }
        
        // 检查每个数据库模式
        DB_PATTERNS.forEach(pattern => {
            const matches = line.match(pattern);
            if (matches) {
                // 检查是否有await、return、.then或.catch
                const hasAwait = line.includes('await');
                const hasReturn = line.trim().startsWith('return');
                const hasPromiseHandling = line.includes('.then') || line.includes('.catch');
                const isAssignment = line.includes('=') && !line.includes('await');
                const isInAsyncContext = checkIfInAsyncFunction(lines, index);
                
                // 如果没有await且没有promise处理，且在异步上下文中，可能是问题
                if (!hasAwait && !hasReturn && !hasPromiseHandling && isInAsyncContext) {
                    // 获取更多上下文
                    const context = getContext(lines, index, 2);
                    
                    issues.push({
                        file: filePath,
                        line: lineNum,
                        code: line.trim(),
                        match: matches[0],
                        context: context,
                        severity: isAssignment ? 'HIGH' : 'MEDIUM'
                    });
                }
            }
        });
    });
}

/**
 * 检查是否在异步函数中
 */
function checkIfInAsyncFunction(lines, currentIndex) {
    // 向上搜索函数定义
    for (let i = currentIndex - 1; i >= 0 && i > currentIndex - 50; i--) {
        const line = lines[i];
        if (line.includes('async') && (line.includes('function') || line.includes('=>'))) {
            return true;
        }
        // 如果遇到函数结束，停止搜索
        if (line.trim() === '}' && i < currentIndex - 10) {
            // 检查是否是函数结束
            let braceCount = 1;
            for (let j = i - 1; j >= 0 && braceCount > 0; j--) {
                if (lines[j].includes('{')) braceCount--;
                if (lines[j].includes('}')) braceCount++;
                if (braceCount === 0 && lines[j].includes('function')) {
                    return lines[j].includes('async');
                }
            }
        }
    }
    return false;
}

/**
 * 获取代码上下文
 */
function getContext(lines, index, contextLines) {
    const start = Math.max(0, index - contextLines);
    const end = Math.min(lines.length - 1, index + contextLines);
    const context = [];
    
    for (let i = start; i <= end; i++) {
        const prefix = i === index ? '>>> ' : '    ';
        context.push(`${prefix}${i + 1}: ${lines[i]}`);
    }
    
    return context.join('\n');
}

/**
 * 递归扫描目录
 */
function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            // 跳过排除的目录
            if (!EXCLUDE_DIRS.includes(item)) {
                scanDirectory(fullPath);
            }
        } else if (stat.isFile() && item.endsWith('.js')) {
            checkFile(fullPath);
        }
    });
}

// 主函数
function main() {
    console.log('正在扫描可能缺少await的数据库操作...\n');
    
    const projectRoot = path.join(__dirname, '..');
    scanDirectory(projectRoot);
    
    if (issues.length === 0) {
        console.log('✅ 未发现可能缺少await的数据库操作！');
    } else {
        console.log(`⚠️  发现 ${issues.length} 个可能缺少await的数据库操作：\n`);
        
        // 按严重程度排序
        issues.sort((a, b) => {
            if (a.severity !== b.severity) {
                return a.severity === 'HIGH' ? -1 : 1;
            }
            return a.file.localeCompare(b.file);
        });
        
        // 输出问题
        issues.forEach((issue, index) => {
            console.log(`${index + 1}. [${issue.severity}] ${issue.file}:${issue.line}`);
            console.log(`   匹配: ${issue.match}`);
            console.log(`   代码: ${issue.code}`);
            console.log(`   上下文:\n${issue.context}`);
            console.log('');
        });
        
        // 统计
        const highCount = issues.filter(i => i.severity === 'HIGH').length;
        const mediumCount = issues.filter(i => i.severity === 'MEDIUM').length;
        
        console.log('\n📊 统计:');
        console.log(`   高严重性: ${highCount}`);
        console.log(`   中严重性: ${mediumCount}`);
        console.log(`   总计: ${issues.length}`);
    }
}

// 运行脚本
main();