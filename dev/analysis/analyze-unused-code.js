#!/usr/bin/env node
/**
 * 分析项目中未使用的导入和函数
 */

const fs = require('fs');
const path = require('path');

class UnusedCodeAnalyzer {
    constructor() {
        this.results = {
            unusedImports: {},
            unusedFunctions: {}
        };
        this.ignorePaths = [
            'node_modules',
            'dist',
            'build',
            '.git',
            'archive',
            'data',
            'logs',
            'backup',
            'tdd-workspace'
        ];
    }

    // 检查路径是否应该被忽略
    shouldIgnorePath(filePath) {
        return this.ignorePaths.some(ignore => 
            filePath.includes(path.sep + ignore + path.sep) || 
            filePath.includes('/' + ignore + '/')
        );
    }

    // 获取所有JS文件
    getAllJsFiles(dir, fileList = []) {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const filePath = path.join(dir, file);
            
            if (this.shouldIgnorePath(filePath)) {
                return;
            }
            
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
                this.getAllJsFiles(filePath, fileList);
            } else if (file.endsWith('.js')) {
                fileList.push(filePath);
            }
        });
        
        return fileList;
    }

    // 分析单个文件的导入
    analyzeImports(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const imports = [];
        
        // 匹配 require 语句
        const requireRegex = /(?:const|let|var)\s+(\w+)\s*=\s*require\s*\(['"]([^'"]+)['"]\)/g;
        const destructuringRequireRegex = /(?:const|let|var)\s+\{([^}]+)\}\s*=\s*require\s*\(['"]([^'"]+)['"]\)/g;
        
        let match;
        
        // 匹配普通 require
        while ((match = requireRegex.exec(content)) !== null) {
            imports.push({
                type: 'require',
                name: match[1],
                module: match[2],
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        // 匹配解构 require
        while ((match = destructuringRequireRegex.exec(content)) !== null) {
            const names = match[1].split(',').map(n => n.trim());
            names.forEach(name => {
                imports.push({
                    type: 'destructuring',
                    name: name,
                    module: match[2],
                    line: content.substring(0, match.index).split('\n').length
                });
            });
        }
        
        // 检查每个导入是否被使用
        const unusedImports = [];
        
        imports.forEach(imp => {
            // 创建正则表达式来查找使用情况
            const usageRegex = new RegExp(`\\b${imp.name}\\b`, 'g');
            const contentWithoutImport = content.split('\n');
            contentWithoutImport[imp.line - 1] = ''; // 移除导入行
            const remainingContent = contentWithoutImport.join('\n');
            
            // 统计使用次数（排除导入行本身）
            const matches = remainingContent.match(usageRegex);
            const usageCount = matches ? matches.length : 0;
            
            if (usageCount === 0) {
                unusedImports.push(imp);
            }
        });
        
        return unusedImports;
    }

    // 分析单个文件的函数
    analyzeFunctions(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const functions = [];
        
        // 匹配函数定义
        const functionPatterns = [
            // function name() {}
            /function\s+(\w+)\s*\(/g,
            // const name = function() {}
            /(?:const|let|var)\s+(\w+)\s*=\s*function\s*\(/g,
            // const name = () => {}
            /(?:const|let|var)\s+(\w+)\s*=\s*\([^)]*\)\s*=>/g,
            // const name = async () => {}
            /(?:const|let|var)\s+(\w+)\s*=\s*async\s*\([^)]*\)\s*=>/g,
            // const name = async function() {}
            /(?:const|let|var)\s+(\w+)\s*=\s*async\s+function\s*\(/g
        ];
        
        functionPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                functions.push({
                    name: match[1],
                    line: content.substring(0, match.index).split('\n').length
                });
            }
        });
        
        // 检查每个函数是否被使用
        const unusedFunctions = [];
        
        // 获取项目中所有文件的内容（用于跨文件检查）
        const projectRoot = path.resolve(__dirname, '..');
        const allFiles = this.getAllJsFiles(projectRoot);
        
        functions.forEach(func => {
            let isUsed = false;
            
            // 排除一些特殊函数
            const specialFunctions = ['constructor', 'render', 'componentDidMount', 'componentWillUnmount'];
            if (specialFunctions.includes(func.name)) {
                return;
            }
            
            // 检查是否被导出
            const exportRegex = new RegExp(`module\\.exports.*${func.name}|exports\\.${func.name}`);
            if (exportRegex.test(content)) {
                // 如果被导出，需要检查其他文件
                for (const file of allFiles) {
                    if (file === filePath) continue;
                    
                    try {
                        const otherContent = fs.readFileSync(file, 'utf8');
                        const usageRegex = new RegExp(`\\b${func.name}\\b`);
                        if (usageRegex.test(otherContent)) {
                            isUsed = true;
                            break;
                        }
                    } catch (err) {
                        // 忽略读取错误
                    }
                }
            } else {
                // 如果未导出，只检查当前文件
                const lines = content.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    if (i === func.line - 1) continue; // 跳过定义行
                    
                    const usageRegex = new RegExp(`\\b${func.name}\\b`);
                    if (usageRegex.test(lines[i])) {
                        isUsed = true;
                        break;
                    }
                }
            }
            
            if (!isUsed) {
                unusedFunctions.push(func);
            }
        });
        
        return unusedFunctions;
    }

    // 运行分析
    async analyze() {
        console.log('开始分析未使用的代码...\n');
        
        const projectRoot = path.resolve(__dirname, '..');
        const jsFiles = this.getAllJsFiles(projectRoot);
        
        console.log(`找到 ${jsFiles.length} 个 JavaScript 文件\n`);
        
        // 分析每个文件
        jsFiles.forEach(file => {
            const relativePath = path.relative(projectRoot, file);
            
            // 分析导入
            const unusedImports = this.analyzeImports(file);
            if (unusedImports.length > 0) {
                this.results.unusedImports[relativePath] = unusedImports;
            }
            
            // 分析函数（仅对主要文件进行，避免过度分析）
            if (!relativePath.includes('test') && !relativePath.includes('spec')) {
                const unusedFunctions = this.analyzeFunctions(file);
                if (unusedFunctions.length > 0) {
                    this.results.unusedFunctions[relativePath] = unusedFunctions;
                }
            }
        });
        
        // 输出结果
        this.printResults();
    }

    // 打印结果
    printResults() {
        console.log('=== 未使用的导入 ===\n');
        
        const importFiles = Object.keys(this.results.unusedImports);
        if (importFiles.length === 0) {
            console.log('没有发现未使用的导入\n');
        } else {
            importFiles.forEach(file => {
                console.log(`文件: ${file}`);
                this.results.unusedImports[file].forEach(imp => {
                    console.log(`  - 第 ${imp.line} 行: ${imp.name} (来自 ${imp.module})`);
                });
                console.log();
            });
        }
        
        console.log('=== 未使用的函数 ===\n');
        
        const functionFiles = Object.keys(this.results.unusedFunctions);
        if (functionFiles.length === 0) {
            console.log('没有发现未使用的函数\n');
        } else {
            functionFiles.forEach(file => {
                console.log(`文件: ${file}`);
                this.results.unusedFunctions[file].forEach(func => {
                    console.log(`  - 第 ${func.line} 行: ${func.name}`);
                });
                console.log();
            });
        }
        
        // 统计
        const totalUnusedImports = importFiles.reduce((sum, file) => 
            sum + this.results.unusedImports[file].length, 0);
        const totalUnusedFunctions = functionFiles.reduce((sum, file) => 
            sum + this.results.unusedFunctions[file].length, 0);
        
        console.log('=== 统计 ===');
        console.log(`未使用的导入总数: ${totalUnusedImports}`);
        console.log(`未使用的函数总数: ${totalUnusedFunctions}`);
        console.log(`受影响的文件数: ${importFiles.length + functionFiles.length}`);
    }
}

// 运行分析器
const analyzer = new UnusedCodeAnalyzer();
analyzer.analyze().catch(console.error);