# 开发分析工具

本目录包含开发阶段使用的代码分析工具，不是项目运行时的依赖。

## 未使用代码分析工具

### 使用方法
```bash
node dev/analysis/analyze-unused-code.js
```

### 功能说明
- 分析项目中未使用的导入（require/import）
- 查找定义但未调用的函数
- 生成详细的分析报告

### MCP (Serena) 的优势
1. **语义分析**：理解代码的实际引用关系，而非简单文本匹配
2. **精确定位**：通过符号系统准确找到函数定义和使用位置
3. **高效搜索**：支持复杂模式匹配和范围限定
4. **避免误判**：区分静态未使用和动态调用的代码

### 注意事项
- 分析结果仅供参考，删除前请仔细验证
- 某些函数可能通过动态方式调用（如全局对象、eval等）
- 第三方库文件（如echarts.min.js）的未使用函数是正常现象