{"servers": {"serena": {"command": "uv", "args": ["run", "serena", "start-mcp-server"], "env": {"SERENA_CONTEXT": "ide-assistant", "SERENA_PROJECT": "$(pwd)"}, "timeout": 30000}}, "playwright-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@microsoft/playwright-mcp", "--key", "5d001b51-971e-49b1-a13b-0a74ff938c2a"]}, "version": "1.0.0", "description": "DStatus项目的最小化MCP配置 - 提供语义代码检索和编辑功能"}