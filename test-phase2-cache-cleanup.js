/**
 * Phase 2 缓存清理效果测试
 * 验证基于第一性原理的缓存清理是否生效
 */

console.log('=== Phase 2 缓存清理效果测试 ===');

// 测试1: 验证实时数据缓存已禁用
console.log('\n1. 测试实时数据缓存禁用状态：');

// 测试节点数据缓存
if (typeof window.CacheManager !== 'undefined') {
    console.log('✅ CacheManager 存在');
    
    // 测试save方法（应该被禁用）
    try {
        window.CacheManager.save({ test: 'data' });
        console.log('✅ save方法调用成功（已禁用，不实际缓存）');
    } catch (e) {
        console.error('❌ save方法调用失败:', e);
    }
    
    // 测试load方法（应该返回null）
    try {
        const result = window.CacheManager.load();
        if (result === null) {
            console.log('✅ load方法返回null（已禁用缓存）');
        } else {
            console.warn('⚠️ load方法返回了数据，可能缓存未完全禁用');
        }
    } catch (e) {
        console.error('❌ load方法调用失败:', e);
    }
} else {
    console.error('❌ CacheManager 不存在');
}

// 测试2: 验证端口隔离的配置缓存
console.log('\n2. 测试端口隔离的配置缓存：');

const port = location.port || '80';
const host = location.hostname.replace(/\./g, '_');

// 测试主题缓存
const themeCacheKey = `theme_${host}_${port}`;
console.log(`主题缓存键: ${themeCacheKey}`);

// 测试个性化设置缓存
const personalizationCacheKey = `personalization-settings_${host}_${port}`;
console.log(`个性化设置缓存键: ${personalizationCacheKey}`);

// 测试3: 检查旧缓存是否被清理
console.log('\n3. 检查旧缓存清理状态：');

const legacyCacheKeys = [
    'dstatus_node_cache',
    'network_data_cache',
    'stats_connection_active',
    'stats_connection_timestamp',
    'theme',
    'personalization-settings'
];

legacyCacheKeys.forEach(key => {
    const value = localStorage.getItem(key);
    if (value === null) {
        console.log(`✅ 旧缓存键 ${key} 已清理`);
    } else {
        console.warn(`⚠️ 旧缓存键 ${key} 仍存在: ${value.substring(0, 50)}...`);
    }
});

// 测试4: 验证缓存迁移工具
console.log('\n4. 测试缓存迁移工具：');

if (typeof window.CacheMigration !== 'undefined') {
    console.log('✅ CacheMigration 工具存在');
    
    try {
        const migrationResult = window.CacheMigration.migrate();
        console.log('✅ 缓存迁移执行成功');
        console.log(`清理的缓存键数量: ${migrationResult.totalCleaned}`);
        if (migrationResult.errors.length > 0) {
            console.warn('⚠️ 迁移过程中的错误:', migrationResult.errors);
        }
    } catch (e) {
        console.error('❌ 缓存迁移执行失败:', e);
    }
} else {
    console.error('❌ CacheMigration 工具不存在');
}

// 测试5: 验证存储清理工具
console.log('\n5. 测试存储清理工具：');

if (typeof window.storageCleanup !== 'undefined') {
    console.log('✅ storageCleanup 工具存在');
} else {
    console.log('ℹ️ storageCleanup 工具不存在（可能是内联脚本）');
}

// 测试6: 验证数据流向
console.log('\n6. 验证数据流向：');

console.log('实时数据来源检查：');
if (typeof window.lastNodeData !== 'undefined') {
    console.log('✅ window.lastNodeData 存在（WebSocket实时数据）');
} else {
    console.log('ℹ️ window.lastNodeData 暂未初始化');
}

if (typeof window.updateNodeStats === 'function') {
    console.log('✅ window.updateNodeStats 函数存在');
} else {
    console.error('❌ window.updateNodeStats 函数不存在');
}

// 测试7: 检查localStorage使用情况
console.log('\n7. 当前localStorage使用情况：');

const allKeys = Object.keys(localStorage);
console.log(`总缓存键数量: ${allKeys.length}`);

const categorizedKeys = {
    theme: [],
    personalization: [],
    deprecated: [],
    other: []
};

allKeys.forEach(key => {
    if (key.includes('theme')) {
        categorizedKeys.theme.push(key);
    } else if (key.includes('personalization')) {
        categorizedKeys.personalization.push(key);
    } else if (key.includes('dstatus_node_cache') || key.includes('network_data_cache') || key.includes('stats_connection')) {
        categorizedKeys.deprecated.push(key);
    } else {
        categorizedKeys.other.push(key);
    }
});

console.log('缓存分类统计：');
console.log(`- 主题相关: ${categorizedKeys.theme.length} 个`);
console.log(`- 个性化设置: ${categorizedKeys.personalization.length} 个`);
console.log(`- 已废弃缓存: ${categorizedKeys.deprecated.length} 个`);
console.log(`- 其他缓存: ${categorizedKeys.other.length} 个`);

if (categorizedKeys.deprecated.length > 0) {
    console.warn('⚠️ 发现已废弃的缓存:', categorizedKeys.deprecated);
}

console.log('\n=== Phase 2 测试完成 ===');
console.log('预期结果：');
console.log('- 实时数据缓存已禁用');
console.log('- 配置缓存使用端口隔离');
console.log('- 旧缓存已清理');
console.log('- 数据流向简化为WebSocket实时推送');
