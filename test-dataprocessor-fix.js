/**
 * DataProcessor修复效果测试
 * 验证数据验证逻辑是否已修复
 */

console.log('=== DataProcessor修复效果测试 ===');

// 测试1: 检查DataProcessor是否存在
if (typeof window.DataProcessor === 'undefined') {
    console.error('❌ DataProcessor不存在');
} else {
    console.log('✅ DataProcessor存在');
    
    // 测试2: 验证函数是否可用
    const methods = ['validateNodeData', 'validateMessage', 'processMessage'];
    methods.forEach(method => {
        if (typeof window.DataProcessor[method] === 'function') {
            console.log(`✅ ${method} 方法可用`);
        } else {
            console.error(`❌ ${method} 方法不可用`);
        }
    });
    
    // 测试3: 测试验证逻辑
    console.log('\n测试验证逻辑：');
    
    // 测试空数据
    const emptyResult = window.DataProcessor.validateNodeData({});
    console.log('空数据验证结果:', emptyResult);
    
    // 测试有效数据（模拟真实节点数据）
    const validData = {
        'node1': {
            name: 'Test Node 1',
            stat: {
                net: {
                    delta: { in: 1000, out: 500 }
                }
            }
        },
        'node2': {
            name: 'Test Node 2'
            // 没有stat字段，测试放宽的验证条件
        },
        'node3': {
            name: 'Test Node 3',
            stat: false // stat为false的情况
        }
    };
    
    const validResult = window.DataProcessor.validateNodeData(validData);
    console.log('有效数据验证结果:', validResult);
    
    // 测试4: 测试消息处理
    console.log('\n测试消息处理：');
    
    const testMessage = {
        type: 'stats',
        timestamp: Date.now(),
        data: validData
    };
    
    const processResult = window.DataProcessor.processMessage(testMessage, false);
    console.log('消息处理结果:', processResult);
    
    if (processResult) {
        console.log('✅ 消息处理成功');
        console.log('处理后的节点数量:', processResult.nodeCount);
        console.log('总计数据:', processResult.totals);
    } else {
        console.error('❌ 消息处理失败');
    }
}

// 测试5: 检查当前WebSocket数据状态
console.log('\n检查当前数据状态：');

if (window.lastNodeData) {
    console.log('✅ window.lastNodeData存在');
    console.log('节点数量:', Object.keys(window.lastNodeData).length);
    console.log('节点ID列表:', Object.keys(window.lastNodeData));
    
    // 检查第一个节点的结构
    const firstNodeId = Object.keys(window.lastNodeData)[0];
    if (firstNodeId) {
        const firstNode = window.lastNodeData[firstNodeId];
        console.log('第一个节点结构:', {
            nodeId: firstNodeId,
            hasName: !!firstNode.name,
            hasStat: !!firstNode.stat,
            statType: typeof firstNode.stat,
            nodeData: firstNode
        });
    }
} else {
    console.log('ℹ️ window.lastNodeData不存在或为空');
}

// 测试6: 手动触发数据更新
console.log('\n手动触发数据更新：');

if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
    console.log('🔄 请求最新数据...');
    window.sharedClient.requestLastData();
} else if (window.statsWs && window.statsWs.readyState === WebSocket.OPEN) {
    console.log('🔄 WebSocket连接正常，等待数据推送...');
} else {
    console.log('⚠️ 没有可用的数据连接');
}

// 测试7: 监听数据更新事件
console.log('\n设置数据更新监听器：');

let updateCount = 0;
const originalUpdateNodeStats = window.updateNodeStats;

if (typeof originalUpdateNodeStats === 'function') {
    window.updateNodeStats = function(data, fromCache) {
        updateCount++;
        console.log(`📊 [监听器] 第${updateCount}次数据更新:`, {
            nodeCount: data ? Object.keys(data).length : 0,
            fromCache: fromCache,
            timestamp: Date.now()
        });
        
        // 调用原始函数
        return originalUpdateNodeStats.call(this, data, fromCache);
    };
    
    console.log('✅ 数据更新监听器已设置');
} else {
    console.log('❌ updateNodeStats函数不存在');
}

console.log('\n=== 测试完成 ===');
console.log('请观察控制台输出，查看数据处理情况');
