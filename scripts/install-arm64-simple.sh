#!/bin/bash
# DStatus ARM64 Docker 安装脚本 (修复版)
# Version: 1.3.0
# Updated: 2025-08-06
set -e

# 配置
DOWNLOAD_BASE="https://down.vps.mom/downloads/arm"
INSTALL_DIR="/opt/dstatus-arm64"
CONTAINER="dstatus-arm64"
IMAGE="dstatus-arm64"
PORT="5555"
VERSION=""  # 版本号，如 v1.0.0
FORCE=false
UNINSTALL=false
YES=false
LICENSE_KEY=""

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --port) PORT="$2"; shift 2 ;;
        --version) VERSION="$2"; shift 2 ;;
        --license-key) LICENSE_KEY="$2"; shift 2 ;;
        --force) FORCE=true; shift ;;
        --uninstall) UNINSTALL=true; shift ;;
        --yes) YES=true; shift ;;
        -h|--help) 
            echo "DStatus ARM64 安装脚本"
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --port PORT          服务端口 (默认: 5555)"
            echo "  --version VERSION    指定版本 (如: v1.0.0)"
            echo "  --license-key KEY    许可证密钥"
            echo "  --force              强制重新安装"
            echo "  --uninstall          卸载"
            echo "  --yes                跳过确认提示"
            echo "  -h, --help           显示帮助"
            exit 0 
            ;;
        *) shift ;;
    esac
done

# 日志
log() { echo "[$(date +'%H:%M:%S')] $1"; }
err() { echo "[错误] $1" >&2; exit 1; }
info() { echo "[信息] $1"; }

# 版本信息
SCRIPT_VERSION="1.3.0"
log "DStatus ARM64 安装脚本 v$SCRIPT_VERSION"

# 检查并安装 Docker
if ! command -v docker >/dev/null; then
    log "Docker 未安装，开始自动安装..."
    
    # 检测系统类型
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VER=$VERSION_ID
    else
        err "无法检测系统类型"
    fi
    
    # 安装 Docker
    case $OS in
        ubuntu|debian)
            log "在 $OS 上安装 Docker..."
            apt-get update -qq
            apt-get install -y ca-certificates curl gnupg
            install -m 0755 -d /etc/apt/keyrings
            curl -fsSL https://download.docker.com/linux/$OS/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
            chmod a+r /etc/apt/keyrings/docker.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$OS $(lsb_release -cs) stable" > /etc/apt/sources.list.d/docker.list
            apt-get update -qq
            apt-get install -y docker-ce docker-ce-cli containerd.io
            systemctl start docker
            systemctl enable docker
            ;;
        centos|rhel|fedora)
            log "在 $OS 上安装 Docker..."
            yum install -y yum-utils
            yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            yum install -y docker-ce docker-ce-cli containerd.io
            systemctl start docker
            systemctl enable docker
            ;;
        *)
            err "不支持的系统: $OS"
            ;;
    esac
    
    # 验证安装
    if command -v docker >/dev/null && docker ps >/dev/null 2>&1; then
        log "✅ Docker 安装成功"
    else
        err "Docker 安装失败"
    fi
fi

# 卸载
if $UNINSTALL; then
    log "卸载 DStatus ARM64..."
    docker stop $CONTAINER 2>/dev/null || true
    docker rm $CONTAINER 2>/dev/null || true
    docker rmi $IMAGE 2>/dev/null || true
    
    if ! $YES && [[ -d $INSTALL_DIR ]]; then
        read -p "删除数据目录 $INSTALL_DIR (包含数据库和日志)? [y/N] " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 备份提醒
            log "提示: 数据将被永久删除，建议先备份 $INSTALL_DIR/data"
            sleep 2
            rm -rf $INSTALL_DIR
        else
            log "保留数据目录: $INSTALL_DIR"
        fi
    fi
    log "✅ 卸载完成"
    exit 0
fi

# 强制重装
if $FORCE && docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER}$"; then
    log "强制重装（保留数据）..."
    # 备份数据
    if [[ -d $INSTALL_DIR/data ]]; then
        log "备份数据..."
        cp -r $INSTALL_DIR/data $INSTALL_DIR/data.backup.$(date +%s)
    fi
    docker stop $CONTAINER 2>/dev/null || true
    docker rm $CONTAINER 2>/dev/null || true
    docker rmi $IMAGE 2>/dev/null || true
fi

# 检查已存在
docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER}$" && err "容器已存在，使用 --force 重装"

# 安装
log "安装 DStatus ARM64 (端口: $PORT)..."
mkdir -p $INSTALL_DIR && cd $INSTALL_DIR

# 清理可能存在的旧文件
[[ -f arm64.tar.gz ]] && rm -f arm64.tar.gz
# 清理旧的程序文件（保留数据）
[[ -f dstatus-linux-arm64 ]] && rm -f dstatus-linux-arm64
[[ -f Dockerfile ]] && rm -f Dockerfile
[[ -d views ]] && rm -rf views
[[ -d static ]] && rm -rf static

# 构建下载URL
if [[ -n "$VERSION" ]]; then
    DOWNLOAD_URL="$DOWNLOAD_BASE/dstatus-arm64-${VERSION}.tar.gz"
    log "下载指定版本: $VERSION"
else
    DOWNLOAD_URL="$DOWNLOAD_BASE/dstatus-arm64.tar.gz"
    log "下载最新版本"
fi

# 下载
log "下载..."
if ! wget -q --show-progress "$DOWNLOAD_URL" -O arm64.tar.gz; then
    err "下载失败，请检查网络连接或版本号是否正确"
fi

# 验证文件
if [[ ! -f arm64.tar.gz ]]; then
    err "下载文件不存在"
fi

# 解压
log "解压文件..."
# 先尝试查看tar包结构
TAR_STRUCTURE=$(tar -tzf arm64.tar.gz | head -1)
if [[ "$TAR_STRUCTURE" == "dstatus-arm64-complete/" ]]; then
    log "检测到包含顶层目录的tar包结构"
    # 使用 --strip-components=1 跳过顶层目录
    if ! tar -xzf arm64.tar.gz --strip-components=1; then
        err "解压失败，文件可能损坏"
    fi
else
    log "检测到扁平化的tar包结构"
    # 直接解压
    if ! tar -xzf arm64.tar.gz; then
        err "解压失败，文件可能损坏"
    fi
fi

# 处理可能存在的子目录（兼容旧版本）
if [[ -d "dstatus-arm64-complete" ]] && [[ ! -f "dstatus-linux-arm64" ]]; then
    log "检测到旧版本目录结构，自动修复..."
    mv dstatus-arm64-complete/* . 2>/dev/null || true
    rmdir dstatus-arm64-complete 2>/dev/null || true
fi

# 验证关键文件
if [[ ! -f "dstatus-linux-arm64" ]]; then
    err "解压后未找到 dstatus-linux-arm64 可执行文件"
fi

# 确保执行权限
chmod +x dstatus-linux-arm64 2>/dev/null || true

# 创建镜像
cat > Dockerfile << 'EOF'
FROM ubuntu:24.04
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY . .
RUN chmod +x dstatus-linux-arm64
EXPOSE 5555
CMD ["./dstatus-linux-arm64"]
EOF

log "构建镜像..."
if ! docker build -q -t $IMAGE .; then
    err "Docker 镜像构建失败"
fi

# 修复权限
log "设置目录权限..."
chown -R root:root "$INSTALL_DIR"
find "$INSTALL_DIR" -type f -exec chmod 644 {} \; 2>/dev/null || true
find "$INSTALL_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || true
chmod +x dstatus-linux-arm64 2>/dev/null || true

# 创建完整的目录结构
log "创建数据目录..."
mkdir -p "$INSTALL_DIR"/{data,logs,data/backups,data/temp}
chmod -R 777 "$INSTALL_DIR"/data "$INSTALL_DIR"/logs

# 运行
log "启动容器..."

PLATFORM=""
[[ $(uname -m) == "x86_64" ]] && PLATFORM="--platform linux/arm64"

# 简化网络配置
if [[ "$PORT" == "5555" ]]; then
    # 默认端口使用host网络模式
    NETWORK_OPTS="--network host"
    PORT_MAPPING=""
    log "使用host网络模式"
else
    # 自定义端口使用端口映射
    NETWORK_OPTS=""
    PORT_MAPPING="-p ${PORT}:5555"
    log "使用端口映射: ${PORT}:5555"
fi

# 执行docker run
log "执行docker run命令..."
CONTAINER_ID=$(docker run -d \
    --name $CONTAINER \
    $PLATFORM \
    ${NETWORK_OPTS} ${PORT_MAPPING} \
    -v ${INSTALL_DIR}/data:/app/data \
    -v ${INSTALL_DIR}/logs:/tmp/dstatus-logs \
    -e DSTATUS_AUTO_LICENSE="$LICENSE_KEY" \
    --restart unless-stopped \
    $IMAGE 2>&1)

# 检查docker run是否成功
if [[ $? -eq 0 ]] && [[ -n "$CONTAINER_ID" ]]; then
    log "容器启动成功，ID: ${CONTAINER_ID:0:12}"
else
    err "容器启动失败: $CONTAINER_ID"
fi

# 验证
log "等待容器完全启动..."
sleep 5
if docker ps | grep -q $CONTAINER; then
    # 获取IP地址
    IP=$(hostname -I | awk '{print $1}')
    [[ -z "$IP" ]] && IP="localhost"
    
    echo ""
    echo "==========================================="
    log "✅ DStatus ARM64 安装成功！"
    echo "==========================================="
    echo ""
    echo "安装信息:"
    echo "  容器名称: $CONTAINER"
    echo "  安装目录: $INSTALL_DIR"
    echo "  数据目录: $INSTALL_DIR/data"
    echo "  日志目录: $INSTALL_DIR/logs"
    [[ -n "$VERSION" ]] && echo "  版本: $VERSION"
    echo ""
    echo "访问地址:"
    echo "  http://$IP:$PORT"
    echo "  http://localhost:$PORT"
    echo ""
    echo "管理命令:"
    echo "  查看日志: docker logs -f $CONTAINER"
    echo "  停止服务: docker stop $CONTAINER"
    echo "  启动服务: docker start $CONTAINER"
    echo "  重启服务: docker restart $CONTAINER"
    echo ""
    [[ -n "$LICENSE_KEY" ]] && log "许可证将在启动时自动激活"
else
    err "启动失败，请查看日志: docker logs $CONTAINER"
fi