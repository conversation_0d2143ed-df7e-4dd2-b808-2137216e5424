#!/bin/bash
# ============================================================
# test-network-modules.sh - 网络模块功能测试脚本
# ============================================================
# 测试新的模块化网络配置功能
# 
# 使用方式:
#   ./test-network-modules.sh [test-case]
# ============================================================

set -euo pipefail

# 加载模块
SCRIPT_DIR="$(dirname "${BASH_SOURCE[0]}")"
source "$SCRIPT_DIR/net-utils.sh"
source "$SCRIPT_DIR/net-ipv6.sh"

# 测试日志函数
test_log() { echo "[TEST] $*"; }
test_success() { echo "[✓] $*"; }
test_fail() { echo "[✗] $*"; }

# 测试1: IPv6支持检测
test_ipv6_detection() {
    test_log "测试 IPv6 支持检测..."
    
    if detect_system_ipv6; then
        test_success "系统IPv6支持检测: 通过"
    else
        test_fail "系统IPv6支持检测: 未通过 (可能系统未启用IPv6)"
    fi
    
    if detect_docker_ipv6; then
        test_success "Docker IPv6支持检测: 通过"
    else
        test_fail "Docker IPv6支持检测: 未通过 (可能Docker未启用IPv6)"
    fi
    
    if detect_full_ipv6_support; then
        test_success "完整IPv6环境检测: 通过"
    else
        test_fail "完整IPv6环境检测: 未通过"
    fi
}

# 测试2: 子网冲突检测
test_conflict_detection() {
    test_log "测试子网冲突检测..."
    
    # 测试明显不冲突的子网
    if check_ipv4_subnet_conflicts "**********/16"; then
        test_success "IPv4子网冲突检测: 通过 (**********/16 无冲突)"
    else
        test_fail "IPv4子网冲突检测: 失败 (**********/16 检测到冲突)"
    fi
    
    if check_ipv6_subnet_conflicts "fd99:test:abcd::/48"; then
        test_success "IPv6子网冲突检测: 通过 (fd99:test:abcd::/48 无冲突)"
    else
        test_fail "IPv6子网冲突检测: 失败 (fd99:test:abcd::/48 检测到冲突)"
    fi
    
    # 测试网络名冲突
    if check_network_name_conflicts "non_existent_network_12345"; then
        test_fail "网络名冲突检测: 失败 (不存在的网络被检测为冲突)"
    else
        test_success "网络名冲突检测: 通过 (不存在的网络正确识别为无冲突)"
    fi
}

# 测试3: 子网选择算法
test_subnet_selection() {
    test_log "测试子网选择算法..."
    
    # 测试IPv4子网选择
    local ipv4_result
    ipv4_result=$(find_available_ipv4_subnet)
    if [[ -n "$ipv4_result" ]]; then
        test_success "IPv4子网选择: 通过 (找到: $ipv4_result)"
    else
        test_fail "IPv4子网选择: 失败 (未找到可用子网)"
    fi
    
    # 测试IPv6子网选择
    local ipv6_result
    ipv6_result=$(find_available_ipv6_subnet)
    if [[ -n "$ipv6_result" ]]; then
        test_success "IPv6子网选择: 通过 (找到: $ipv6_result)"
    else
        test_fail "IPv6子网选择: 失败 (未找到可用子网)"
    fi
    
    # 测试组合子网选择
    local combined_result
    combined_result=$(find_available_subnet)
    if [[ -n "$combined_result" ]]; then
        test_success "组合子网选择: 通过 (找到: $combined_result)"
    else
        test_fail "组合子网选择: 失败 (未找到可用子网组合)"
    fi
}

# 测试4: 随机ULA生成
test_random_ula() {
    test_log "测试随机ULA生成..."
    
    for i in {1..5}; do
        local ula
        ula=$(generate_random_ula)
        if [[ "$ula" =~ ^fd[0-9a-f]{2}:[0-9a-f]{4}:[0-9a-f]{4}::/48$ ]]; then
            test_success "随机ULA生成 #$i: 通过 ($ula)"
        else
            test_fail "随机ULA生成 #$i: 失败 (格式错误: $ula)"
        fi
    done
}

# 测试5: 网络名生成
test_network_name_generation() {
    test_log "测试网络名生成..."
    
    local name1 name2
    name1=$(generate_unique_network_name "test")
    name2=$(generate_unique_network_name "test")
    
    if [[ "$name1" != "$name2" ]]; then
        test_success "网络名唯一性: 通过 ($name1 != $name2)"
    else
        test_fail "网络名唯一性: 失败 (生成了相同的名称)"
    fi
    
    if [[ "$name1" =~ ^test_[0-9]+$ ]]; then
        test_success "网络名格式: 通过 ($name1)"
    else
        test_fail "网络名格式: 失败 (格式不正确: $name1)"
    fi
}

# 测试6: Docker Compose配置生成
test_compose_generation() {
    test_log "测试Docker Compose配置生成..."
    
    local test_file="/tmp/test-compose-$$.yml"
    echo "# Test compose file" > "$test_file"
    
    # 测试IPv6网络配置生成
    generate_compose_ipv6_network "$test_file" "test_network" "**********/16" "fd00:test::/48" "false"
    
    if grep -q "enable_ipv6: true" "$test_file"; then
        test_success "IPv6配置生成: 通过 (enable_ipv6找到)"
    else
        test_fail "IPv6配置生成: 失败 (enable_ipv6未找到)"
    fi
    
    if grep -q "**********/16" "$test_file" && grep -q "fd00:test::/48" "$test_file"; then
        test_success "子网配置生成: 通过 (IPv4和IPv6子网都找到)"
    else
        test_fail "子网配置生成: 失败 (子网信息不完整)"
    fi
    
    # 清理测试文件
    rm -f "$test_file"
}

# 主测试函数
run_all_tests() {
    echo "======================================"
    echo "网络模块功能测试"
    echo "======================================"
    
    test_ipv6_detection
    echo
    test_conflict_detection
    echo
    test_subnet_selection  
    echo
    test_random_ula
    echo
    test_network_name_generation
    echo
    test_compose_generation
    
    echo
    echo "======================================"
    echo "测试完成"
    echo "======================================"
}

# 单独测试函数
run_single_test() {
    case "$1" in
        "ipv6"|"detection")
            test_ipv6_detection
            ;;
        "conflict"|"conflicts")
            test_conflict_detection
            ;;
        "subnet"|"subnets")
            test_subnet_selection
            ;;
        "ula"|"random")
            test_random_ula
            ;;
        "name"|"names")
            test_network_name_generation
            ;;
        "compose"|"config")
            test_compose_generation
            ;;
        *)
            echo "未知测试: $1"
            echo "可用测试: ipv6, conflict, subnet, ula, name, compose"
            exit 1
            ;;
    esac
}

# 主程序
main() {
    if [[ $# -eq 0 ]]; then
        run_all_tests
    else
        run_single_test "$1"
    fi
}

# 检查是否直接运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi