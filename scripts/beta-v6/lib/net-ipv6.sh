#!/bin/bash
# ============================================================
# net-ipv6.sh - IPv6网络配置专用模块
# ============================================================
# 专门处理Docker IPv6网络的检测、配置和管理
# 
# 依赖: net-utils.sh
# 使用方式:
#   source "$(dirname "$0")/lib/net-utils.sh"
#   source "$(dirname "$0")/lib/net-ipv6.sh"
# ============================================================

set -euo pipefail

# 加载依赖的基础网络工具
SCRIPT_DIR="$(dirname "${BASH_SOURCE[0]}")"
source "$SCRIPT_DIR/net-utils.sh"

# 日志函数
log_info() { echo "[INFO] $*"; }
log_warning() { echo "[WARNING] $*"; }
log_success() { echo "[SUCCESS] $*"; }
log_error() { echo "[ERROR] $*"; }

# 检测完整的IPv6支持状态
detect_full_ipv6_support() {
    if detect_system_ipv6 && detect_docker_ipv6; then
        return 0
    else
        return 1
    fi
}

# 生成Docker Compose IPv6网络配置段
generate_compose_ipv6_network() {
    local compose_file="$1"
    local network_name="$2"
    local ipv4_subnet="$3"
    local ipv6_subnet="$4"
    local use_routed_mode="${5:-false}"
    
    # 移除现有的networks配置段
    if [[ -f "$compose_file" ]] && grep -q '^networks:' "$compose_file" 2>/dev/null; then
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' '/^networks:/,$d' "$compose_file"
        else
            sed -i '/^networks:/,$d' "$compose_file"
        fi
    fi
    
    # 生成网络配置
    cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
    enable_ipv6: true
EOF

    # 根据模式选择不同的配置
    if [[ "$use_routed_mode" == "true" ]]; then
        # Routed模式 (Docker v27+)
        cat >> "$compose_file" << EOF
    driver_opts:
      com.docker.network.bridge.gateway_mode_ipv6: "routed"
EOF
    fi
    
    # 添加IPAM配置
    if [[ -n "$ipv4_subnet" && -n "$ipv6_subnet" ]]; then
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv4_subnet}
        - subnet: ${ipv6_subnet}
          gateway: $(echo ${ipv6_subnet} | sed 's/:\/.*$/:1/')
EOF
    elif [[ -n "$ipv6_subnet" ]]; then
        # 仅IPv6
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv6_subnet}
          gateway: $(echo ${ipv6_subnet} | sed 's/:\/.*$/:1/')
EOF
    fi
}

# 生成Docker Compose IPv4网络配置段
generate_compose_ipv4_network() {
    local compose_file="$1"
    local network_name="$2"
    local ipv4_subnet="$3"
    
    # 移除现有的networks配置段
    if [[ -f "$compose_file" ]] && grep -q '^networks:' "$compose_file" 2>/dev/null; then
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' '/^networks:/,$d' "$compose_file"
        else
            sed -i '/^networks:/,$d' "$compose_file"
        fi
    fi
    
    cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
EOF

    if [[ -n "$ipv4_subnet" ]]; then
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv4_subnet}
EOF
    fi
}

# 确保服务使用指定网络
attach_service_to_network() {
    local compose_file="$1"
    local network_name="$2"
    local service_name="${3:-dstatus-beta-monitor}"
    
    # 检查服务是否已有networks配置
    if ! grep -A5 "container_name: $service_name" "$compose_file" | grep -q 'networks:'; then
        # 在container_name行后添加networks配置
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' "/container_name: $service_name/a\\
    networks:\\
      - default" "$compose_file"
        else
            sed -i "/container_name: $service_name/a\\
    networks:\\
      - default" "$compose_file"
        fi
    fi
}

# 启用IPv6网络配置
enable_ipv6_network() {
    local compose_file="${1:-docker-compose.yml}"
    local use_routed_mode="${2:-false}"
    local force_network_name="${3:-}"
    
    log_info "正在配置IPv6双栈网络..."
    
    # 清理旧网络
    cleanup_old_networks
    
    # 生成唯一网络名
    local network_name
    if [[ -n "$force_network_name" ]]; then
        network_name="$force_network_name"
    else
        network_name=$(generate_unique_network_name "dstatus_beta")
    fi
    
    # 查找可用子网
    local subnet_info ipv4_subnet ipv6_subnet
    subnet_info=$(find_available_subnet)
    read -r ipv4_subnet ipv6_subnet <<< "$subnet_info"
    
    if [[ -z "$ipv4_subnet" && -z "$ipv6_subnet" ]]; then
        log_warning "未找到可用的自定义网络范围，使用Docker默认分配"
        # 生成简单的IPv6配置
        cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
    enable_ipv6: true
EOF
    else
        log_info "使用IPv4子网: $ipv4_subnet"
        log_info "使用IPv6子网: $ipv6_subnet"
        
        # 生成完整网络配置
        generate_compose_ipv6_network "$compose_file" "$network_name" "$ipv4_subnet" "$ipv6_subnet" "$use_routed_mode"
    fi
    
    # 确保服务使用正确的网络
    attach_service_to_network "$compose_file" "$network_name"
    
    if [[ "$use_routed_mode" == "true" ]]; then
        log_success "IPv6网络已成功配置 (routed模式): $network_name"
    else
        log_success "IPv6网络已成功配置 (bridge模式): $network_name"
    fi
    
    return 0
}

# 启用IPv4网络配置
enable_ipv4_network() {
    local compose_file="${1:-docker-compose.yml}"
    local force_network_name="${2:-}"
    
    log_info "正在配置IPv4网络..."
    
    # 清理旧网络
    cleanup_old_networks
    
    # 生成唯一网络名
    local network_name
    if [[ -n "$force_network_name" ]]; then
        network_name="$force_network_name"
    else
        network_name=$(generate_unique_network_name "dstatus_beta")
    fi
    
    # 查找可用IPv4子网
    local ipv4_subnet
    ipv4_subnet=$(find_available_ipv4_subnet)
    
    if [[ -n "$ipv4_subnet" ]]; then
        log_info "使用IPv4子网: $ipv4_subnet"
        generate_compose_ipv4_network "$compose_file" "$network_name" "$ipv4_subnet"
    else
        log_warning "未找到可用的IPv4子网，使用Docker默认分配"
        generate_compose_ipv4_network "$compose_file" "$network_name" ""
    fi
    
    # 确保服务使用正确的网络
    attach_service_to_network "$compose_file" "$network_name"
    
    log_success "IPv4网络已成功配置: $network_name"
    return 0
}

# 主要的网络配置函数
configure_network_with_modules() {
    local compose_file="${1:-docker-compose.yml}"
    local enable_ipv6="${2:-auto}"
    local use_routed_mode="${3:-false}"
    local force_network_name="${4:-}"
    
    log_info "正在检测Docker网络环境..."
    
    # 检查IPv6支持状态
    local ipv6_available=false
    if detect_full_ipv6_support; then
        ipv6_available=true
        log_info "检测到完整的IPv6支持 (系统+Docker)"
    else
        log_warning "IPv6支持不完整"
        if ! detect_system_ipv6; then
            log_warning "- 系统IPv6未启用或不可用"
        fi
        if ! detect_docker_ipv6; then
            log_warning "- Docker IPv6未启用"
        fi
    fi
    
    # 根据配置决定网络模式
    case "$enable_ipv6" in
        "true"|"force")
            if [[ "$ipv6_available" == "true" ]]; then
                enable_ipv6_network "$compose_file" "$use_routed_mode" "$force_network_name"
            else
                log_error "强制启用IPv6失败: IPv6环境不可用"
                return 1
            fi
            ;;
        "false"|"disable")
            enable_ipv4_network "$compose_file" "$force_network_name"
            ;;
        "auto"|*)
            if [[ "$ipv6_available" == "true" ]]; then
                enable_ipv6_network "$compose_file" "$use_routed_mode" "$force_network_name"
            else
                log_info "IPv6不可用，回退到IPv4-only模式"
                enable_ipv4_network "$compose_file" "$force_network_name"
            fi
            ;;
    esac
}

# 导出可用的公共函数
# 以下函数可被外部脚本调用:
# - detect_full_ipv6_support
# - configure_network_with_modules  
# - enable_ipv6_network
# - enable_ipv4_network
# - check_ipv6_support_status (来自net-utils.sh)