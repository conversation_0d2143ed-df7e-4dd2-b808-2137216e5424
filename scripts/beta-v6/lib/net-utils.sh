#!/bin/bash
# ============================================================
# net-utils.sh - 网络工具基础模块
# ============================================================
# 提供通用的网络检测、冲突检查和子网选择功能
# 
# 使用方式:
#   source "$(dirname "$0")/lib/net-utils.sh"
# ============================================================

set -euo pipefail

# 检查系统级IPv6支持
detect_system_ipv6() {
    # 检查内核IPv6模块文件是否存在
    [[ -f /proc/net/if_inet6 ]] || return 1
    
    # 检查IPv6是否被禁用
    local disabled=$(sysctl -n net.ipv6.conf.all.disable_ipv6 2>/dev/null || echo 1)
    [[ $disabled -eq 0 ]] || return 1
    
    # 检查是否有IPv6地址 - 先检查最基本的loopback
    if ip -6 addr show lo 2>/dev/null | grep -q '::1'; then
        return 0
    fi
    
    # 检查是否有任何IPv6地址
    if ip -6 addr show 2>/dev/null | grep -q 'inet6'; then
        return 0
    fi
    
    return 1
}

# 检查Docker守护进程IPv6支持
detect_docker_ipv6() {
    # 方法1: 使用官方Docker info API
    if docker info --format '{{.IPv6}}' 2>/dev/null | grep -q '^true$'; then
        return 0
    fi
    
    # 方法2: 检查daemon.json配置
    if [[ -f /etc/docker/daemon.json ]] && grep -q '"ipv6":[[:space:]]*true' /etc/docker/daemon.json 2>/dev/null; then
        return 0
    fi
    
    # 方法3: 检查默认bridge网络是否有IPv6配置
    if docker network inspect bridge --format '{{range .IPAM.Config}}{{.Subnet}}{{end}}' 2>/dev/null | grep -q ':'; then
        return 0
    fi
    
    return 1
}

# 生成随机ULA (Unique Local Address)
generate_random_ula() {
    # 生成40-bit全局ID (RFC 4193)
    local global_id=$(openssl rand -hex 5)
    # 格式化为 fdXX:XXXX:XXXX::/48
    printf "fd%s:%s:%s::/48" \
        "${global_id:0:2}" \
        "${global_id:2:4}" \
        "${global_id:6:4}"
}

# 检查网络名冲突
check_network_name_conflicts() {
    local network_name="$1"
    docker network inspect "$network_name" >/dev/null 2>&1
}

# 检查IPv4子网冲突
check_ipv4_subnet_conflicts() {
    local candidate_subnet="$1"
    
    [[ -z "$candidate_subnet" ]] && return 0
    
    # 获取现有Docker网络信息
    local existing_networks
    existing_networks=$(docker network ls --format '{{.Name}}' | grep -v "^bridge$\|^host$\|^none$" | head -20)
    
    # 检查Docker网络子网冲突
    for network in $existing_networks; do
        local network_subnets
        network_subnets=$(docker network inspect "$network" --format '{{range .IPAM.Config}}{{.Subnet}} {{end}}' 2>/dev/null || true)
        for subnet in $network_subnets; do
            # 排除IPv6地址
            if [[ ! "$subnet" =~ : ]]; then
                if [[ "$subnet" == "$candidate_subnet" ]]; then
                    return 1  # 完全匹配冲突
                fi
                # 如果有ipcalc可用，进行更精确的包含关系检查
                if command -v ipcalc >/dev/null 2>&1; then
                    if ipcalc -c "$candidate_subnet" "$subnet" 2>/dev/null || \
                       ipcalc -c "$subnet" "$candidate_subnet" 2>/dev/null; then
                        return 1  # 子网包含冲突
                    fi
                fi
            fi
        done
    done
    
    # 检查系统路由表冲突
    local existing_routes
    existing_routes=$(ip -4 route 2>/dev/null | awk '{print $1}' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$' || true)
    for route in $existing_routes; do
        if [[ "$route" == "$candidate_subnet" ]]; then
            return 1  # 与系统路由完全匹配
        fi
        # 简单的前缀检查（如果没有ipcalc）
        local route_prefix route_suffix
        route_prefix="${route%/*}"
        route_suffix="${route#*/}"
        local cand_prefix cand_suffix
        cand_prefix="${candidate_subnet%/*}"
        cand_suffix="${candidate_subnet#*/}"
        
        # 如果前缀相同且掩码不同，可能有包含关系
        if [[ "$route_prefix" == "$cand_prefix" && "$route_suffix" != "$cand_suffix" ]]; then
            return 1
        fi
    done
    
    return 0  # 无冲突
}

# 检查IPv6子网冲突
check_ipv6_subnet_conflicts() {
    local candidate_subnet="$1"
    
    [[ -z "$candidate_subnet" ]] && return 0
    
    # 获取现有Docker网络信息
    local existing_networks
    existing_networks=$(docker network ls --format '{{.Name}}' | grep -v "^bridge$\|^host$\|^none$" | head -20)
    
    # 检查Docker网络IPv6子网冲突
    for network in $existing_networks; do
        local network_subnets
        network_subnets=$(docker network inspect "$network" --format '{{range .IPAM.Config}}{{.Subnet}} {{end}}' 2>/dev/null || true)
        for subnet in $network_subnets; do
            # 只处理IPv6地址
            if [[ "$subnet" =~ : ]]; then
                if [[ "$subnet" == "$candidate_subnet" ]]; then
                    return 1  # 完全匹配冲突
                fi
                # IPv6前缀包含检查 (简化版本)
                local subnet_prefix subnet_len
                subnet_prefix="${subnet%::*}"
                subnet_len="${subnet##*/}"
                local cand_prefix cand_len
                cand_prefix="${candidate_subnet%::*}"
                cand_len="${candidate_subnet##*/}"
                
                # 如果前缀相同，检查长度
                if [[ "$subnet_prefix" == "$cand_prefix" ]]; then
                    return 1  # 相同前缀可能冲突
                fi
            fi
        done
    done
    
    # 检查系统IPv6路由表冲突
    local existing_routes
    existing_routes=$(ip -6 route 2>/dev/null | awk '{print $1}' | grep ':' || true)
    for route in $existing_routes; do
        if [[ "$route" == "$candidate_subnet" ]]; then
            return 1  # 与系统路由完全匹配
        fi
        # 简单的IPv6前缀检查
        local route_prefix="${route%::*}"
        local cand_prefix="${candidate_subnet%::*}"
        if [[ "$route_prefix" == "$cand_prefix" ]]; then
            return 1  # 可能的前缀冲突
        fi
    done
    
    return 0  # 无冲突
}

# 检查网络冲突 (IPv4和IPv6) - 主检查函数
# 参数: $1=IPv4子网 $2=IPv6子网
check_network_conflicts() {
    local ipv4_subnet="$1"
    local ipv6_subnet="$2"
    
    # 检查IPv4子网冲突
    if ! check_ipv4_subnet_conflicts "$ipv4_subnet"; then
        return 1  # IPv4冲突
    fi
    
    # 检查IPv6子网冲突
    if ! check_ipv6_subnet_conflicts "$ipv6_subnet"; then
        return 1  # IPv6冲突
    fi
    
    return 0  # 无冲突
}

# 查找可用的子网范围
find_available_subnet() {
    # IPv4候选子网池 - 分优先级
    local ipv4_primary=(
        "**********/16"
        "**********/16"
        "**********/16"
        "**********/16"
    )
    
    local ipv4_secondary=(
        "**********/16"
        "**********/16"
        "**********/16"
        "**********/16"
    )
    
    local ipv4_fallback=(
        "*************/24"
        "*************/24"
        "*************/24"
        "*************/24"
    )
    
    # 合并所有IPv4候选
    local ipv4_candidates=("${ipv4_primary[@]}" "${ipv4_secondary[@]}" "${ipv4_fallback[@]}")
    
    # IPv6候选子网池 - 预定义ULA (使用有效的十六进制)
    local ipv6_primary=(
        "fd00:dead:beef::/48"
        "fd00:cafe:babe::/48"
        "fd00:bad:c0de::/48" 
        "fd01:acdc:feed::/48"
    )
    
    local ipv6_secondary=(
        "fd02:ab12:cd34::/48"
        "fd03:ef56:ab78::/48"
        "fd04:1234:5678::/48"
        "fd05:9abc:def0::/48"
    )
    
    # 合并所有IPv6候选
    local ipv6_candidates=("${ipv6_primary[@]}" "${ipv6_secondary[@]}")
    
    # 第一轮：尝试主要候选组合
    for ipv4 in "${ipv4_primary[@]}"; do
        for ipv6 in "${ipv6_primary[@]}"; do
            if check_network_conflicts "$ipv4" "$ipv6"; then
                echo "$ipv4 $ipv6"
                return 0
            fi
        done
    done
    
    # 第二轮：尝试所有固定候选组合
    for ipv4 in "${ipv4_candidates[@]}"; do
        for ipv6 in "${ipv6_candidates[@]}"; do
            if check_network_conflicts "$ipv4" "$ipv6"; then
                echo "$ipv4 $ipv6"
                return 0
            fi
        done
    done
    
    # 第三轮：使用随机ULA与固定IPv4组合
    for ipv4 in "${ipv4_candidates[@]}"; do
        for i in {1..10}; do  # 增加尝试次数
            local random_ipv6
            random_ipv6=$(generate_random_ula)
            if check_network_conflicts "$ipv4" "$random_ipv6"; then
                echo "$ipv4 $random_ipv6"
                return 0
            fi
        done
    done
    
    # 第四轮：纯随机方式（最后的兜底方案）
    for i in {1..20}; do
        local random_ipv4 random_ipv6
        # 生成随机的10.x.0.0/16网段
        local random_second=$((RANDOM % 50 + 200))  # 10.200-249.0.0/16
        random_ipv4="10.${random_second}.0.0/16"
        random_ipv6=$(generate_random_ula)
        
        if check_network_conflicts "$random_ipv4" "$random_ipv6"; then
            echo "$random_ipv4 $random_ipv6"
            return 0
        fi
    done
    
    # 都失败，返回空值让Docker自动分配
    echo "" ""
}

# 查找可用的IPv4子网 (单独使用)
find_available_ipv4_subnet() {
    local ipv4_candidates=(
        "**********/16" "**********/16" "**********/16" "**********/16"
        "**********/16" "**********/16" "**********/16" "**********/16"
        "*************/24" "*************/24" "*************/24"
    )
    
    for ipv4 in "${ipv4_candidates[@]}"; do
        if check_ipv4_subnet_conflicts "$ipv4"; then
            echo "$ipv4"
            return 0
        fi
    done
    
    # 尝试随机生成
    for i in {1..10}; do
        local random_second=$((RANDOM % 50 + 200))
        local random_ipv4="10.${random_second}.0.0/16"
        if check_ipv4_subnet_conflicts "$random_ipv4"; then
            echo "$random_ipv4"
            return 0
        fi
    done
    
    echo ""  # 无可用子网
}

# 查找可用的IPv6子网 (单独使用)  
find_available_ipv6_subnet() {
    local ipv6_candidates=(
        "fd00:dstatus:beta::/48" "fd00:dstatus:app::/48" 
        "fd00:docker:beta::/48" "fd01:dstatus:net::/48"
        "fd02:dstatus:prod::/48" "fd03:dstatus:dev::/48"
    )
    
    for ipv6 in "${ipv6_candidates[@]}"; do
        if check_ipv6_subnet_conflicts "$ipv6"; then
            echo "$ipv6"
            return 0
        fi
    done
    
    # 尝试随机ULA
    for i in {1..15}; do
        local random_ipv6
        random_ipv6=$(generate_random_ula)
        if check_ipv6_subnet_conflicts "$random_ipv6"; then
            echo "$random_ipv6"
            return 0
        fi
    done
    
    echo ""  # 无可用子网
}

# 生成唯一网络名
generate_unique_network_name() {
    local base_name="${1:-dstatus_beta}"
    
    # 使用更精确的时间戳 (微秒级别)
    local timestamp=$(date +%s%N | cut -c1-16)  # 16位数字：秒+微秒前6位
    local network_name="${base_name}_${timestamp}"
    
    # 如果网络名已存在，添加随机后缀
    local attempt=0
    while check_network_name_conflicts "$network_name"; do
        attempt=$((attempt + 1))
        if [[ $attempt -gt 5 ]]; then
            # 使用更随机的方法
            local random_suffix=$(openssl rand -hex 4)
            network_name="${base_name}_${timestamp}_${random_suffix}"
            if ! check_network_name_conflicts "$network_name"; then
                break
            fi
            # 如果还是冲突，再试一次完全随机的名字
            network_name="${base_name}_$(openssl rand -hex 8)"
            break
        fi
        # 添加小的随机延迟避免在循环中产生相同时间戳
        sleep 0.001 2>/dev/null || sleep 1
        timestamp=$(date +%s%N | cut -c1-16)
        network_name="${base_name}_${timestamp}_${attempt}"
    done
    
    echo "$network_name"
}

# 清理旧的DStatus网络
cleanup_old_networks() {
    echo "正在清理可能存在的旧DStatus网络..."
    
    # 查找所有以dstatus开头的网络
    local old_networks
    old_networks=$(docker network ls --format "{{.Name}}" | grep "^dstatus" || true)
    
    for network in $old_networks; do
        # 检查网络是否正在被使用
        local container_count
        container_count=$(docker network inspect "$network" --format '{{len .Containers}}' 2>/dev/null || echo "0")
        
        if [[ "$container_count" -eq 0 ]]; then
            echo "删除未使用的网络: $network"
            docker network rm "$network" 2>/dev/null || true
        else
            echo "保留正在使用的网络: $network (容器数: $container_count)"
        fi
    done
}

# 输出函数 - 检查IPv6支持状态
check_ipv6_support_status() {
    echo "=== IPv6支持状态检查 ==="
    
    if detect_system_ipv6; then
        echo "✓ 系统IPv6支持: 已启用"
    else
        echo "✗ 系统IPv6支持: 未启用或不可用"
        return 1
    fi
    
    if detect_docker_ipv6; then
        echo "✓ Docker IPv6支持: 已启用"
    else
        echo "✗ Docker IPv6支持: 未启用"
        return 1
    fi
    
    echo "✓ IPv6环境检查通过"
    return 0
}