#!/bin/bash
# ============================================================
# integration-test.sh - 网络配置集成测试
# ============================================================
# 模拟不同环境下的网络配置测试
# 
# 使用方式:
#   ./integration-test.sh [test-scenario]
# ============================================================

set -euo pipefail

# 加载模块
SCRIPT_DIR="$(dirname "${BASH_SOURCE[0]}")"
source "$SCRIPT_DIR/net-utils.sh"
source "$SCRIPT_DIR/net-ipv6.sh"

# 测试配置
TEST_COMPOSE_DIR="/tmp/dstatus-integration-test"
TEST_COMPOSE_FILE="$TEST_COMPOSE_DIR/docker-compose.yml"

# 测试日志函数
test_log() { echo "[INTEGRATION] $*"; }
test_success() { echo "[✓] $*"; }
test_fail() { echo "[✗] $*"; exit 1; }

# 清理测试环境
cleanup_test_env() {
    test_log "清理测试环境..."
    rm -rf "$TEST_COMPOSE_DIR"
    
    # 清理测试网络（如果存在）
    local test_networks=$(docker network ls --format "{{.Name}}" | grep "test_dstatus" || true)
    for network in $test_networks; do
        docker network rm "$network" 2>/dev/null || true
    done
}

# 准备测试环境
prepare_test_env() {
    cleanup_test_env
    mkdir -p "$TEST_COMPOSE_DIR"
    
    # 创建基础compose文件
    cat > "$TEST_COMPOSE_FILE" << 'EOF'
version: '3.8'
services:
  dstatus-beta-monitor:
    image: nginx:alpine
    container_name: dstatus-beta-monitor
    ports:
      - "5555:80"
    environment:
      - BETA_VERSION=true
EOF
}

# 测试场景1: IPv4-only环境
test_ipv4_only() {
    test_log "测试场景1: IPv4-only环境"
    prepare_test_env
    
    # 模拟IPv4-only配置
    configure_network_with_modules "$TEST_COMPOSE_FILE" "false" "false"
    
    # 验证配置
    if grep -q "enable_ipv6: true" "$TEST_COMPOSE_FILE"; then
        test_fail "IPv4-only模式不应启用IPv6"
    fi
    
    if ! grep -q "networks:" "$TEST_COMPOSE_FILE"; then
        test_fail "IPv4-only模式应创建自定义网络"
    fi
    
    test_success "IPv4-only环境测试通过"
}

# 测试场景2: IPv6强制启用
test_ipv6_forced() {
    test_log "测试场景2: IPv6强制启用"
    prepare_test_env
    
    # 强制启用IPv6（即使环境不支持）
    configure_network_with_modules "$TEST_COMPOSE_FILE" "true" "false" || {
        test_log "IPv6强制启用失败（预期，因为测试环境可能不支持IPv6）"
        return 0
    }
    
    test_success "IPv6强制启用测试通过"
}

# 测试场景3: 自动检测模式
test_auto_detection() {
    test_log "测试场景3: 自动检测模式"
    prepare_test_env
    
    # 自动检测IPv6支持
    configure_network_with_modules "$TEST_COMPOSE_FILE" "auto" "false"
    
    # 验证配置文件生成
    if ! grep -q "networks:" "$TEST_COMPOSE_FILE"; then
        test_fail "自动检测模式应创建网络配置"
    fi
    
    # 检查是否正确回退到IPv4
    if ! grep -q "name: test_dstatus" "$TEST_COMPOSE_FILE"; then
        test_log "检查生成的网络名..."
        grep "name:" "$TEST_COMPOSE_FILE" || test_fail "未找到网络名配置"
    fi
    
    test_success "自动检测模式测试通过"
}

# 测试场景4: 网络冲突处理
test_conflict_handling() {
    test_log "测试场景4: 网络冲突处理"
    prepare_test_env
    
    # 创建冲突的Docker网络
    docker network create --subnet=**********/16 test_conflict_network 2>/dev/null || true
    
    # 测试冲突处理
    local subnet_result
    subnet_result=$(find_available_subnet)
    local ipv4_subnet=$(echo "$subnet_result" | awk '{print $1}')
    
    if [[ "$ipv4_subnet" == "**********/16" ]]; then
        test_fail "冲突检测失败：仍然选择了冲突的子网"
    fi
    
    # 清理测试网络
    docker network rm test_conflict_network 2>/dev/null || true
    
    test_success "网络冲突处理测试通过 (选择了: $ipv4_subnet)"
}

# 测试场景5: Routed模式配置
test_routed_mode() {
    test_log "测试场景5: Routed模式配置"
    prepare_test_env
    
    # 测试routed模式（可能失败，取决于Docker版本）
    configure_network_with_modules "$TEST_COMPOSE_FILE" "auto" "true" 2>/dev/null || {
        test_log "Routed模式配置失败（可能Docker版本不支持）"
        return 0
    }
    
    # 检查routed模式配置
    if grep -q "gateway_mode_ipv6.*routed" "$TEST_COMPOSE_FILE"; then
        test_success "Routed模式配置测试通过"
    else
        test_log "Routed模式配置未生效（环境限制）"
    fi
}

# 测试场景6: 完整安装流程模拟
test_full_install_simulation() {
    test_log "测试场景6: 完整安装流程模拟"
    prepare_test_env
    
    # 模拟完整的网络配置流程
    local old_enable_ipv6="${ENABLE_IPV6:-auto}"
    local old_use_routed="${USE_ROUTED_MODE:-false}"
    local old_network_debug="${NETWORK_DEBUG:-false}"
    
    # 设置测试参数
    ENABLE_IPV6="auto"
    USE_ROUTED_MODE="false"
    NETWORK_DEBUG="true"
    
    # 模拟install-beta.sh中的configure_docker_network调用
    cd "$TEST_COMPOSE_DIR"
    
    # 这里我们需要模拟主脚本的环境
    local compose_file="docker-compose.yml"
    
    # 启用网络调试模式
    if [[ "$NETWORK_DEBUG" == "true" ]]; then
        test_log "网络调试模式已启用"
        test_log "IPv6模式: $ENABLE_IPV6"
        test_log "Routed模式: $USE_ROUTED_MODE"
        
        # 显示IPv6支持状态
        check_ipv6_support_status || true
    fi
    
    # 使用新的模块化网络配置
    configure_network_with_modules "$compose_file" "$ENABLE_IPV6" "$USE_ROUTED_MODE"
    
    # 验证生成的配置
    if [[ ! -f "$compose_file" ]]; then
        test_fail "配置文件未生成"
    fi
    
    if ! grep -q "dstatus-beta-monitor" "$compose_file"; then
        test_fail "服务配置丢失"
    fi
    
    if ! grep -q "networks:" "$compose_file"; then
        test_fail "网络配置未生成"
    fi
    
    # 恢复原始参数
    ENABLE_IPV6="$old_enable_ipv6"
    USE_ROUTED_MODE="$old_use_routed"
    NETWORK_DEBUG="$old_network_debug"
    
    test_success "完整安装流程模拟测试通过"
}

# 运行所有测试
run_all_integration_tests() {
    echo "======================================"
    echo "网络配置集成测试"
    echo "======================================"
    echo
    
    test_ipv4_only
    echo
    test_ipv6_forced
    echo
    test_auto_detection
    echo
    test_conflict_handling
    echo
    test_routed_mode
    echo
    test_full_install_simulation
    
    echo
    echo "======================================"
    echo "集成测试完成"
    echo "======================================"
    
    cleanup_test_env
}

# 运行单个测试
run_single_integration_test() {
    case "$1" in
        "ipv4"|"ipv4-only")
            test_ipv4_only
            ;;
        "ipv6"|"ipv6-forced")
            test_ipv6_forced
            ;;
        "auto"|"detection")
            test_auto_detection
            ;;
        "conflict"|"conflicts")
            test_conflict_handling
            ;;
        "routed"|"routed-mode")
            test_routed_mode
            ;;
        "full"|"install")
            test_full_install_simulation
            ;;
        *)
            echo "未知测试场景: $1"
            echo "可用场景: ipv4, ipv6, auto, conflict, routed, full"
            exit 1
            ;;
    esac
    
    cleanup_test_env
}

# 主程序
main() {
    # 检查Docker是否可用
    if ! command -v docker >/dev/null 2>&1; then
        echo "错误: Docker未安装或不可用"
        exit 1
    fi
    
    if [[ $# -eq 0 ]]; then
        run_all_integration_tests
    else
        run_single_integration_test "$1"
    fi
}

# 设置清理钩子
trap cleanup_test_env EXIT

# 检查是否直接运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi