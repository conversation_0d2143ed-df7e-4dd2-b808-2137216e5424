# Beta V6 安装脚本套件

本目录包含 DStatus Beta V6 版本的自动化安装脚本及相关模块。专门为测试版本设计，支持与正式版本并存。

## 📁 文件结构

```
beta-v6/
├── install-beta.sh          # 主安装脚本 (34KB)
├── README.md               # 功能说明文档
└── lib/                    # 模块库目录
    ├── net-utils.sh        # 基础网络工具模块 (13KB)
    ├── net-ipv6.sh         # IPv6专用配置模块 (8KB)
    ├── test-network-modules.sh    # 单元测试脚本 (7KB)
    └── integration-test.sh # 集成测试脚本 (8KB)
```

## 🚀 主要功能

### 主安装脚本 (install-beta.sh)

#### 核心功能
- **Docker 一键部署**: 自动检测并安装 Docker 环境
- **多版本管理**: 支持指定特定 Beta 版本或使用最新版本
- **数据备份**: 更新/重装时自动备份现有数据
- **端口配置**: 默认使用 5555 端口，避免与正式版冲突
- **许可证集成**: 支持在安装时配置许可证密钥

#### 网络配置特性
- **IPv6 自动检测**: 智能检测系统和 Docker 的 IPv6 支持
- **三种网络模式**:
  - `auto`: 自动检测最佳配置（默认）
  - `true`: 强制启用 IPv6
  - `false`: 完全禁用 IPv6
- **Routed 模式**: 支持 Docker v27+ 的高级 IPv6 路由模式
- **子网冲突检测**: 自动避免与现有 Docker 网络的 IP 冲突

#### 安装选项
```bash
--method METHOD        # 安装方式: docker (默认)
--license-key KEY      # 许可证密钥
--port PORT           # 服务端口 (默认: 5555)
--install-dir DIR     # 安装目录 (默认: /opt/dstatus-beta)
--beta-version VER    # Beta版本 (默认: latest)
--force               # 强制重新安装
--update              # 更新模式
--uninstall           # 完全卸载
--ipv6 MODE          # IPv6模式: auto/true/false
--routed-mode        # 启用IPv6 routed模式
--network-debug      # 启用网络调试
-y, --yes            # 跳过确认提示
```

### 🔧 网络工具模块 (lib/net-utils.sh)

#### 基础网络功能
- **系统 IPv6 检测**: 检查内核模块、sysctl 配置和全局地址
- **Docker IPv6 检测**: 通过多种方式验证 Docker 的 IPv6 支持
- **ULA 地址生成**: 生成符合 RFC 4193 的唯一本地地址
- **网络冲突检测**: 
  - 检查网络名称冲突
  - 检查 IPv4 子网冲突
  - 检查 IPv6 子网冲突
- **智能子网选择**: 自动查找可用的 IPv4/IPv6 子网

### 🌐 IPv6 配置模块 (lib/net-ipv6.sh)

#### IPv6 专用功能
- **完整性检测**: 同时验证系统和 Docker 的 IPv6 支持
- **Compose 配置生成**: 自动生成 Docker Compose 的 IPv6 网络配置
- **多模式支持**:
  - NAT 模式（默认）
  - Routed 模式（需要 Docker v27+）
  - 仅 IPv6 模式
- **Docker 守护进程配置**: 自动配置 /etc/docker/daemon.json
- **网络创建**: 创建支持双栈的 Docker 网络

### 🧪 测试脚本

#### 单元测试 (test-network-modules.sh)
- **IPv6 支持检测测试**: 验证检测功能的准确性
- **冲突检测测试**: 测试子网和网络名冲突检测
- **子网选择测试**: 验证自动子网选择算法
- **ULA 生成测试**: 验证 ULA 地址格式正确性
- **Docker 配置测试**: 测试 Docker 守护进程配置功能

#### 集成测试 (integration-test.sh)
- **场景1**: IPv4-only 环境测试
- **场景2**: IPv6 强制启用测试
- **场景3**: 自动检测模式测试
- **场景4**: Routed 模式测试
- **场景5**: 网络冲突处理测试
- **场景6**: 完整安装流程测试

## 📋 使用示例

### 基础安装
```bash
# 使用默认配置安装
./install-beta.sh --license-key="YOUR_KEY" -y

# 指定端口安装
./install-beta.sh --license-key="YOUR_KEY" --port=8080 -y

# 指定版本安装
./install-beta.sh --license-key="YOUR_KEY" --beta-version=14 -y
```

### IPv6 配置
```bash
# 强制启用 IPv6
./install-beta.sh --license-key="YOUR_KEY" --ipv6=true -y

# 使用 Routed 模式 (Docker v27+)
./install-beta.sh --license-key="YOUR_KEY" --ipv6=true --routed-mode -y

# 禁用 IPv6
./install-beta.sh --license-key="YOUR_KEY" --no-ipv6 -y
```

### 维护操作
```bash
# 更新到最新版本
./install-beta.sh --update

# 强制重新安装（自动备份数据）
./install-beta.sh --force --license-key="YOUR_KEY" -y

# 完全卸载
./install-beta.sh --uninstall
```

### 调试模式
```bash
# 启用网络调试信息
./install-beta.sh --license-key="YOUR_KEY" --network-debug -y
```

## 🧪 运行测试

### 运行单元测试
```bash
cd scripts/beta-v6/lib
./test-network-modules.sh all
```

### 运行集成测试
```bash
cd scripts/beta-v6/lib
./integration-test.sh all
```

### 测试特定功能
```bash
# 测试 IPv6 检测
./test-network-modules.sh ipv6_detection

# 测试冲突检测
./test-network-modules.sh conflict_detection

# 测试特定场景
./integration-test.sh ipv4_only
./integration-test.sh ipv6_forced
```

## ⚠️ 注意事项

1. **测试版本警告**: 这是内部测试版本，可能包含未经充分测试的新功能
2. **生产环境**: 不建议在生产环境中使用 Beta 版本
3. **数据备份**: 虽然脚本会自动备份，但建议手动备份重要数据
4. **Docker 版本**: Routed 模式需要 Docker v27.0.0 或更高版本
5. **网络权限**: IPv6 配置可能需要额外的系统权限
6. **反馈渠道**: 发现问题请标注 [BETA] 标签反馈

## 📊 环境要求

- **操作系统**: Linux (Ubuntu/Debian/CentOS/RHEL)
- **Docker**: 20.10.0+ (Routed 模式需要 27.0.0+)
- **内存**: 最少 1GB 可用内存
- **磁盘**: 最少 2GB 可用空间
- **网络**: IPv4 必需，IPv6 可选但推荐
- **权限**: root 或 sudo 权限

## 🔗 相关链接

- 主项目仓库: [DStatus](https://github.com/dingzier/dstatus)
- 正式版安装: https://down.vps.mom/scripts/install.sh
- Beta 版下载: https://down.vps.mom/downloads/beta/
- 问题反馈: 请在 Issue 中标注 [BETA] 标签