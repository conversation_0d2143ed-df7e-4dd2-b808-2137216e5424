#!/bin/bash

# DStatus Beta 测试版一键安装脚本
# 支持 Docker 和传统安装方式
# ⚠️ 此脚本用于安装测试版本，仅供内部测试使用

set -euo pipefail

# 确保使用bash运行
if [ -z "$BASH_VERSION" ]; then
    echo "错误: 此脚本需要使用bash运行"
    echo "请使用: bash <(curl -fsSL https://down.vps.mom/downloads/beta/install-beta.sh)"
    exit 1
fi

# 为了最大兼容性，移除所有颜色支持

# 配置
DOWNLOAD_BASE="https://down.vps.mom/downloads/beta"
INSTALL_DIR="/opt/dstatus-beta"

# 默认参数
LICENSE_KEY=""
PORT="5555"  
FORCE_INSTALL=false
UPDATE_MODE=false
AUTO_INSTALL_DOCKER=true
BETA_VERSION="latest"
YES_TO_ALL=false
UNINSTALL=false

# 显示帮助信息
show_help() {
    echo "DStatus Beta 测试版一键安装脚本"
    echo ""
    echo "⚠️  这是内部测试版本，仅供开发和测试使用！"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --method METHOD        安装方式: docker (默认且唯一支持)"
    echo "  --license-key KEY      许可证密钥 (安装时必需)"
    echo "  --port PORT           服务端口 (默认: 5555)"
    echo "  --install-dir DIR     安装目录 (默认: /opt/dstatus-beta)"
    echo "  --beta-version VER    Beta版本 (默认: latest)"
    echo "  --force               强制重新安装 (自动备份数据后覆盖安装)"
    echo "  --update              更新模式 (自动备份数据后在线更新)"
    echo "  --uninstall           完全卸载 (彻底删除容器、镜像、数据和程序)"
    echo "  --skip-docker         跳过 Docker 自动安装"
    echo "  --no-auto-docker      禁用 Docker 自动安装"
    echo "  -y, --yes             跳过所有确认提示，自动安装"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "安装示例:"
    echo "  $0 --license-key=\"YOUR_LICENSE_KEY\" --yes"
    echo "  $0 --license-key=YOUR_LICENSE_KEY --port=8080 --yes"
    echo "  $0 --license-key=YOUR_LICENSE_KEY --beta-version=14 --yes"
    echo ""
    echo "Beta版本特性:"
    echo "  - 使用独立的安装目录和服务名"
    echo "  - 默认端口5555，避免与正式版本冲突"
    echo "  - 支持与正式版本并存安装"
    echo "  - 包含最新的实验性功能"
    echo "  - 可能存在不稳定问题"
    echo ""
    echo "更新示例:"
    echo "  $0 --update"
    echo "  $0 --update --install-dir=/custom/path"
    echo ""
    echo "注意事项:"
    echo "  - 测试版本仅供开发和测试使用"
    echo "  - 不建议在生产环境中使用"
    echo "  - 可能包含未经充分测试的新功能"
    echo "  - 反馈问题请标注 [BETA] 标签"
}

# 简化的日志函数 - 纯文本输出
log_info() {
    echo "[BETA-INFO] $1"
}

log_success() {
    echo "[BETA-SUCCESS] $1"
}

log_warning() {
    echo "[BETA-WARNING] $1"
}

log_error() {
    echo "[BETA-ERROR] $1"
}

# ============================================================
# IPv6 网络检测增强功能 - 从 beta-v6 版本移植
# ============================================================

# 检查系统级IPv6支持
detect_system_ipv6() {
    # 检查内核IPv6模块文件是否存在
    [[ -f /proc/net/if_inet6 ]] || return 1
    
    # 检查IPv6是否被禁用
    local disabled=$(sysctl -n net.ipv6.conf.all.disable_ipv6 2>/dev/null || echo 1)
    [[ $disabled -eq 0 ]] || return 1
    
    # 检查是否有IPv6地址 - 先检查最基本的loopback
    if ip -6 addr show lo 2>/dev/null | grep -q '::1'; then
        return 0
    fi
    
    # 检查是否有任何IPv6地址
    if ip -6 addr show 2>/dev/null | grep -q 'inet6'; then
        return 0
    fi
    
    return 1
}

# 检查Docker守护进程IPv6支持
detect_docker_ipv6() {
    # 方法1: 使用官方Docker info API
    if docker info --format '{{.IPv6}}' 2>/dev/null | grep -q '^true$'; then
        return 0
    fi
    
    # 方法2: 检查daemon.json配置
    if [[ -f /etc/docker/daemon.json ]] && grep -q '"ipv6":[[:space:]]*true' /etc/docker/daemon.json 2>/dev/null; then
        return 0
    fi
    
    # 方法3: 检查默认bridge网络是否有IPv6配置
    if docker network inspect bridge --format '{{range .IPAM.Config}}{{.Subnet}}{{end}}' 2>/dev/null | grep -q ':'; then
        return 0
    fi
    
    return 1
}

# 检测完整的IPv6支持状态
detect_full_ipv6_support() {
    if detect_system_ipv6 && detect_docker_ipv6; then
        return 0
    else
        return 1
    fi
}

# 生成随机ULA (Unique Local Address)
generate_random_ula() {
    # 生成40-bit全局ID (RFC 4193)
    local global_id=$(openssl rand -hex 5)
    # 格式化为 fdXX:XXXX:XXXX::/48
    printf "fd%s:%s:%s::/48" \
        "${global_id:0:2}" \
        "${global_id:2:4}" \
        "${global_id:6:4}"
}

# 检查网络名冲突
check_network_name_conflicts() {
    local network_name="$1"
    docker network inspect "$network_name" >/dev/null 2>&1
}

# 检查IPv4子网冲突
check_ipv4_subnet_conflicts() {
    local candidate_subnet="$1"
    
    [[ -z "$candidate_subnet" ]] && return 0
    
    # 获取现有Docker网络信息
    local existing_networks
    existing_networks=$(docker network ls --format '{{.Name}}' | grep -v "^bridge$\|^host$\|^none$" | head -20)
    
    # 检查Docker网络子网冲突
    for network in $existing_networks; do
        local network_subnets
        network_subnets=$(docker network inspect "$network" --format '{{range .IPAM.Config}}{{.Subnet}} {{end}}' 2>/dev/null || true)
        for subnet in $network_subnets; do
            # 排除IPv6地址
            if [[ ! "$subnet" =~ : ]]; then
                if [[ "$subnet" == "$candidate_subnet" ]]; then
                    return 1  # 完全匹配冲突
                fi
                # 如果有ipcalc可用，进行更精确的包含关系检查
                if command -v ipcalc >/dev/null 2>&1; then
                    if ipcalc -c "$candidate_subnet" "$subnet" 2>/dev/null || \
                       ipcalc -c "$subnet" "$candidate_subnet" 2>/dev/null; then
                        return 1  # 子网包含冲突
                    fi
                fi
            fi
        done
    done
    
    # 检查系统路由表冲突
    local existing_routes
    existing_routes=$(ip -4 route 2>/dev/null | awk '{print $1}' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$' || true)
    for route in $existing_routes; do
        if [[ "$route" == "$candidate_subnet" ]]; then
            return 1  # 与系统路由完全匹配
        fi
        # 简单的前缀检查（如果没有ipcalc）
        local route_prefix route_suffix
        route_prefix="${route%/*}"
        route_suffix="${route#*/}"
        local cand_prefix cand_suffix
        cand_prefix="${candidate_subnet%/*}"
        cand_suffix="${candidate_subnet#*/}"
        
        # 如果前缀相同且掩码不同，可能有包含关系
        if [[ "$route_prefix" == "$cand_prefix" && "$route_suffix" != "$cand_suffix" ]]; then
            return 1
        fi
    done
    
    return 0  # 无冲突
}

# 检查IPv6子网冲突
check_ipv6_subnet_conflicts() {
    local candidate_subnet="$1"
    
    [[ -z "$candidate_subnet" ]] && return 0
    
    # 获取现有Docker网络信息
    local existing_networks
    existing_networks=$(docker network ls --format '{{.Name}}' | grep -v "^bridge$\|^host$\|^none$" | head -20)
    
    # 检查Docker网络IPv6子网冲突
    for network in $existing_networks; do
        local network_subnets
        network_subnets=$(docker network inspect "$network" --format '{{range .IPAM.Config}}{{.Subnet}} {{end}}' 2>/dev/null || true)
        for subnet in $network_subnets; do
            # 只处理IPv6地址
            if [[ "$subnet" =~ : ]]; then
                if [[ "$subnet" == "$candidate_subnet" ]]; then
                    return 1  # 完全匹配冲突
                fi
                # IPv6前缀包含检查 (简化版本)
                local subnet_prefix subnet_len
                subnet_prefix="${subnet%::*}"
                subnet_len="${subnet##*/}"
                local cand_prefix cand_len
                cand_prefix="${candidate_subnet%::*}"
                cand_len="${candidate_subnet##*/}"
                
                # 如果前缀相同，检查长度
                if [[ "$subnet_prefix" == "$cand_prefix" ]]; then
                    return 1  # 相同前缀可能冲突
                fi
            fi
        done
    done
    
    # 检查系统IPv6路由表冲突
    local existing_routes
    existing_routes=$(ip -6 route 2>/dev/null | awk '{print $1}' | grep ':' || true)
    for route in $existing_routes; do
        if [[ "$route" == "$candidate_subnet" ]]; then
            return 1  # 与系统路由完全匹配
        fi
        # 简单的IPv6前缀检查
        local route_prefix="${route%::*}"
        local cand_prefix="${candidate_subnet%::*}"
        if [[ "$route_prefix" == "$cand_prefix" ]]; then
            return 1  # 可能的前缀冲突
        fi
    done
    
    return 0  # 无冲突
}

# 检查网络冲突 (IPv4和IPv6) - 主检查函数
# 参数: $1=IPv4子网 $2=IPv6子网
check_network_conflicts() {
    local ipv4_subnet="$1"
    local ipv6_subnet="$2"
    
    # 检查IPv4子网冲突
    if ! check_ipv4_subnet_conflicts "$ipv4_subnet"; then
        return 1  # IPv4冲突
    fi
    
    # 检查IPv6子网冲突
    if ! check_ipv6_subnet_conflicts "$ipv6_subnet"; then
        return 1  # IPv6冲突
    fi
    
    return 0  # 无冲突
}

# 查找可用的IPv4子网 (单独使用)
find_available_ipv4_subnet() {
    local ipv4_candidates=(
        "**********/16" "**********/16" "**********/16" "**********/16"
        "**********/16" "**********/16" "**********/16" "**********/16"
        "*************/24" "*************/24" "*************/24"
    )
    
    for ipv4 in "${ipv4_candidates[@]}"; do
        if check_ipv4_subnet_conflicts "$ipv4"; then
            echo "$ipv4"
            return 0
        fi
    done
    
    # 尝试随机生成
    for i in {1..10}; do
        local random_second=$((RANDOM % 50 + 200))
        local random_ipv4="10.${random_second}.0.0/16"
        if check_ipv4_subnet_conflicts "$random_ipv4"; then
            echo "$random_ipv4"
            return 0
        fi
    done
    
    echo ""  # 无可用子网
}

# 查找可用的IPv6子网 (单独使用) - 修复格式
find_available_ipv6_subnet() {
    local ipv6_candidates=(
        "fd00:dead:beef::/48" "fd00:cafe:babe::/48" 
        "fd00:bad:c0de::/48" "fd01:acdc:feed::/48"
        "fd02:ab12:cd34::/48" "fd03:ef56:ab78::/48"
        "fd04:1234:5678::/48" "fd05:9abc:def0::/48"
    )
    
    for ipv6 in "${ipv6_candidates[@]}"; do
        if check_ipv6_subnet_conflicts "$ipv6"; then
            echo "$ipv6"
            return 0
        fi
    done
    
    # 尝试随机ULA
    for i in {1..15}; do
        local random_ipv6
        random_ipv6=$(generate_random_ula)
        if check_ipv6_subnet_conflicts "$random_ipv6"; then
            echo "$random_ipv6"
            return 0
        fi
    done
    
    echo ""  # 无可用子网
}

# 生成唯一网络名
generate_unique_network_name() {
    local base_name="${1:-dstatus_beta}"
    
    # 使用更精确的时间戳 (微秒级别)
    local timestamp=$(date +%s%N | cut -c1-16)  # 16位数字：秒+微秒前6位
    local network_name="${base_name}_${timestamp}"
    
    # 如果网络名已存在，添加随机后缀
    local attempt=0
    while check_network_name_conflicts "$network_name"; do
        attempt=$((attempt + 1))
        if [[ $attempt -gt 5 ]]; then
            # 使用更随机的方法
            local random_suffix=$(openssl rand -hex 4)
            network_name="${base_name}_${timestamp}_${random_suffix}"
            if ! check_network_name_conflicts "$network_name"; then
                break
            fi
            # 如果还是冲突，再试一次完全随机的名字
            network_name="${base_name}_$(openssl rand -hex 8)"
            break
        fi
        # 添加小的随机延迟避免在循环中产生相同时间戳
        sleep 0.001 2>/dev/null || sleep 1
        timestamp=$(date +%s%N | cut -c1-16)
        network_name="${base_name}_${timestamp}_${attempt}"
    done
    
    echo "$network_name"
}

# 确保服务使用指定网络
attach_service_to_network() {
    local compose_file="$1"
    local network_name="$2"
    local service_name="${3:-dstatus-beta-monitor}"
    
    # 检查服务是否已有networks配置
    if ! grep -A5 "container_name: $service_name" "$compose_file" | grep -q 'networks:'; then
        # 在container_name行后添加networks配置
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' "/container_name: $service_name/a\\
    networks:\\
      - default" "$compose_file"
        else
            sed -i "/container_name: $service_name/a\\
    networks:\\
      - default" "$compose_file"
        fi
    fi
}

# ============================================================

# 统一镜像清理函数
clean_beta_images() {
    log_info "清理旧 Beta 镜像..."
    local old_images=$(docker images --format "{{.Repository}}:{{.Tag}}" --filter reference='*dstatus-docker-beta*' 2>/dev/null || true)
    if [[ -n "$old_images" ]]; then
        echo "$old_images" | xargs -r docker rmi -f 2>/dev/null || true
        log_info "Beta 镜像已清理"
    else
        log_info "未发现需要清理的 Beta 镜像"
    fi
}

# 数据安全管理函数
backup_user_data() {
    local backup_reason="$1"  # 原因：force-install/update/uninstall
    
    if [[ ! -d "$INSTALL_DIR" ]]; then
        return 0  # 没有现有安装，无需备份
    fi
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="$INSTALL_DIR/backup-${backup_reason}-${timestamp}"
    
    log_info "为${backup_reason}操作创建数据备份..."
    mkdir -p "$backup_dir"
    
    local backed_up=false
    
    # 备份数据目录
    if [[ -d "$INSTALL_DIR/data" ]]; then
        cp -r "$INSTALL_DIR/data" "$backup_dir/"
        backed_up=true
        local data_size=$(du -sh "$INSTALL_DIR/data" 2>/dev/null | cut -f1 || echo "未知")
        log_info "已备份：数据目录 ($data_size)"
    fi
    
    # 备份配置文件
    if [[ -f "$INSTALL_DIR/config.yaml" ]]; then
        cp "$INSTALL_DIR/config.yaml" "$backup_dir/"
        backed_up=true
        log_info "已备份：配置文件"
    fi
    
    # 备份日志（可选，通常较大）
    if [[ -d "$INSTALL_DIR/logs" ]] && [[ "$backup_reason" != "uninstall" ]]; then
        cp -r "$INSTALL_DIR/logs" "$backup_dir/"
        log_info "已备份：日志目录"
    fi
    
    if [[ "$backed_up" == true ]]; then
        echo "BACKUP_DIR=$backup_dir" > /tmp/dstatus_backup_info
        log_success "数据备份完成：$backup_dir"
        return 0
    else
        rmdir "$backup_dir" 2>/dev/null || true
        log_info "无需备份的数据"
        return 1
    fi
}

# 数据恢复函数
restore_user_data() {
    local backup_dir="$1"
    
    if [[ ! -d "$backup_dir" ]]; then
        log_warning "备份目录不存在：$backup_dir"
        return 1
    fi
    
    log_info "恢复用户数据从：$backup_dir"
    
    # 恢复数据目录
    if [[ -d "$backup_dir/data" ]]; then
        rm -rf "$INSTALL_DIR/data"
        cp -r "$backup_dir/data" "$INSTALL_DIR/"
        log_success "已恢复数据目录"
    fi
    
    # 恢复配置文件
    if [[ -f "$backup_dir/config.yaml" ]]; then
        cp "$backup_dir/config.yaml" "$INSTALL_DIR/"
        log_success "已恢复配置文件"
    fi
    
    # 恢复日志（如果存在）
    if [[ -d "$backup_dir/logs" ]]; then
        rm -rf "$INSTALL_DIR/logs"
        cp -r "$backup_dir/logs" "$INSTALL_DIR/"
        log_success "已恢复日志目录"
    fi
    
    return 0
}

# 安全处理现有安装
handle_existing_installation() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        return 0  # 没有现有安装
    fi
    
    if [[ "$FORCE_INSTALL" == true ]]; then
        log_warning "检测到现有安装，强制模式将会："
        echo "  • 自动备份您的数据和配置"
        echo "  • 重新安装最新版本"
        echo "  • 恢复您的数据和配置"
        echo ""
        
        # 创建备份
        if backup_user_data "force-install"; then
            log_info "数据备份成功，继续安装..."
        else
            log_info "未发现需要备份的数据，继续安装..."
        fi
        
        # 停止现有服务
        log_info "停止现有服务..."
        if [[ -f "$INSTALL_DIR/docker-compose.yml" ]]; then
            cd "$INSTALL_DIR" && docker compose down 2>/dev/null || true
        fi
        
        # 保存备份目录信息以供恢复
        if [[ -f "/tmp/dstatus_backup_info" ]]; then
            cp /tmp/dstatus_backup_info /tmp/dstatus_restore_info
        fi
        
        # 安全删除（保护备份目录）
        for item in "$INSTALL_DIR"/*; do
            if [[ -d "$item" ]] && [[ "$(basename "$item")" =~ ^backup- ]]; then
                continue  # 跳过备份目录
            fi
            rm -rf "$item" 2>/dev/null || true
        done
        
    else
        log_warning "检测到现有 DStatus Beta 安装在 $INSTALL_DIR"
        echo ""
        log_info "您可以选择："
        echo "  • 使用 --force 进行安全覆盖安装（会自动备份数据）"
        echo "  • 使用 --update 进行在线更新" 
        echo "  • 使用 --uninstall 完全卸载"
        echo ""
        exit 1
    fi
}

# 安全卸载DStatus Beta
uninstall_dstatus() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        log_success "DStatus Beta 已完全卸载"
        exit 0
    fi

    log_warning "开始卸载 DStatus Beta..."

    # 停止并删除所有容器
    cd "$INSTALL_DIR" && docker compose down --volumes --remove-orphans || true
    clean_beta_images

    # 删除安装目录
    rm -rf "$INSTALL_DIR"

    log_success "DStatus Beta 已完全卸载（含数据及镜像）"
    exit 0
}


# 显示Beta版本警告
show_beta_warning() {
    echo ""
    echo "================================================="
    echo "⚠️  DStatus Beta 测试版本安装警告"
    echo "================================================="
    echo ""
    echo "注意："
    echo "  • 这是内部测试版本，仅供开发和测试使用"
    echo "  • 可能包含未经充分测试的新功能"
    echo "  • 不建议在生产环境中使用"
    echo "  • 可能存在数据丢失或系统不稳定的风险"
    echo ""
    echo "Beta版本优势："
    echo "  • 体验最新功能和改进"
    echo "  • 独立安装，不影响正式版本"
    echo "  • 快速获得问题反馈和修复"
    echo ""
    echo "继续安装意味着您理解并接受上述风险"
    echo ""
    
    # 等待用户确认（除非使用了--yes参数）
    if [[ "$YES_TO_ALL" != "true" ]]; then
        # 检测是否可以进行交互（避免curl管道方式时的问题）
        if [[ -t 0 ]]; then
            read -p "是否继续安装 Beta 版本？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "安装已取消"
                exit 0
            fi
        else
            log_warning "检测到非交互式环境（如curl管道方式）"
            log_error "请使用 --yes 参数跳过确认，或下载脚本后本地运行"
            echo ""
            echo "建议的运行方式："
            echo "  方式1: curl -fsSL https://down.vps.mom/downloads/beta/install-beta.sh | bash -s -- --license-key=\"YOUR_KEY\" --yes"
            echo "  方式2: wget https://down.vps.mom/downloads/beta/install-beta.sh && bash install-beta.sh --license-key=\"YOUR_KEY\""
            echo ""
            exit 1
        fi
    else
        log_info "已自动确认安装 Beta 版本"
    fi
}

# 自动安装依赖
install_dependencies() {
    log_info "检查并安装核心依赖..."
    
    local commands_to_check=("curl" "wget" "tar")
    local missing_commands=()
    
    for cmd in "${commands_to_check[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [ ${#missing_commands[@]} -eq 0 ]; then
        log_info "所有核心依赖均已安装。"
        return 0
    fi
    
    log_warning "检测到以下依赖缺失: ${missing_commands[*]}"
    log_info "尝试自动安装..."
    
    # 检测包管理器
    local pkg_manager=""
    if command -v apt-get >/dev/null 2>&1; then
        pkg_manager="apt-get"
    elif command -v dnf >/dev/null 2>&1; then
        pkg_manager="dnf"
    elif command -v yum >/dev/null 2>&1; then
        pkg_manager="yum"
    else
        log_error "无法检测到 apt-get, dnf, 或 yum。请手动安装缺失的依赖。"
        return 1
    fi
    
    log_info "使用 $pkg_manager 进行安装。"
    
    # 根据包管理器安装
    if [[ "$pkg_manager" == "apt-get" ]]; then
        apt-get update -qq || log_warning "apt-get update 失败，可能无法安装依赖。"
        for cmd in "${missing_commands[@]}"; do
            apt-get install -y -qq "$cmd" || log_error "安装 $cmd 失败。"
        done
    else # dnf or yum
        for cmd in "${missing_commands[@]}"; do
            $pkg_manager install -y -q "$cmd" || log_error "安装 $cmd 失败。"
        done
    fi
    
    # 再次验证
    for cmd in "${missing_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_error "关键依赖 $cmd 安装失败。请手动安装后重试。"
            return 1
        fi
    done

    log_success "依赖已成功安装/验证。"
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 仅支持 Linux x86_64 架构
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持 Linux 系统 (x86_64)"
        exit 1
    fi
    if [[ "$(uname -m)" != "x86_64" ]]; then
        log_error "当前架构 $(uname -m) 不受支持，仅支持 x86_64"
        exit 1
    fi
    
    # 检查权限
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用 root 权限运行此脚本"
        exit 1
    fi
    
    # 检查并安装必要命令
    install_dependencies
    if [ $? -ne 0 ]; then
        exit 1
    fi
    
    log_success "系统检查通过"
}

# 检查端口冲突 – 零阻塞 + --force 自动腾端口
check_port_conflict() {
    log_info "检查端口 $PORT 是否被占用..."
    local occupied=false

    if command -v ss >/dev/null 2>&1; then
        ss -H -ln state LISTEN sport = :$PORT | grep -q . && occupied=true
    elif command -v lsof >/dev/null 2>&1; then
        lsof -nP -iTCP:$PORT -sTCP:LISTEN >/dev/null 2>&1 && occupied=true
    elif command -v netstat >/dev/null 2>&1; then
        netstat -tln | awk '{print $4}' | grep -qE "(:|\\.)$PORT$" && occupied=true
    else
        log_warning "系统缺少 ss/lsof/netstat，跳过端口检查"
        return
    fi

    if ${occupied}; then
        if [[ "$FORCE_INSTALL" == true ]]; then
            log_warning "端口 $PORT 被占用，已启用 --force，尝试释放..."

            if command -v docker >/dev/null 2>&1; then
                local cid
                cid=$(docker ps --format '{{.ID}} {{.Ports}}' | grep -E "0\.0\.0\.0:$PORT->|::$PORT->" | awk '{print $1}' | head -n1)
                if [[ -n "$cid" ]]; then
                    log_info "停止占用端口的容器 $cid"
                    docker stop "$cid" >/dev/null 2>&1 || true
                    sleep 2
                    check_port_conflict  # 递归再检
                    return
                fi
            fi
            log_warning "未能自动释放端口，继续安装可能失败"
        else
            log_error "端口 $PORT 已被占用；如需覆盖安装请加 --force 或改 --port"
            exit 1
        fi
    fi

    log_success "端口 $PORT 可用"
}

# 检查正式版本冲突
check_production_conflict() {
    log_info "检查与正式版本的冲突..."
    
    # 检查正式版本是否已安装
    if [[ -d "/opt/dstatus" ]]; then
        log_info "检测到正式版本已安装在 /opt/dstatus"
        
        # 检查正式版本Docker容器状态
        if docker ps --format "{{.Names}}" | grep -q "dstatus-monitor" 2>/dev/null; then
            log_warning "正式版本Docker容器正在运行"
            log_info "Beta版本将使用独立的端口和服务名"
        fi
    fi
    
    log_success "冲突检查完成"
}

# 检查并安装 Docker
check_and_install_docker() {
    if [[ "$AUTO_INSTALL_DOCKER" == "false" ]]; then
        log_info "跳过 Docker 自动安装"
        return
    fi

    # 检查 Docker 是否已安装
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装，版本: $(docker --version)"

        # 检查 Docker 服务是否运行
        if ! docker info &> /dev/null; then
            log_warning "Docker 守护进程未运行，请手动启动 Docker 服务"
            log_info "通常可以使用: sudo service docker start 或重启系统"
            exit 1
        fi

        # 检查 Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            log_info "安装 Docker Compose..."
            install_docker_compose
        fi

        return
    fi

    log_info "未检测到 Docker，开始自动安装..."

    # 检测系统类型
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VER=$VERSION_ID
    else
        log_error "无法检测系统类型"
        exit 1
    fi

    case $OS in
        ubuntu|debian)
            install_docker_debian
            ;;
        centos|rhel|fedora)
            install_docker_redhat
            ;;
        *)
            log_warning "不支持的系统类型: $OS，尝试使用官方安装脚本..."
            install_docker_official
            ;;
    esac

    # 验证安装
    if command -v docker &> /dev/null; then
        log_success "Docker 安装成功"
        sleep 3
        
        if ! docker info &> /dev/null; then
            log_warning "Docker 守护进程未运行，请手动启动或重启系统"
            log_info "安装完成后通常需要重启系统或手动启动 Docker 服务"
        fi
        
        install_docker_compose
    else
        log_error "Docker 安装失败"
        exit 1
    fi
}

# 安装 Docker (Debian/Ubuntu)
install_docker_debian() {
    log_info "在 Debian/Ubuntu 系统上安装 Docker..."
    
    apt-get update -qq
    apt-get install -y -qq ca-certificates curl gnupg lsb-release
    
    mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/$OS/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$OS \
        $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    apt-get update -qq
    apt-get install -y -qq docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
}

# 安装 Docker (CentOS/RHEL/Fedora)
install_docker_redhat() {
    log_info "在 CentOS/RHEL/Fedora 系统上安装 Docker..."
    
    if command -v dnf &> /dev/null; then
        dnf install -y -q yum-utils
        dnf config-manager --add-repo https://download.docker.com/linux/$OS/docker-ce.repo
        dnf install -y -q docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    else
        yum install -y -q yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/$OS/docker-ce.repo
        yum install -y -q docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    fi
}

# 使用官方安装脚本安装 Docker
install_docker_official() {
    log_info "使用 Docker 官方安装脚本..."
    
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
}

# 安装 Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装"
        return
    fi
    
    if docker compose version &> /dev/null; then
        log_info "Docker Compose 插件已安装"
        if [[ ! -f /usr/local/bin/docker-compose ]]; then
            cat > /usr/local/bin/docker-compose << 'EOF'
#!/bin/bash
docker compose "$@"
EOF
            chmod +x /usr/local/bin/docker-compose
        fi
        return
    fi
    
    log_info "安装 Docker Compose..."
    
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    if [[ -z "$COMPOSE_VERSION" ]]; then
        COMPOSE_VERSION="v2.24.0"
    fi
    
    curl -L "https://github.com/docker/compose/releases/download/$COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
}

# 确保Docker可用
ensure_docker_available() {
    log_info "确保 Docker 环境可用..."
    
    check_and_install_docker
    
    if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
        log_error "Docker 安装失败，无法安装 DStatus Beta"
        exit 1
    fi
    
    log_success "Docker 环境准备完成"
}

# 下载文件
download_file() {
    local url="$1"
    local output="$2"
    
    log_info "下载: $url"
    
    if command -v wget &> /dev/null; then
        wget -q --show-progress -O "$output" "$url"
    else
        curl -L -o "$output" "$url"
    fi
    
    if [[ $? -ne 0 ]]; then
        log_error "下载失败: $url"
        exit 1
    fi
}

# 检查网络冲突
check_network_conflicts() {
    local subnet="$1"
    local ipv6_subnet="$2"
    
    # 检查 IPv4 子网冲突
    if [[ -n "$subnet" ]]; then
        local existing_networks=$(docker network ls --format "{{.Name}}" | grep -v "bridge\|host\|none" | head -10)
        for network in $existing_networks; do
            local network_subnet=$(docker network inspect "$network" --format '{{range .IPAM.Config}}{{.Subnet}}{{end}}' 2>/dev/null)
            if [[ "$network_subnet" == "$subnet" ]]; then
                return 1  # 冲突
            fi
        done
    fi
    
    # 检查系统网络接口冲突
    if ip route | grep -q "$subnet"; then
        return 1  # 与系统路由冲突
    fi
    
    return 0  # 无冲突
}

# 查找可用的网络范围
find_available_subnet() {
    # IPv4候选子网池 - 分优先级
    local ipv4_primary=(
        "**********/16"
        "**********/16"
        "**********/16"
        "**********/16"
    )
    
    local ipv4_secondary=(
        "**********/16"
        "**********/16"
        "**********/16"
        "**********/16"
    )
    
    local ipv4_fallback=(
        "*************/24"
        "*************/24"
        "*************/24"
        "*************/24"
    )
    
    # 合并所有IPv4候选
    local ipv4_candidates=("${ipv4_primary[@]}" "${ipv4_secondary[@]}" "${ipv4_fallback[@]}")
    
    # IPv6候选子网池 - 预定义ULA (使用有效的十六进制)
    local ipv6_primary=(
        "fd00:dead:beef::/48"
        "fd00:cafe:babe::/48"
        "fd00:bad:c0de::/48" 
        "fd01:acdc:feed::/48"
    )
    
    local ipv6_secondary=(
        "fd02:ab12:cd34::/48"
        "fd03:ef56:ab78::/48"
        "fd04:1234:5678::/48"
        "fd05:9abc:def0::/48"
    )
    
    # 合并所有IPv6候选
    local ipv6_candidates=("${ipv6_primary[@]}" "${ipv6_secondary[@]}")
    
    # 第一轮：尝试主要候选组合
    for ipv4 in "${ipv4_primary[@]}"; do
        for ipv6 in "${ipv6_primary[@]}"; do
            if check_network_conflicts "$ipv4" "$ipv6"; then
                echo "$ipv4 $ipv6"
                return 0
            fi
        done
    done
    
    # 第二轮：尝试所有固定候选组合
    for ipv4 in "${ipv4_candidates[@]}"; do
        for ipv6 in "${ipv6_candidates[@]}"; do
            if check_network_conflicts "$ipv4" "$ipv6"; then
                echo "$ipv4 $ipv6"
                return 0
            fi
        done
    done
    
    # 第三轮：使用随机ULA与固定IPv4组合
    for ipv4 in "${ipv4_candidates[@]}"; do
        for i in {1..10}; do  # 增加尝试次数
            local random_ipv6
            random_ipv6=$(generate_random_ula)
            if check_network_conflicts "$ipv4" "$random_ipv6"; then
                echo "$ipv4 $random_ipv6"
                return 0
            fi
        done
    done
    
    # 第四轮：纯随机方式（最后的兜底方案）
    for i in {1..20}; do
        local random_ipv4 random_ipv6
        # 生成随机的10.x.0.0/16网段
        local random_second=$((RANDOM % 50 + 200))  # 10.200-249.0.0/16
        random_ipv4="10.${random_second}.0.0/16"
        random_ipv6=$(generate_random_ula)
        
        if check_network_conflicts "$random_ipv4" "$random_ipv6"; then
            echo "$random_ipv4 $random_ipv6"
            return 0
        fi
    done
    
    # 都失败，返回空值让Docker自动分配
    echo "" ""
}

# 生成Docker Compose IPv4网络配置段
generate_compose_ipv4_network() {
    local compose_file="$1"
    local network_name="$2"
    local ipv4_subnet="$3"
    
    # 移除现有的networks配置段
    if [[ -f "$compose_file" ]] && grep -q '^networks:' "$compose_file" 2>/dev/null; then
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' '/^networks:/,$d' "$compose_file"
        else
            sed -i '/^networks:/,$d' "$compose_file"
        fi
    fi
    
    cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
EOF

    if [[ -n "$ipv4_subnet" ]]; then
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv4_subnet}
EOF
    fi
}

# 生成Docker Compose IPv6网络配置段
generate_compose_ipv6_network() {
    local compose_file="$1"
    local network_name="$2"
    local ipv4_subnet="$3"
    local ipv6_subnet="$4"
    local use_routed_mode="${5:-false}"
    
    # 移除现有的networks配置段
    if [[ -f "$compose_file" ]] && grep -q '^networks:' "$compose_file" 2>/dev/null; then
        # Linux 系统不需要 '' 参数
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' '/^networks:/,$d' "$compose_file"
        else
            sed -i '/^networks:/,$d' "$compose_file"
        fi
    fi
    
    # 生成网络配置
    cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
    enable_ipv6: true
EOF

    # 根据模式选择不同的配置
    if [[ "$use_routed_mode" == "true" ]]; then
        # Routed模式 (Docker v27+)
        cat >> "$compose_file" << EOF
    driver_opts:
      com.docker.network.bridge.gateway_mode_ipv6: "routed"
EOF
    fi
    
    # 添加IPAM配置
    if [[ -n "$ipv4_subnet" && -n "$ipv6_subnet" ]]; then
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv4_subnet}
        - subnet: ${ipv6_subnet}
          gateway: $(echo ${ipv6_subnet} | sed 's/:\/.*$/:1/')
EOF
    elif [[ -n "$ipv6_subnet" ]]; then
        # 仅IPv6
        cat >> "$compose_file" << EOF
    ipam:
      driver: default
      config:
        - subnet: ${ipv6_subnet}
          gateway: $(echo ${ipv6_subnet} | sed 's/:\/.*$/:1/')
EOF
    fi
}

# 清理旧的DStatus网络
cleanup_old_networks() {
    echo "正在清理可能存在的旧DStatus网络..."
    
    # 查找所有以dstatus开头的网络
    local old_networks
    old_networks=$(docker network ls --format "{{.Name}}" | grep "^dstatus" || true)
    
    for network in $old_networks; do
        # 检查网络是否正在被使用
        local container_count
        container_count=$(docker network inspect "$network" --format '{{len .Containers}}' 2>/dev/null || echo "0")
        
        if [[ "$container_count" -eq 0 ]]; then
            echo "删除未使用的网络: $network"
            docker network rm "$network" 2>/dev/null || true
        else
            echo "保留正在使用的网络: $network (容器数: $container_count)"
        fi
    done
}

# 启用IPv6网络配置
enable_ipv6_network() {
    local compose_file="${1:-docker-compose.yml}"
    local use_routed_mode="${2:-false}"
    local force_network_name="${3:-}"
    
    log_info "正在配置IPv6双栈网络..."
    
    # 清理旧网络
    cleanup_old_networks
    
    # 生成唯一网络名
    local network_name
    if [[ -n "$force_network_name" ]]; then
        network_name="$force_network_name"
    else
        network_name=$(generate_unique_network_name "dstatus_beta")
    fi
    
    # 查找可用子网
    local subnet_info ipv4_subnet ipv6_subnet
    subnet_info=$(find_available_subnet)
    read -r ipv4_subnet ipv6_subnet <<< "$subnet_info"
    
    if [[ -z "$ipv4_subnet" && -z "$ipv6_subnet" ]]; then
        log_warning "未找到可用的自定义网络范围，使用Docker默认分配"
        # 生成简单的IPv6配置
        cat >> "$compose_file" << EOF

networks:
  default:
    name: ${network_name}
    driver: bridge
    enable_ipv6: true
EOF
    else
        log_info "使用IPv4子网: $ipv4_subnet"
        log_info "使用IPv6子网: $ipv6_subnet"
        
        # 生成完整网络配置
        generate_compose_ipv6_network "$compose_file" "$network_name" "$ipv4_subnet" "$ipv6_subnet" "$use_routed_mode"
    fi
    
    # 确保服务使用正确的网络
    attach_service_to_network "$compose_file" "$network_name"
    
    if [[ "$use_routed_mode" == "true" ]]; then
        log_success "IPv6网络已成功配置 (routed模式): $network_name"
    else
        log_success "IPv6网络已成功配置 (bridge模式): $network_name"
    fi
    
    return 0
}

# 启用IPv4网络配置
enable_ipv4_network() {
    local compose_file="${1:-docker-compose.yml}"
    local force_network_name="${2:-}"
    
    log_info "正在配置IPv4网络..."
    
    # 清理旧网络
    cleanup_old_networks
    
    # 生成唯一网络名
    local network_name
    if [[ -n "$force_network_name" ]]; then
        network_name="$force_network_name"
    else
        network_name=$(generate_unique_network_name "dstatus_beta")
    fi
    
    # 查找可用IPv4子网
    local ipv4_subnet
    ipv4_subnet=$(find_available_ipv4_subnet)
    
    if [[ -n "$ipv4_subnet" ]]; then
        log_info "使用IPv4子网: $ipv4_subnet"
        generate_compose_ipv4_network "$compose_file" "$network_name" "$ipv4_subnet"
    else
        log_warning "未找到可用的IPv4子网，使用Docker默认分配"
        generate_compose_ipv4_network "$compose_file" "$network_name" ""
    fi
    
    # 确保服务使用正确的网络
    attach_service_to_network "$compose_file" "$network_name"
    
    log_success "IPv4网络已成功配置: $network_name"
    return 0
}

# 动态配置 Docker 网络 (IPv4/IPv6 自动检测)
configure_docker_network() {
    log_info "正在检测Docker网络环境..."

    # 详细的IPv6检测状态
    if detect_system_ipv6; then
        log_info "✓ 系统IPv6支持: 已启用"
    else
        log_warning "✗ 系统IPv6支持: 未启用或不可用"
    fi

    if detect_docker_ipv6; then
        log_info "✓ Docker IPv6支持: 已启用"
    else
        log_warning "✗ Docker IPv6支持: 未启用"
    fi

    # 根据检测结果选择网络配置
    if detect_full_ipv6_support; then
        log_info "检测到完整的IPv6支持环境"
        enable_ipv6_network "docker-compose.yml"
    else
        log_info "IPv6不可用，使用IPv4-only模式"
        enable_ipv4_network "docker-compose.yml"
    fi
}

# Docker 安装
install_docker() {
    log_info "使用 Docker 方式安装 DStatus Beta..."
    
    # 确保 Docker 已安装
    if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
        log_info "Docker 未完全安装，开始安装..."
        check_and_install_docker
    fi
    
    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"
    
    # 构建下载URL
    local download_url
    if [[ "$BETA_VERSION" == "latest" ]]; then
        download_url="$DOWNLOAD_BASE/dstatus-docker-beta.tar.gz"
    else
        download_url="$DOWNLOAD_BASE/dstatus-docker-beta-${BETA_VERSION}.tar.gz"
    fi
    
    # 下载 Docker 部署包
    download_file "$download_url" "dstatus-docker-beta.tar.gz"
    
    # 解压到当前目录
    log_info "解压部署包..."
    tar -xzf dstatus-docker-beta.tar.gz --strip-components=1
    rm dstatus-docker-beta.tar.gz
    
    # 修复权限和配置
    log_info "修复安装权限和配置..."
    
    chown -R root:root "$INSTALL_DIR"
    find "$INSTALL_DIR" -type f -exec chmod 644 {} \;
    find "$INSTALL_DIR" -type d -exec chmod 755 {} \;
    chmod +x "$INSTALL_DIR"/*.sh 2>/dev/null || true
    
    # 创建必要的数据目录
    mkdir -p "$INSTALL_DIR"/{data,logs,data/backups,data/temp}
    chmod -R 777 "$INSTALL_DIR"/data "$INSTALL_DIR"/logs
    
    # 修复 docker-compose.yml 配置
    if [[ -f "docker-compose.yml" ]]; then
        # 更新容器名称为Beta版本
        sed -i "s/container_name: dstatus-monitor/container_name: dstatus-beta-monitor/g" docker-compose.yml
        
        # 更新服务名称
        sed -i "s/dstatus-monitor:/dstatus-beta-monitor:/g" docker-compose.yml
        
        # 添加Beta标识
        if ! grep -q "BETA_VERSION" docker-compose.yml; then
            sed -i '/environment:/a\      - BETA_VERSION=true' docker-compose.yml
        fi
        
        # 确保权限配置
        if ! grep -q "user:" docker-compose.yml; then
            sed -i '/container_name: dstatus-beta-monitor/a\    user: "0:0"' docker-compose.yml
        fi
    fi
    
    # 配置许可证自动激活
    if [[ -n "$LICENSE_KEY" ]]; then
        log_info "配置许可证自动激活..."
        
        # 添加环境变量到docker-compose.yml
        if ! grep -q "DSTATUS_AUTO_LICENSE" docker-compose.yml; then
            # 在environment部分添加许可证环境变量
            sed -i.bak '/environment:/a\
      - DSTATUS_AUTO_LICENSE='"$LICENSE_KEY" docker-compose.yml
            # 删除备份文件
            rm -f docker-compose.yml.bak
            
            if grep -q "DSTATUS_AUTO_LICENSE" docker-compose.yml; then
                log_success "许可证将在系统启动时自动激活"
            else
                log_warning "许可证环境变量配置失败，请在Web管理面板中手动激活"
            fi
        else
            log_info "许可证环境变量已存在，跳过配置"
        fi
    fi
    
    # 动态配置 Docker 网络 (IPv4/IPv6)
    configure_docker_network
    
    # 配置端口
    if [[ "$PORT" != "5555" ]]; then
        log_info "配置自定义端口: $PORT"
        # 支持多种端口格式匹配
        sed -i.bak -E "s/\"[0-9]+:5555\"/\"$PORT:5555\"/g" docker-compose.yml
        
        if grep -q "\"$PORT:5555\"" docker-compose.yml; then
            log_success "端口配置成功：外部端口 $PORT -> 内部端口 5555"
            rm -f docker-compose.yml.bak
        else
            log_error "端口配置失败，请检查docker-compose.yml文件"
            log_info "当前端口映射："
            grep -E "[0-9]+:5555" docker-compose.yml || true
            exit 1
        fi
    fi
    
    # 启动服务
    log_info "启动 DStatus Beta 服务..."
    ./start.sh
    
    # 恢复用户数据（如果有备份）
    if [[ -f "/tmp/dstatus_restore_info" ]]; then
        source /tmp/dstatus_restore_info
        if [[ -n "$BACKUP_DIR" ]] && [[ -d "$BACKUP_DIR" ]]; then
            log_info "恢复之前的用户数据..."
            restore_user_data "$BACKUP_DIR"
            
            # 重启服务以应用恢复的配置  
            ./stop.sh 2>/dev/null || true
            sleep 2
            ./start.sh
        fi
        rm -f /tmp/dstatus_restore_info /tmp/dstatus_backup_info
    fi
    
    log_success "Docker Beta 安装完成！"
    log_info "访问地址: http://localhost:$PORT"
}


# 更新 DStatus Beta
update_dstatus() {
    log_info "开始更新 DStatus Beta..."
    
    # 检查是否已安装
    if [[ ! -d "$INSTALL_DIR" ]]; then
        log_error "未找到现有安装，请使用安装模式"
        exit 1
    fi
    
    # 检测当前安装方式
    if [[ -f "$INSTALL_DIR/docker-compose.yml" ]]; then
        log_info "检测到 Docker Beta 安装"
    else
        log_error "未检测到有效的Docker Beta安装"
        exit 1
    fi

    # 备份用户数据
    backup_user_data "update"

    # 停止现有服务
    log_info "停止现有服务..."
    if [[ -f "$INSTALL_DIR/stop.sh" ]]; then
        cd "$INSTALL_DIR"
        ./stop.sh 2/dev/null || true
    fi

    # 执行更新
    update_docker_beta

    log_success "Beta 更新完成！"
}

# 更新 Docker Beta 安装
update_docker_beta() {
    log_info "更新 Docker Beta 安装..."
    
    cd "$INSTALL_DIR"
    
    # 停止现有服务并清理镜像
    docker compose down --volumes --remove-orphans || true
    clean_beta_images
    
    # 下载新版本
    local download_url
    if [[ "$BETA_VERSION" == "latest" ]]; then
        download_url="$DOWNLOAD_BASE/dstatus-docker-beta.tar.gz"
    else
        download_url="$DOWNLOAD_BASE/dstatus-docker-beta-${BETA_VERSION}.tar.gz"
    fi
    
    download_file "$download_url" "dstatus-docker-beta-new.tar.gz"
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # 解压新版本
    tar -xzf "$INSTALL_DIR/dstatus-docker-beta-new.tar.gz" --strip-components=1
    
    # 保留配置和数据
    if [[ -f "$INSTALL_DIR/config.yaml" ]]; then
        cp "$INSTALL_DIR/config.yaml" ./config.yaml
    fi
    
    if [[ -d "$INSTALL_DIR/data" ]]; then
        rm -rf ./data
        cp -r "$INSTALL_DIR/data" ./data
    fi
    
    if [[ -d "$INSTALL_DIR/logs" ]]; then
        rm -rf ./logs
        cp -r "$INSTALL_DIR/logs" ./logs
    fi
    
    # 替换文件
    cd "$INSTALL_DIR"
    rm -f dstatus-docker-beta-new.tar.gz
    
    for file in *; do
        if [[ "$file" != "data" ]] && [[ "$file" != "logs" ]] && [[ "$file" != "config.yaml" ]] && [[ ! "$file" =~ ^backup- ]]; then
            rm -rf "$file"
        fi
    done
    
    # 复制新文件
    cp -r "$TEMP_DIR"/* ./
    rm -rf "$TEMP_DIR"
    
    # 重新应用配置
    if [[ -f "docker-compose.yml" ]]; then
        sed -i "s/container_name: dstatus-monitor/container_name: dstatus-beta-monitor/g" docker-compose.yml
        sed -i "s/dstatus-monitor:/dstatus-beta-monitor:/g" docker-compose.yml
        
        if ! grep -q "BETA_VERSION" docker-compose.yml; then
            sed -i '/environment:/a\      - BETA_VERSION=true' docker-compose.yml
        fi
        
        # 重新应用网络配置
        configure_docker_network
        
        # 配置端口
        if [[ "$PORT" != "5555" ]]; then
            log_info "重新配置端口: $PORT"
            # 支持多种端口格式匹配
            sed -i.bak -E "s/\"[0-9]+:5555\"/\"$PORT:5555\"/g" docker-compose.yml
            rm -f docker-compose.yml.bak
        fi
        
        log_success "配置重新应用完成"
    fi
    
    # 强制重新构建
    docker compose build --no-cache
    
    # 启动服务
    ./start.sh
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    sleep 5
    
    # 检查Docker容器状态
    if [[ -f "$INSTALL_DIR/docker-compose.yml" ]]; then
        cd "$INSTALL_DIR"
        local container_status=$(docker compose ps --services --filter status=running 2>/dev/null | wc -l)
        
        if [[ "$container_status" -gt 0 ]]; then
            log_success "DStatus Beta Docker容器运行正常"
            
            if netstat -tlnp | grep ":$PORT " > /dev/null 2>&1; then
                log_success "服务端口 $PORT 正在监听"
            else
                log_warning "服务端口 $PORT 未监听，请检查配置"
            fi
        else
            log_error "DStatus Beta Docker容器启动失败"
            log_info "查看日志: cd $INSTALL_DIR && docker compose logs"
            return 1
        fi
    else
        log_warning "未找到docker-compose.yml，跳过服务检查"
    fi
}

# 显示安装结果
show_result() {
    echo ""
    echo "🎉 DStatus Beta 安装完成！"
    echo ""
    echo "⚠️  这是测试版本，请谨慎使用"
    echo ""
    echo "安装信息:"
    echo "   安装方式: Docker Beta"
    echo "   安装目录: $INSTALL_DIR"
    echo "   服务端口: $PORT"

    # 获取公网 IPv4 地址（若失败则回退到localhost）
    local PUBLIC_IP
    PUBLIC_IP=$(curl -4 -sS --connect-timeout 5 https://ipv4.icanhazip.com 2>/dev/null || curl -4 -sS --connect-timeout 5 https://api.ipify.org 2>/dev/null || curl -4 -sS --connect-timeout 5 https://ifconfig.me 2>/dev/null || hostname -I 2>/dev/null | awk '{for(i=1;i<=NF;i++) if($i ~ /^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/) {print $i; exit}}' || echo "localhost")
    if [[ -z "$PUBLIC_IP" ]]; then
        PUBLIC_IP="localhost"
    fi
    echo "   访问地址: http://$PUBLIC_IP:$PORT"

    echo "   Beta版本: $BETA_VERSION"
    echo ""
    echo "管理命令:"
    echo "   启动服务: cd $INSTALL_DIR && docker compose up -d"
    echo "   停止服务: cd $INSTALL_DIR && docker compose down"
    echo "   重启服务: cd $INSTALL_DIR && docker compose restart"
    echo "   查看状态: cd $INSTALL_DIR && docker compose ps"
    echo "   查看日志: cd $INSTALL_DIR && docker compose logs -f"
    echo ""

}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --method)
            INSTALL_METHOD="$2"
            shift 2
            ;;
        --method=*)
            INSTALL_METHOD="${1#*=}"
            shift
            ;;
        --license-key)
            LICENSE_KEY="$2"
            shift 2
            ;;
        --license-key=*)
            LICENSE_KEY="${1#*=}"
            shift
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --port=*)
            PORT="${1#*=}"
            shift
            ;;
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --install-dir=*)
            INSTALL_DIR="${1#*=}"
            shift
            ;;
        --beta-version)
            BETA_VERSION="$2"
            shift 2
            ;;
        --beta-version=*)
            BETA_VERSION="${1#*=}"
            shift
            ;;
        --force)
            FORCE_INSTALL=true
            shift
            ;;
        --update)
            UPDATE_MODE=true
            shift
            ;;
        --uninstall)
            UNINSTALL=true
            shift
            ;;
        --skip-docker|--no-auto-docker)
            AUTO_INSTALL_DOCKER=false
            shift
            ;;
        -y|--yes)
            YES_TO_ALL=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主安装流程
main() {
    # 显示Beta警告
    show_beta_warning
    
    echo "🚀 DStatus Beta 测试版安装脚本"
    echo "=================================="
    
    # 检查系统
    check_system
    
    # 更新模式
    if [[ "$UPDATE_MODE" == true ]]; then
        log_info "运行更新模式..."
        update_dstatus
        check_service
        show_result
        return
    fi
    
    # 卸载模式
    if [[ "$UNINSTALL" == true ]]; then
        uninstall_dstatus
        return
    fi
    
    # 安装模式需要许可证密钥
    if [[ -z "$LICENSE_KEY" ]]; then
        log_error "请提供许可证密钥: --license-key=\"YOUR_LICENSE_KEY\""
        exit 1
    fi
    
    # 检查冲突
    check_port_conflict
    check_production_conflict
    
    # 确保Docker环境可用
    ensure_docker_available
    
    # 安全处理现有安装
    handle_existing_installation
    
    # 执行安装
    install_docker
    
    # 检查服务
    check_service
    
    # 显示结果
    show_result
}

# 运行主函数
main "$@"