#!/bin/bash

# DStatus 密码重置工具
# 使用方法: 
#   交互式: curl -fsSL https://down.vps.mom/scripts/reset-password.sh | bash
#   自动模式: curl -fsSL https://down.vps.mom/scripts/reset-password.sh | bash -s -- -y

echo "🔐 DStatus 密码重置工具"
echo "========================"

# 检查参数，支持 -y 自动确认
AUTO_CONFIRM=false
if [[ "$1" == "-y" ]] || [[ "$1" == "--yes" ]]; then
    AUTO_CONFIRM=true
    echo "🤖 自动模式已启用"
fi

# 检查 sqlite3
if ! command -v sqlite3 &> /dev/null; then
    echo "❌ 需要安装 sqlite3"
    echo "Ubuntu/Debian: apt-get install -y sqlite3"
    echo "CentOS/RHEL: yum install -y sqlite"
    exit 1
fi

# 查找数据库（优先查找常见位置）
echo "🔍 检查常见安装位置..."
for path in /opt/dstatus*/data/db.db /root/dstatus*/data/db.db ~/dstatus*/data/db.db ./data/db.db; do
    if [[ -f "$path" ]] && sqlite3 "$path" "SELECT 1 FROM setting LIMIT 1;" &>/dev/null 2>&1; then
        DB="$path"
        break
    fi
done

# 如果常见位置找不到，进行深度搜索
if [[ -z "$DB" ]]; then
    echo "📂 常见位置未找到，正在搜索其他目录..."
    echo "   这可能需要一些时间，请稍候..."
    
    # 搜索可能的安装目录（限制搜索深度和范围）
    SEARCH_DIRS="/opt /usr/local /home /root /var"
    DB_FOUND=""
    
    for dir in $SEARCH_DIRS; do
        if [[ -d "$dir" ]]; then
            # 查找包含 dstatus 的目录下的 db.db 文件
            DB_FOUND=$(find "$dir" -maxdepth 5 -type f -path "*dstatus*/data/db.db" 2>/dev/null | head -5)
            if [[ -n "$DB_FOUND" ]]; then
                # 验证找到的数据库
                for candidate in $DB_FOUND; do
                    if sqlite3 "$candidate" "SELECT 1 FROM setting LIMIT 1;" &>/dev/null 2>&1; then
                        DB="$candidate"
                        echo "✅ 在自定义位置找到数据库"
                        break 2
                    fi
                done
            fi
        fi
    done
fi

# 如果还是找不到，提供帮助信息
if [[ -z "$DB" ]]; then
    echo "❌ 未找到 DStatus 数据库"
    echo ""
    echo "可能的原因："
    echo "1. DStatus 未安装或安装在非标准位置"
    echo "2. 数据库文件权限不足"
    echo "3. 使用了自定义的数据目录"
    echo ""
    echo "解决方法："
    echo "1. 进入 DStatus 安装目录后运行此脚本"
    echo "2. 手动指定数据库路径："
    echo "   sqlite3 /path/to/data/db.db \"UPDATE setting SET val='\\\"dstatus\\\"' WHERE key='password';\""
    exit 1
fi

echo "✅ 找到数据库: $DB"

# 查询当前密码状态
CURRENT_PWD=$(sqlite3 "$DB" "SELECT val FROM setting WHERE key='password';" 2>/dev/null)
if [[ -n "$CURRENT_PWD" ]]; then
    # 计算密码长度（去除JSON引号）
    PWD_LEN=$(echo "$CURRENT_PWD" | sed 's/^"//;s/"$//' | wc -c | xargs)
    echo "📊 当前密码状态: 已设置 (长度: $PWD_LEN 字符)"
    
    # 检查是否为默认密码
    if [[ "$CURRENT_PWD" == '"dstatus"' ]]; then
        echo "⚠️  警告: 当前使用的是默认密码"
    fi
else
    echo "📊 当前密码状态: 未设置"
fi

# 用户确认
echo ""
echo "⚠️  即将执行以下操作："
echo "   1. 备份数据库"
echo "   2. 重置密码为: dstatus"
echo ""
if [[ "$AUTO_CONFIRM" == "true" ]]; then
    echo "🤖 自动确认执行"
    REPLY="y"
else
    if [ -t 0 ]; then
        # 交互式运行
        read -p "是否继续？(y/N): " -n 1 -r
        echo
    elif [ -e /dev/tty ]; then
        # 管道运行，尝试从 /dev/tty 读取输入
        read -p "是否继续？(y/N): " -n 1 -r < /dev/tty
        echo
    else
        # 无法交互，提示使用自动模式
        echo "⚠️  无法进行交互式确认"
        echo "   请使用自动模式: curl -fsSL https://down.vps.mom/scripts/reset-password.sh | bash -s -- -y"
        exit 1
    fi
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        exit 0
    fi
fi

# 备份
BACKUP="${DB}.backup.$(date +%Y%m%d_%H%M%S)"
if cp "$DB" "$BACKUP"; then
    echo "✅ 已备份至: $BACKUP"
else
    echo "❌ 备份失败"
    if [[ "$AUTO_CONFIRM" == "true" ]]; then
        echo "🤖 自动模式：跳过备份继续执行"
    else
        if [ -t 0 ]; then
            read -p "备份失败，是否仍要继续？(y/N): " -n 1 -r
            echo
        elif [ -e /dev/tty ]; then
            read -p "备份失败，是否仍要继续？(y/N): " -n 1 -r < /dev/tty
            echo
        else
            echo "❌ 备份失败，无法进行交互式确认"
            exit 1
        fi
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 操作已取消"
            exit 0
        fi
    fi
fi

# 重置密码
echo "🔄 重置密码..."
if sqlite3 "$DB" "UPDATE setting SET val='\"dstatus\"' WHERE key='password';"; then
    echo "✅ 密码重置成功！"
    echo "📝 新密码: dstatus"
    echo "⚠️  请立即登录修改密码"
else
    echo "❌ 重置失败"
fi