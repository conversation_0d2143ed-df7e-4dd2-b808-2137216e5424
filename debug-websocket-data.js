/**
 * WebSocket数据调试脚本
 * 用于查看实际接收到的数据格式
 */

console.log('=== WebSocket数据调试 ===');

// 拦截原始的handleWsMessage函数
if (typeof window.originalHandleWsMessage === 'undefined' && typeof handleWsMessage !== 'undefined') {
    window.originalHandleWsMessage = handleWsMessage;
    
    // 重写handleWsMessage函数
    window.handleWsMessage = function(message, fromCache = false) {
        console.log('🔍 [调试] 接收到WebSocket消息:', {
            type: message.type,
            fromCache: fromCache,
            timestamp: message.timestamp,
            hasData: !!message.data,
            dataType: typeof message.data,
            dataKeys: message.data ? Object.keys(message.data) : [],
            dataSize: message.data ? Object.keys(message.data).length : 0,
            firstNodeId: message.data ? Object.keys(message.data)[0] : null,
            firstNodeData: message.data ? message.data[Object.keys(message.data)[0]] : null,
            fullMessage: message
        });
        
        // 如果有数据，详细检查第一个节点的结构
        if (message.data && Object.keys(message.data).length > 0) {
            const firstNodeId = Object.keys(message.data)[0];
            const firstNode = message.data[firstNodeId];
            
            console.log('🔍 [调试] 第一个节点详细结构:', {
                nodeId: firstNodeId,
                nodeType: typeof firstNode,
                nodeKeys: firstNode ? Object.keys(firstNode) : [],
                hasName: !!firstNode?.name,
                hasStat: !!firstNode?.stat,
                statType: typeof firstNode?.stat,
                statKeys: firstNode?.stat ? Object.keys(firstNode.stat) : [],
                hasNet: !!firstNode?.stat?.net,
                netType: typeof firstNode?.stat?.net,
                netKeys: firstNode?.stat?.net ? Object.keys(firstNode.stat.net) : [],
                fullNode: firstNode
            });
        }
        
        // 调用原始函数
        return window.originalHandleWsMessage(message, fromCache);
    };
    
    console.log('✅ WebSocket消息拦截器已安装');
}

// 检查DataProcessor的验证逻辑
if (typeof window.DataProcessor !== 'undefined') {
    console.log('🔍 [调试] DataProcessor状态:', {
        exists: true,
        hasValidateNodeData: typeof window.DataProcessor.validateNodeData === 'function',
        hasValidateMessage: typeof window.DataProcessor.validateMessage === 'function',
        hasProcessMessage: typeof window.DataProcessor.processMessage === 'function'
    });
    
    // 拦截DataProcessor的验证函数
    if (typeof window.originalValidateNodeData === 'undefined') {
        window.originalValidateNodeData = window.DataProcessor.validateNodeData;
        
        window.DataProcessor.validateNodeData = function(data) {
            const result = window.originalValidateNodeData(data);
            
            console.log('🔍 [调试] DataProcessor验证结果:', {
                isValid: result.isValid,
                nodeCount: result.nodeCount,
                errors: result.errors,
                inputData: data,
                inputKeys: data ? Object.keys(data) : [],
                inputType: typeof data
            });
            
            return result;
        };
        
        console.log('✅ DataProcessor验证拦截器已安装');
    }
} else {
    console.log('❌ DataProcessor不存在');
}

// 检查当前的数据状态
console.log('🔍 [调试] 当前数据状态:', {
    hasLastNodeData: !!window.lastNodeData,
    lastNodeDataKeys: window.lastNodeData ? Object.keys(window.lastNodeData) : [],
    lastNodeDataSize: window.lastNodeData ? Object.keys(window.lastNodeData).length : 0,
    hasSharedClient: !!window.sharedClient,
    hasStatsWs: !!window.statsWs
});

// 手动触发数据请求（如果可能）
if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
    console.log('🔄 [调试] 手动请求最新数据...');
    window.sharedClient.requestLastData();
}

console.log('=== 调试脚本安装完成 ===');
console.log('请等待WebSocket消息，查看控制台输出');
