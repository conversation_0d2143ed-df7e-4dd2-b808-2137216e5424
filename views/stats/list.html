{%set title = "节点状态"%}
{%extends "../base.html"%}
{%block content%}

<!-- 主容器 - 移除多余的容器，统一使用base.html中的容器 -->
<div id="server-view-container">
    <!-- 仪表盘区域 -->
    {% include "stats/dashboard.html" %}

    <!-- 服务器列表区域 -->
    <div class="mt-4">
    <!-- 服务器卡片网格 -->
    <div class="mt-3 relative min-h-[200px] mb-8" id="groups-container"
         data-group="all"
         data-status="ALL"
         data-expiry=""
         data-region=""
         data-tags="">
        {% macro server_card(sid, node, group_id='all') %}
        <div class="server-card card-border card-hover px-4 py-1.5 {%if node.stat==0 or node.stat.offline%}offline{%else%}online{%endif%} {% if setting.personalization and setting.personalization.blur and setting.personalization.blur.enabled %}blur-enabled{% endif %} {% if setting.personalization and setting.personalization.card and setting.personalization.card.backgroundImage and setting.personalization.card.backgroundImage.enabled %}has-bg-image{% endif %} relative"
              data-group="{{node.group_id if node.group_id else 'nogroup'}}"
              data-sid="{{sid}}"
              data-top="{{node.top}}"
              data-cpu="{{(node.stat.cpu.multi * 100)|float|round(2) if node.stat and node.stat.cpu else 0}}"
              data-memory="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(2) if node.stat and node.stat.mem.virtual else 0}}"
              data-total-traffic="{{node.traffic_stats.used if node.traffic_stats else 0}}"
              data-upload="{{node.stat.net.delta.out if node.stat and node.stat.net else 0}}"
              data-download="{{node.stat.net.delta.in if node.stat and node.stat.net else 0}}"
              data-expiration="{{node.expire_time if node.expire_time else 0}}"
              data-uptime="{{node.stat.host.uptime if node.stat and node.stat.host and node.stat.host.uptime else 0}}"
              data-region="{% if node.data.location and node.data.location.code %}{{node.data.location.code}}{% elif node.data.location and node.data.location.country and node.data.location.country.code %}{{node.data.location.country.code}}{% else %}UNKNOWN{% endif %}"
              data-status="{% if node.stat and not node.stat.offline %}ONLINE{% else %}OFFLINE{% endif %}"
              data-expiry-days="{{node.days_until_expiry if node.days_until_expiry else 999}}">

            <!-- 离线状态遮罩 - 始终渲染但通过CSS控制显示 -->
            <div class="absolute inset-0 flex items-center justify-center z-10 pointer-events-none {% if node.stat and not node.stat.offline %}hidden{% endif %}" id="{{sid}}_OFFLINE_CONTAINER">
                <div class="flex items-center px-3 py-1.5 text-sm font-medium text-slate-700 dark:text-slate-200 bg-slate-300/90 dark:bg-slate-700/90 rounded-lg shadow-lg pointer-events-auto" id="{{sid}}_OFFLINE_BADGE">
                    <i class="ti ti-power mr-1.5"></i>
                    <span id="{{sid}}_LAST_ONLINE" class="font-medium" data-last-online="{{node.last_online|default(0)}}">
                        {% if node.last_online %}
                            {{ node.last_online|timestamp_to_date }}前离线
                        {% else %}
                            离线
                        {% endif %}
                    </span>
                </div>
            </div>

            <!-- PC端：真正的单行水平布局 (lg以上屏幕) -->
            <div class="hidden lg:flex items-center justify-between w-full py-2 px-0 gap-4">
                <!-- 区域1：服务器基本信息 (左侧固定) -->
                <div class="flex items-center gap-2 min-w-0 flex-shrink-0" style="min-width: 200px;">
                    <!-- 拖拽手柄 (仅管理员) -->
                    {%if admin%}
                    <div class="drag-handle cursor-grab opacity-0 hover:opacity-50 transition-opacity duration-200" title="拖动排序">
                        <i class="ti ti-grip-vertical text-slate-500 dark:text-slate-400 text-sm"></i>
                    </div>
                    {%endif%}
                    
                    <!-- 状态指示器 -->
                    <div class="w-1.5 h-1.5 rounded-full flex-shrink-0 {% if node.stat and node.stat != 0 and not node.stat.offline %}bg-green-500{% else %}bg-red-500{% endif %}" 
                         id="{{sid}}_status_indicator"></div>
                    
                    <!-- 国旗 -->
                    {% if node.data.location and node.data.location.code %}
                        <span class="country-flag text-sm flex-shrink-0" title="{{node.data.location.code}}">
                            {% include "stats/flag.html" %}
                        </span>
                    {% endif %}
                    
                    <!-- 服务器名称 -->
                    <h3 class="text-xs font-normal truncate text-slate-700 dark:text-slate-200 min-w-0">
                        <a href="/stats/{{sid}}/" class="card-title-link">{{node.name}}</a>
                    </h3>
                </div>

                <!-- 区域2：性能指标 (弹性空间) -->
                <div class="flex items-center gap-4 flex-1">
                    <!-- CPU -->
                    <div class="flex items-center gap-1">
                        <i class="ti ti-cpu text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs text-slate-500 dark:text-slate-400">CPU:</span>
                        <span class="text-xs font-normal tabular-nums text-slate-700 dark:text-slate-300 text-right inline-block" id="{{sid}}_CPU"
                             data-cpu="{{(node.stat.cpu.multi * 100)|float|round(2) if node.stat and node.stat.cpu else 0}}"
                             style="min-width: 45px;">
                            {{(node.stat.cpu.multi * 100)|float|round(1) if node.stat and node.stat.cpu else 0}}%
                        </span>
                    </div>
                    
                    <!-- 内存 -->
                    <div class="flex items-center gap-1">
                        <i class="ti ti-device-sd-card text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs text-slate-500 dark:text-slate-400">内存:</span>
                        <span class="text-xs font-normal tabular-nums text-slate-700 dark:text-slate-300 text-right inline-block" id="{{sid}}_MEM"
                             data-memory="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(2) if node.stat and node.stat.mem.virtual else 0}}"
                             style="min-width: 45px;">
                            {{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(1) if node.stat and node.stat.mem.virtual else 0}}%
                        </span>
                    </div>
                    
                    <!-- 硬盘 -->
                    <div class="flex items-center gap-1">
                        <i class="ti ti-database text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs text-slate-500 dark:text-slate-400">硬盘:</span>
                        <span class="text-xs font-normal text-slate-700 dark:text-slate-300 text-right inline-block" id="{{sid}}_DISK_USAGE"
                             data-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}{% else %}0{% endif %}"
                             data-total-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ node.stat.disk.total }}{% else %}0{% endif %}"
                             style="min-width: 45px;">
                            {% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}
                                {{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}%
                            {% else %}
                                N/A
                            {% endif %}
                        </span>
                    </div>
                </div>

                <!-- 区域3：网络指标 (弹性空间) -->
                <div class="flex items-center gap-3 flex-1">
                    <!-- 下载速度容器 -->
                    <div class="flex items-center justify-between" style="min-width: 120px;">
                        <i class="ti ti-arrow-down text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs font-normal tabular-nums text-slate-700 dark:text-slate-300 text-right" 
                              style="min-width: 105px;" id="{{sid}}_NET_IN"
                              data-download="{{node.stat.net.delta.in if node.stat and node.stat.net else 0}}">
                            0 Kbps
                        </span>
                    </div>
                    
                    <!-- 上传速度容器 -->
                    <div class="flex items-center justify-between" style="min-width: 120px;">
                        <i class="ti ti-arrow-up text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs font-normal tabular-nums text-slate-700 dark:text-slate-300 text-right"
                              style="min-width: 105px;" id="{{sid}}_NET_OUT"
                              data-upload="{{node.stat.net.delta.out if node.stat and node.stat.net else 0}}">
                            0 Kbps
                        </span>
                    </div>
                </div>

                <!-- 区域4：状态和操作 (右侧固定) -->
                <div class="flex items-center gap-3 flex-shrink-0" style="min-width: 150px;">
                    <!-- 延迟 -->
                    <div class="flex items-center gap-1">
                        <i class="ti ti-arrows-horizontal text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs font-normal text-slate-700 dark:text-slate-300 text-right inline-block" id="{{sid}}_LATENCY"
                              style="min-width: 60px;">--- ms</span>
                    </div>
                    
                    <!-- 到期时间 -->
                    <div class="flex items-center gap-1">
                        <i class="ti ti-calendar text-xs text-slate-500 dark:text-slate-400"></i>
                        <span class="text-xs font-normal text-slate-700 dark:text-slate-300 text-right inline-block" id="{{sid}}_EXPIRE_TIME"
                              data-expire="{{node.expire_time|default(0)}}"
                              style="min-width: 80px;">
                            {{node.expire if node.expire else '--'}}
                        </span>
                    </div>
                    
                    <!-- 编辑按钮 (仅管理员) -->
                    {%if admin%}
                    <a class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300" href="/admin/servers/{{sid}}/">
                        <i class="ti ti-edit text-xs"></i>
                    </a>
                    {%endif%}
                </div>
            </div>

            <!-- 移动端：保持原有垂直布局 (lg以下屏幕) -->
            <div class="flex lg:hidden flex-col px-0 py-1.5">
                <!-- 卡片标题行 -->
                <div class="flex items-center mb-1">
                    <!-- 拖拽手柄 -->
                    {%if admin%}
                    <div class="drag-handle mr-2 cursor-grab opacity-0 hover:opacity-50 transition-opacity duration-200" title="拖动排序">
                        <i class="ti ti-grip-vertical text-slate-500 dark:text-slate-400 text-lg"></i>
                    </div>
                    {%endif%}
                    
                    <div class="w-1.5 h-1.5 rounded-full flex-shrink-0 {% if node.stat and node.stat != 0 and not node.stat.offline %}bg-green-500{% else %}bg-red-500{% endif %}"
                         id="{{sid}}_status_indicator_mobile"
                         title="{% if node.stat and node.stat != 0 and not node.stat.offline %}在线{% else %}离线{% endif %}">
                    </div>

                    <!-- 国家/地区标志 -->
                    {% if node.data.location and node.data.location.code %}
                        <span class="country-flag text-base flex-shrink-0 ml-1.5 mr-1.5" title="{{node.data.location.code}}">
                            {% include "stats/flag.html" %}
                        </span>
                    {% else %}
                        <span class="country-flag text-base flex-shrink-0 ml-1.5 mr-1.5" title="未知位置">
                            {% include "stats/flag.html" %}
                        </span>
                    {% endif %}

                    <!-- 服务器名称 -->
                    <h3 class="font-medium truncate text-sm text-slate-700 dark:text-slate-200 whitespace-nowrap">
                        <a href="/stats/{{sid}}/" class="card-title-link">{{node.name}}</a>
                    </h3>

                    <!-- 编辑按钮 -->
                    {%if admin%}
                    <a class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300 ml-auto"
                       href="/admin/servers/{{sid}}/">
                        <i class="ti ti-edit text-xs"></i>
                    </a>
                    {%endif%}
                </div>

                <!-- CPU和内存指标行 -->
                <div class="flex items-center mb-1">
                    <!-- CPU指标和进度条 -->
                    <div class="flex items-center mr-2 text-xs text-slate-500 dark:text-slate-400">
                        <i class="ti ti-cpu text-xs mr-1"></i>
                        <span id="{{sid}}_CPU_MOBILE" class="tabular-nums whitespace-nowrap"
                              data-cpu="{{(node.stat.cpu.multi * 100)|float|round(2) if node.stat and node.stat.cpu else 0}}">
                            {{(node.stat.cpu.multi * 100)|float|round(1) if node.stat and node.stat.cpu else 0}}%
                        </span>
                    </div>
                    <div class="h-1 bg-slate-700/50 dark:bg-slate-600/50 rounded-full overflow-hidden flex-1 mr-2 progress-bar-bg">
                        <div id="{{sid}}_CPU_progress" class="h-full bg-primary-500/50 rounded-full"
                             data-width="{{(node.stat.cpu.multi * 100)|float|round(1) if node.stat and node.stat.cpu else 0}}"></div>
                    </div>

                    <!-- 内存指标和进度条 -->
                    <div class="flex items-center mr-2 text-xs text-slate-500 dark:text-slate-400">
                        <i class="ti ti-device-sd-card text-xs mr-1"></i>
                        <span id="{{sid}}_MEM_MOBILE" class="tabular-nums whitespace-nowrap"
                              data-memory="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(2) if node.stat and node.stat.mem.virtual else 0}}">
                            {{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(1) if node.stat and node.stat.mem.virtual else 0}}%
                        </span>
                    </div>
                    <div class="h-1 bg-slate-700/50 dark:bg-slate-600/50 rounded-full overflow-hidden flex-1 progress-bar-bg">
                        <div id="{{sid}}_MEM_progress" class="h-full bg-purple-500/50 rounded-full"
                             data-width="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(1) if node.stat and node.stat.mem.virtual else 0}}"></div>
                    </div>
                </div>

                <!-- 网络指标行 -->
                <div class="flex justify-between mb-1">
                    <!-- 下载速度 -->
                    <div class="flex items-center">
                        <i class="ti ti-arrow-down text-slate-500 dark:text-slate-400 text-xs mr-1"></i>
                        <span id="{{sid}}_NET_IN_MOBILE" class="text-xs text-slate-500 dark:text-slate-400 tabular-nums whitespace-nowrap"
                            data-download="{{node.stat.net.delta.in if node.stat and node.stat.net else 0}}">
                            0 Kbps
                        </span>
                    </div>

                    <!-- 上传速度 -->
                    <div class="flex items-center">
                        <i class="ti ti-arrow-up text-slate-500 dark:text-slate-400 text-xs mr-1"></i>
                        <span id="{{sid}}_NET_OUT_MOBILE" class="text-xs text-slate-500 dark:text-slate-400 tabular-nums whitespace-nowrap"
                            data-upload="{{node.stat.net.delta.out if node.stat and node.stat.net else 0}}">
                            0 Kbps
                        </span>
                    </div>

                    <!-- 下载总量 -->
                    <div class="flex items-center">
                        <i class="ti ti-download text-slate-500 dark:text-slate-400 text-xs mr-1"></i>
                        <span id="{{sid}}_NET_IN_TOTAL" class="text-xs text-slate-500 dark:text-slate-400 tabular-nums whitespace-nowrap"
                            data-total-download="{{node.stat.net.total.in if node.stat and node.stat.net else 0}}">
                            0 GB
                        </span>
                    </div>

                    <!-- 上传总量 -->
                    <div class="flex items-center">
                        <i class="ti ti-upload text-slate-500 dark:text-slate-400 text-xs mr-1"></i>
                        <span id="{{sid}}_NET_OUT_TOTAL" class="text-xs text-slate-500 dark:text-slate-400 tabular-nums whitespace-nowrap"
                            data-total-upload="{{node.stat.net.total.out if node.stat and node.stat.net else 0}}">
                            0 GB
                        </span>
                    </div>
                </div>

                <!-- 底部信息行 -->
                <div class="mt-1 pt-1 border-t border-slate-700/30 dark:border-slate-600/30">
                    <div class="flex items-center justify-start gap-2 w-full">
                        <!-- 到期时间 -->
                        <div class="flex items-center gap-1 text-xs px-1.5 py-0.5 rounded bg-slate-100 dark:bg-slate-800/60">
                            <i class="ti ti-calendar" style="font-size: 12px;"></i>
                            <span id="{{sid}}_EXPIRE_TIME_MOBILE" class="text-xs font-normal"
                                  data-expire="{{node.expire_time|default(0)}}">
                                {{node.expire if node.expire else '- -'}}
                            </span>
                        </div>
                        
                        <!-- 硬盘使用率 -->
                        <div class="flex items-center gap-1 text-xs px-1.5 py-0.5 rounded bg-slate-100 dark:bg-slate-800/60">
                            <i class="ti ti-database" style="font-size: 12px;"></i>
                            <span id="{{sid}}_DISK_USAGE_MOBILE" class="text-xs font-normal"
                                 data-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}{% else %}0{% endif %}"
                                 data-total-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ node.stat.disk.total }}{% else %}0{% endif %}">
                               {% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}
                                   {{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}%
                               {% else %}
                                   N/A
                               {% endif %}
                            </span>
                        </div>
                        
                        <!-- 延迟 -->
                        <div class="flex items-center gap-1 text-xs px-1.5 py-0.5 rounded bg-slate-100 dark:bg-slate-800/60">
                            <i class="ti ti-arrows-horizontal" style="font-size: 12px;"></i>
                            <span id="{{sid}}_LATENCY_MOBILE" class="text-xs font-normal">--- ms</span>
                        </div>
                        
                        <!-- 丢包率 -->
                        <div class="flex items-center gap-1 text-xs px-1.5 py-0.5 rounded bg-slate-100 dark:bg-slate-800/60">
                            <i class="ti ti-alert-triangle" style="font-size: 12px;"></i>
                            <span id="{{sid}}_PACKET_LOSS_MOBILE" class="text-xs font-normal">--- %</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endmacro %}

        <!-- 分组视图容器 - 使用flex布局 -->
        <div class="group-view transition-all duration-300 ease-in-out" data-group="all">
            <div id="list-grid-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 gap-2">
                {%for sid, node in stats%}
                    {{ server_card(sid, node) }}
                {% endfor %}
            </div>
        </div>
    </div>
{%endblock%}

{%block js%}
<style type="text/tailwindcss">
/* 只保留特定于此页面的样式 */
.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* 拖拽手柄样式 */
.server-card:hover .drag-handle {
    opacity: 0.5;
}

.server-card.sortable-chosen .drag-handle,
.server-card.sortable-drag .drag-handle {
    opacity: 1 !important;
    cursor: grabbing !important;
}

/* 拖拽动画增强 */
.sortable-ghost {
    opacity: 0.4;
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed rgba(99, 102, 241, 0.5);
}

.sortable-drag {
    opacity: 0.9 !important;
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.sortable-chosen {
    opacity: 0.8;
}

/* 拖拽时的过渡动画 - 调整为不影响拖拽的属性 */
.server-card {
    transition: background-color 0.15s ease, border-color 0.15s ease;
}

/* 拖拽时禁用过渡效果，避免抖动 */
.dragging-active .server-card {
    transition: none;
}

/* 撤销/重做按钮样式 */
#undo-sort-btn:not(:disabled):hover,
#redo-sort-btn:not(:disabled):hover {
    opacity: 1;
    background-color: rgba(99, 102, 241, 0.1);
}

/* 拖拽交互反馈样式 */
.server-card.drop-target {
    background-color: rgba(99, 102, 241, 0.1) !important;
    border: 2px solid rgba(99, 102, 241, 0.5) !important;
    /* 移除transform避免抖动 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    /* 添加过渡效果使变化更平滑 */
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.server-card.dragging-item {
    opacity: 0.9;
    transform: scale(1.02);
    z-index: 1000;
    /* 防止拖拽时的过渡效果 */
    transition: none !important;
}

/* 交换动画 */
.server-card.swap-animation {
    animation: swapPulse 0.3s ease-in-out;
}

@keyframes swapPulse {
    0% {
        background-color: transparent;
        opacity: 1;
    }
    50% {
        background-color: rgba(99, 102, 241, 0.15);
        opacity: 0.9;
    }
    100% {
        background-color: transparent;
        opacity: 1;
    }
}

/* 拖拽时的指针 */
.dragging-active .server-card {
    cursor: grab;
}

.dragging-active .server-card.sortable-drag {
    cursor: grabbing !important;
}

/* 拖拽预览位置指示器 */
.sortable-swap-highlight {
    background-color: rgba(99, 102, 241, 0.2) !important;
    border: 2px dashed rgba(99, 102, 241, 0.6) !important;
}
</style>

<!-- 核心脚本 -->
<script src="/js/libs/sortable.min.js"></script>
<script>
// Sortable已加载，可以直接使用
if (typeof Sortable !== 'undefined') {
    console.log('✅ Sortable插件已加载');
} else {
    console.warn('❌ Sortable插件未加载');
}
</script>
<script src="/js/sort.js"></script>

<script>
// 监听个性化设置更新事件
document.addEventListener('personalization-settings-updated', function(event) {
    const settings = event.detail;
    if (!settings) return;

    // 更新壁纸设置
    if (window.settingsSync && window.settingsSync.applyWallpaperSettings) {
        window.settingsSync.applyWallpaperSettings(settings);
    }
});

// 初始化时从 sessionStorage 读取设置
document.addEventListener('DOMContentLoaded', function() {
    try {
        const storedSettings = sessionStorage.getItem('personalization-settings');
        if (storedSettings) {
            const settings = JSON.parse(storedSettings);
            const event = new CustomEvent('personalization-settings-updated', {
                detail: settings
            });
            document.dispatchEvent(event);
        }
    } catch (e) {
        console.warn('无法读取存储的个性化设置:', e);
    }

    // 设置CPU进度条
    document.querySelectorAll('[id$="_CPU_progress"]').forEach(el => {
        const width = el.getAttribute('data-width');
        if (width) el.style.width = width + '%';
    });

    // 设置内存进度条
    document.querySelectorAll('[id$="_MEM_progress"]').forEach(el => {
        const width = el.getAttribute('data-width');
        if (width) el.style.width = width + '%';
    });

    // 确保移动端的网络数据元素与桌面端同步
    document.querySelectorAll('[id$="_NET_IN"]').forEach(el => {
        const mobileEl = document.getElementById(el.id + '_MOBILE');
        if (mobileEl) {
            mobileEl.setAttribute('data-download', el.getAttribute('data-download') || '0');
        }
    });

    document.querySelectorAll('[id$="_NET_OUT"]').forEach(el => {
        const mobileEl = document.getElementById(el.id + '_MOBILE');
        if (mobileEl) {
            mobileEl.setAttribute('data-upload', el.getAttribute('data-upload') || '0');
        }
    });

    // 确保footer可见
    const container = document.getElementById('groups-container');
    if (container) {
        // 在分组切换时调整容器高度
        const updateGroupsHeight = () => {
            const activeGroup = document.querySelector('.group-view:not(.hidden)');
            if (activeGroup) {
                container.style.minHeight = activeGroup.scrollHeight + 'px';
            }
        };

        // 监听标签切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                setTimeout(updateGroupsHeight, 300); // 在过渡效果结束后调整高度
            });
        });

        // 初始化时调整高度
        setTimeout(updateGroupsHeight, 100);
    }
});
</script>

<!-- 通用筛选器样式和脚本 -->
<link rel="stylesheet" href="/css/universal-filters.css">
<script type="module" src="/js/universal-filter-manager.js"></script>
<!-- URL 参数筛选处理 -->
<script src="/js/url-filter-handler.js"></script>

{%endblock%}