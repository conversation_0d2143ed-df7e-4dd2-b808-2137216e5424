{%set title = "节点状态"%}
{%extends "../base.html"%}
{%block content%}

    <!-- 仪表盘区域 -->
    {% include "stats/dashboard.html" %}

<!-- 主容器 - 移除多余的容器，使用base.html中的容器即可 -->
<!-- 隐藏的拖拽开关元素，用于兼容sort.js -->
<input type="checkbox" id="enable-drag-sort" style="display: none;" checked="true" />



    <!-- 分组内容区域 -->
    <div class="mt-4 mb-8" id="groups-container"
         data-group="all"
         data-status="ALL"
         data-expiry=""
         data-region=""
         data-tags="">
        <!-- 全部节点视图 -->
        <div class="group-view transition-all duration-300 ease-in-out" data-group="all">
            <div id="card-grid-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-3 md:gap-4"> <!-- 增加卡片间距，大屏幕更宽松 -->
                {%for sid, node in stats%}

                <div class="server-card theme-border card-hover {% if not node.stat or node.stat.offline %}offline{% endif %} relative rounded-xl shadow-lg"
                     data-group="{{node.group_id if node.group_id else 'default'}}"
                     data-sid="{{sid}}"
                     data-top="{{node.top}}"
                     data-cpu="{{(node.stat.cpu.multi * 100)|float|round(2) if node.stat and node.stat.cpu else 0}}"
                     data-memory="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(2) if node.stat and node.stat.mem.virtual else 0}}"
                     data-total-traffic="{{node.traffic_stats.used if node.traffic_stats else 0}}"
                     data-upload="{{node.stat.net.delta.out if node.stat and node.stat.net else 0}}"
                     data-download="{{node.stat.net.delta.in if node.stat and node.stat.net else 0}}"
                     data-expiration="{{node.expire_time if node.expire_time else 0}}"
                     data-region="{% if node.data.location and node.data.location.code %}{{node.data.location.code}}{% elif node.data.location and node.data.location.country and node.data.location.country.code %}{{node.data.location.country.code}}{% else %}UNKNOWN{% endif %}"
                     data-status="{% if node.stat and not node.stat.offline %}ONLINE{% else %}OFFLINE{% endif %}"
                     data-expiry-days="{{node.days_until_expiry if node.days_until_expiry else 999}}">

                    <!-- 离线状态遮罩 - 始终渲染但通过CSS控制显示 -->
                    <div class="absolute inset-0 flex items-center justify-center z-10 pointer-events-none {% if node.stat and not node.stat.offline %}hidden{% endif %}" id="{{sid}}_OFFLINE_CONTAINER">
                        <div class="flex items-center px-3 py-1.5 text-sm font-medium text-slate-700 dark:text-slate-200 bg-slate-300/90 dark:bg-slate-700/90 rounded-lg shadow-lg pointer-events-auto" id="{{sid}}_OFFLINE_BADGE">
                            <i class="ti ti-power mr-1.5"></i>
                            <span id="{{sid}}_LAST_ONLINE" class="font-medium" data-last-online="{{node.last_online|default(0)}}">
                                {% if node.last_online %}
                                    {{ node.last_online|timestamp_to_date }}前离线
                                {% else %}
                                    离线
                                {% endif %}
                            </span>
                        </div>
                    </div>

                    <div class="flex flex-col h-full">
                      <div class="server-card-content pt-3 px-5 pb-5">
                        <!-- 服务器名称和操作按钮 - 优化布局 -->
                        <div class="server-card-header">
                          <!-- 拖拽手柄 - 顶部居中，绝对定位不占用空间 -->
                          {%if admin%}
                          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 z-10">
                              <div class="drag-handle cursor-grab opacity-0 hover:opacity-50 transition-opacity duration-200" title="拖动排序">
                                  <i class="ti ti-grip-vertical text-gray-400 text-sm transform rotate-90"></i>
                              </div>
                          </div>
                          {%endif%}
                          
                          <div class="server-card-handle flex items-center justify-between mb-2 group"> {# 保持底部间距 mb-2 #}
                            <div class="flex items-center gap-1 overflow-hidden max-w-[85%]"> <!-- 限制宽度以留出空间给操作按钮 -->
                                <!-- 国家信息和服务器名称 -->
                                <div class="flex items-center gap-1 overflow-hidden">
                                    <!-- 国家图标 -->
                                    <div class="flex-shrink-0">
                                        {% if node.data.location and node.data.location.code %}
                                            <span class="country-flag text-base inline-flex" title="{{node.data.location.code}}">
                                                {% include "stats/flag.html" %}
                                            </span>
                                        {% else %}
                                            <span class="country-flag text-base inline-flex" title="未知位置">
                                                {% include "stats/flag.html" %}
                                            </span>
                                        {% endif %}
                                    </div>

                                    <!-- 服务器名称和状态 -->
                                    <div class="flex items-center overflow-hidden">
                                        <h3 class="server-name font-medium text-xs flex items-center gap-1 flex-1" title="{{node.name}}">
                                            <span class="truncate">
                                                {% if node.data.location and node.data.location.code %}
                                                    <a href="/stats/{{sid}}/" class="card-title-link text-xs">{{node.data.location.code}}- {{node.name}}</a>
                                                {% else %}
                                                    <a href="/stats/{{sid}}/" class="card-title-link text-xs">{{node.name}}</a>
                                                {% endif %}
                                            </span>
                                            <!-- 详情入口图标 -->
                                            <a href="/stats/{{sid}}/" class="inline-flex items-center justify-center w-3.5 h-3.5 rounded-full bg-slate-100 dark:bg-slate-800 hover:bg-blue-100 dark:hover:bg-blue-900 text-slate-500 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 flex-shrink-0" title="查看详情">
                                                <i class="ti ti-external-link text-[10px] leading-none"></i>
                                            </a>
                                        </h3>

                                        <!-- 系统版本图标 -->
                                        <div class="flex-shrink-0 ml-1.5 system-icon-container" 
                                             data-platform="{% if node.stat and node.stat.host and node.stat.host.platform %}{{node.stat.host.platform}}{% else %}{% endif %}">
                                            <!-- 图标将由 system-icons.js 动态填充 -->
                                            <i class="fas fa-server text-base text-gray-400"></i>
                                        </div>

                                        <!-- 状态指示器 -->
                                        <div class="flex-shrink-0 ml-1.5" id="{{sid}}_status_container">
                                            <div class="w-2 h-2 rounded-full {% if node.stat and not node.stat.offline %}bg-green-500{% else %}bg-red-500{% endif %}"
                                                 id="{{sid}}_status_indicator"
                                                 title="{% if node.stat and not node.stat.offline %}在线{% else %}离线{% endif %}"
                                                 data-online="{% if node.stat and not node.stat.offline %}true{% else %}false{% endif %}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex-shrink-0 flex gap-1">
                                {%if admin%}
                                <a class="text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 p-0.5" href="/admin/servers/{{sid}}/"> {# 调整管理员按钮颜色 #}
                                    <i class="ti ti-edit md-14"></i>
                                </a>
                                {%endif%}
                            </div>
                          </div> <!-- End server-card-handle -->
                        </div> <!-- End server-card-header -->

                        <!-- 系统信息 - 三大主要区域布局 -->
                        <div class="space-y-3"> <!-- 区域间距 -->
                            
                            <!-- 第一区域：系统性能指标 -->
                            <div class="performance-metrics">
                              <div class="grid grid-cols-2 gap-3">
                                <!-- CPU 使用率 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-cpu md-14"></i>
                                            <span class="text-xs font-medium">CPU</span>
                                        </div>
                                        <span id="{{sid}}_CPU" class="text-xs font-normal text-slate-700 dark:text-slate-100"
                                              data-cpu="{{(node.stat.cpu.multi * 100)|float|round(2) if node.stat and node.stat.cpu else 0}}">
                                            {{node.cpu|default('N/A')}}<span class="metric-unit">%</span>
                                        </span>
                                    </div>
                                    <div class="h-1.5 bg-gray-200 dark:bg-gray-700/50 rounded-full overflow-hidden">
                                        <div id="{{sid}}_CPU_progress" class="h-full bg-blue-500 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                </div>

                                <!-- 内存使用率 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-device-sd-card md-14"></i>
                                            <span class="text-xs font-medium">内存</span>
                                        </div>
                                        <span id="{{sid}}_MEM" class="text-xs font-normal text-slate-700 dark:text-slate-100"
                                              data-memory="{{((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100)|float|round(2) if node.stat and node.stat.mem.virtual else 0}}">
                                            {{node.memory|default('N/A')}}<span class="metric-unit">%</span>
                                        </span>
                                    </div>
                                    <div class="h-1.5 bg-gray-200 dark:bg-gray-700/50 rounded-full overflow-hidden">
                                        <div id="{{sid}}_MEM_progress" class="h-full bg-purple-500 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                </div>
                              </div>
                            </div>

                            <!-- 第二区域：存储与流量指标 -->
                            <div class="storage-traffic-metrics">
                              <div class="grid grid-cols-2 gap-3">
                                <!-- 磁盘使用率 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-database md-14"></i>
                                            <span class="text-xs font-medium">磁盘</span>
                                        </div>
                                        <span id="{{sid}}_DISK_USAGE" class="text-xs font-normal text-slate-700 dark:text-slate-100"
                                              data-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}{% else %}0{% endif %}"
                                              data-total-disk="{% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}{{ node.stat.disk.total }}{% else %}0{% endif %}">
                                            {% if node.stat and node.stat.disk and node.stat.disk.total > 0 %}
                                                {{ ((node.stat.disk.used / node.stat.disk.total) * 100)|float|round(1) }}<span class="metric-unit">%</span>
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="h-1.5 bg-gray-200 dark:bg-gray-700/50 rounded-full overflow-hidden">
                                        <div id="{{sid}}_DISK_BAR" class="h-full bg-amber-500 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                </div>

                                <!-- 流量使用 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-chart-pie md-14"></i>
                                            <span class="text-xs font-medium">月流量</span>
                                        </div>
                                        <!-- 流量数值显示 -->
                                        <span id="{{sid}}_TRAFFIC" class="text-xs font-normal text-slate-700 dark:text-slate-100"
                                              data-sid="{{sid}}"
                                              data-traffic-used="{% if node.traffic_stats %}{{node.traffic_stats.used}}{% else %}0{% endif %}"
                                              data-traffic-limit="{% if node.traffic_stats %}{{node.traffic_stats.limit}}{% else %}0{% endif %}">
                                            {% if node.traffic_stats %}
                                                计算中...
                                            {% else %}
                                                0B
                                            {% endif %}
                                        </span>
                                    </div>
                                    <!-- 流量条形图（有限制和无限制都显示） -->
                                    <div class="h-1.5 bg-gray-200 dark:bg-gray-700/50 rounded-full overflow-hidden">
                                        <!-- 有限制流量的进度条 -->
                                        <div class="traffic-bar relative h-full rounded-full transition-all duration-300" data-sid="{{sid}}"
                                             data-traffic-ratio="{% if node.traffic_stats and node.traffic_stats.limit > 0 %}{{node.traffic_stats.ratio|round(1)}}{% else %}0{% endif %}"
                                             style="width: 0%; background-color: #06b6d4;">
                                            <!-- 无限制流量的文字显示 -->
                                            <div class="traffic-unlimited-text absolute inset-0 flex items-center justify-center text-[10px] leading-none font-medium text-slate-600 dark:text-slate-300"
                                                 data-sid="{{sid}}" style="display: none; transform: translateY(-3px);">
                                                ∞
                                            </div>
                                        </div>
                                    </div>
                                </div>
                              </div>
                            </div>

                            <!-- 第三区域：网络实时指标 -->
                            <div class="network-metrics">
                              <div class="grid grid-cols-2 gap-2">
                                <!-- 下载速度和总量 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center gap-2">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400 flex-shrink-0">
                                            <i class="ti ti-arrow-down md-12"></i>
                                        </div>
                                        <span id="{{sid}}_NET_IN" class="text-sm font-semibold text-green-600 dark:text-green-400 text-right min-w-0 flex-shrink-0">NaN</span>
                                    </div>
                                    <div class="flex justify-between items-center mt-1 gap-2">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400 flex-shrink-0">
                                            <i class="ti ti-cloud-download md-12"></i>
                                        </div>
                                        <span id="{{sid}}_NET_IN_TOTAL" class="text-xs text-slate-500 dark:text-slate-300 text-right min-w-0 flex-shrink-0">NaN</span>
                                    </div>
                                </div>

                                <!-- 上传速度和总量 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center gap-2">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400 flex-shrink-0">
                                            <i class="ti ti-arrow-up md-12"></i>
                                        </div>
                                        <span id="{{sid}}_NET_OUT" class="text-sm font-semibold text-blue-600 dark:text-blue-400 text-right min-w-0 flex-shrink-0">NaN</span>
                                    </div>
                                    <div class="flex justify-between items-center mt-1 gap-2">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400 flex-shrink-0">
                                            <i class="ti ti-cloud-upload md-12"></i>
                                        </div>
                                        <span id="{{sid}}_NET_OUT_TOTAL" class="text-xs text-slate-500 dark:text-slate-300 text-right min-w-0 flex-shrink-0">NaN</span>
                                    </div>
                                </div>
                              </div>
                            </div>

                            <!-- 第四区域：网络延迟和丢包率 -->
                            <div class="network-quality-metrics border-t border-b border-gray-200 dark:border-gray-700 py-3 my-3">
                              <div class="grid grid-cols-2 gap-3">
                                <!-- 网络延迟 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-wifi md-14"></i>
                                            <span class="text-xs font-medium">延迟</span>
                                        </div>
                                        <span id="{{sid}}_LATENCY" class="text-xs font-normal text-slate-700 dark:text-slate-100" 
                                              data-latency="{% if node.latency %}{{node.latency}}{% else %}0{% endif %}">
                                            {% if node.latency %}
                                                {{node.latency|round(0)}}<span class="metric-unit">ms</span>
                                            {% else %}
                                                <span class="text-slate-400 dark:text-slate-500">获取中...</span>
                                            {% endif %}
                                        </span>
                                    </div>
                                    <!-- 延迟竖条图 -->
                                    <div class="latency-mini-chart" data-sid="{{sid}}">
                                        <!-- 竖条将由JavaScript动态生成 -->
                                    </div>
                                </div>

                                <!-- 丢包率 -->
                                <div class="metric-item">
                                    <div class="flex justify-between items-center mb-1">
                                        <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                            <i class="ti ti-signal-4g md-14"></i>
                                            <span class="text-xs font-medium">丢包率</span>
                                        </div>
                                        <span id="{{sid}}_PACKET_LOSS" class="text-xs font-normal text-slate-700 dark:text-slate-100" 
                                              data-packet-loss="{% if node.packet_loss is defined %}{{node.packet_loss}}{% else %}0{% endif %}">
                                            {% if node.packet_loss is defined %}
                                                {{node.packet_loss|round(1)}}<span class="metric-unit">%</span>
                                            {% else %}
                                                <span class="text-slate-400 dark:text-slate-500">获取中...</span>
                                            {% endif %}
                                        </span>
                                    </div>
                                    <!-- 丢包率竖条图 -->
                                    <div class="packet-loss-mini-chart" data-sid="{{sid}}">
                                        <!-- 竖条将由JavaScript动态生成 -->
                                    </div>
                                </div>
                              </div>
                            </div>

                            <!-- 底部次要信息标签区 -->
                            <div class="server-card-footer">
                              <div class="pt-2 mt-1 border-t border-gray-200 dark:border-gray-700/30">
                                <!-- 第一行：重要指标 -->
                                <div class="grid grid-cols-2 gap-3 mb-3">
                                    <!-- 到期时间 -->
                                    <div class="metric-item">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                                <i class="ti ti-calendar md-12"></i>
                                                <span class="text-xs font-medium">到期</span>
                                            </div>
                                            <span id="{{sid}}_EXPIRE_TIME" class="text-xs font-normal text-orange-600 dark:text-orange-400"
                                                  data-expire="{{node.expire_time|default(0)}}">
                                                {{node.expire}}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 在线时间 -->
                                    <div class="metric-item" {% if not node.stat or node.stat.offline %}style="display: none;"{% endif %}>
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
                                                <i class="ti ti-refresh md-12"></i>
                                                <span class="text-xs font-medium">在线</span>
                                            </div>
                                            <span id="{{sid}}_UPTIME" class="text-xs font-normal text-blue-600 dark:text-blue-400"
                                                  data-uptime="{{node.stat.host.uptime if node.stat and node.stat.host and node.stat.host.uptime else 0}}">
                                                {% if node.stat and node.stat.host and node.stat.host.uptime %}
                                                    计算中...
                                                {% else %}
                                                    --
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二行：用户自定义标签 -->
                                <div class="flex justify-between items-center min-h-[1.75rem] gap-1" id="{{sid}}_CUSTOM_TAGS" 
                                     data-tag-count="{{node.data.tags|length if node.data.tags else 0}}"
                                     {% if admin %}onclick="event.stopPropagation(); openQuickTagEditor('{{sid}}');" data-editable="true"{% else %}data-editable="false"{% endif %}>
                                    
                                    {% if node.data.tags and node.data.tags|length > 0 %}
                                        {% set tagCount = 0 %}
                                        {% for tag in node.data.tags %}
                                            {% if tagCount < 4 %}
                                        <span class="tag-item inline-flex items-center text-[11px] font-medium 
                                                     {% if node.data.tags|length == 1 %}px-2{% elif node.data.tags|length == 2 %}px-1.5{% elif node.data.tags|length == 3 %}px-1{% else %}px-0.5{% endif %} py-0.5 
                                                     {% if node.data.tags|length <= 3 %}min-w-[2rem]{% else %}min-w-[1.5rem]{% endif %} max-w-[3.5rem] rounded text-center 
                                                     whitespace-nowrap truncate transition-all
                                                     {% if admin %}cursor-pointer hover:opacity-80{% else %}cursor-default{% endif %}"
                                              title="{% if admin %}点击管理标签：{{tag.name}}{% else %}标签：{{tag.name}}{% endif %}"
                                              style="background-color: {{tag.color|default('#6b7280')}}15; 
                                                     border: 1px solid {{tag.color|default('#6b7280')}}30; 
                                                     color: {{tag.color|default('#6b7280')}};">
                                            {{tag.name}}
                                        </span>
                                            {% set tagCount = tagCount + 1 %}
                                            {% endif %}
                                        {% endfor %}
                                        
                                        <!-- 管理员添加按钮 -->
                                        {% if admin and node.data.tags|length < 4 %}
                                        <span class="tag-add inline-flex items-center text-[11px] font-medium
                                                     px-1.5 py-0.5 rounded border border-dashed 
                                                     border-slate-300 dark:border-slate-700 
                                                     text-slate-400 dark:text-slate-600 
                                                     cursor-pointer opacity-60 hover:opacity-100 transition-all"
                                              title="点击添加标签">
                                            <i class="ti ti-plus text-[10px] mr-0.5"></i>
                                            <span>添加</span>
                                        </span>
                                        {% endif %}
                                    {% else %}
                                        <!-- 无标签状态 -->
                                        {% if admin %}
                                        <span class="tag-add inline-flex items-center text-[11px] font-medium
                                                     px-1.5 py-0.5 rounded border border-dashed 
                                                     border-slate-300 dark:border-slate-700 
                                                     text-slate-400 dark:text-slate-600
                                                     cursor-pointer opacity-60 hover:opacity-100 transition-all"
                                              title="点击添加标签">
                                            <i class="ti ti-plus text-[10px] mr-0.5"></i>
                                            <span>添加标签</span>
                                        </span>
                                        {% else %}
                                        <span class="inline-flex items-center text-[11px] px-2 py-0.5 rounded
                                                     bg-slate-100 dark:bg-slate-800/50 text-slate-400">
                                            暂无标签
                                        </span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                              </div>
                            </div>
                        </div> <!-- End space-y-3 -->
                      </div> {# End server-card-content #}

                      <!-- 恢复并优化详细信息按钮 -->
                      <!-- 注释掉详细信息区域
                      <div class="mt-auto border-t border-gray-200 dark:border-gray-700/30"> {# Remove pb-1 #}
                          <a href="/stats/{{sid}}" class="server-card-detail-btn flex items-center justify-center w-full text-center py-2 bg-slate-50/50 dark:bg-slate-800/30 text-slate-500 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-700 dark:hover:text-slate-100 text-xs font-medium transition-colors duration-200"> 
                              <i class="ti ti-info-circle text-sm align-text-bottom mr-1"></i>
                              <span>详细信息</span>
                          </a>
                      </div>
                      -->
                    </div> <!-- End flex-col h-full -->
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 分组视图已移除 - 现在使用JavaScript动态显示/隐藏卡片来实现分组功能 -->


    </div>



{%endblock%}

{%block js%}
<!-- 所有JavaScript文件已在dashboard.html中加载，无需重复引用 -->

<script>
// Card页面分组功能验证（不重复初始化，tab-menu.js会自动初始化）
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Card页面分组功能检查...');
    
    // 检查关键函数是否已加载
    const requiredFunctions = ['applyGroupFilter'];
    const missing = requiredFunctions.filter(fn => typeof window[fn] !== 'function');
    
    if (missing.length === 0) {
        console.log('✅ 分组功能脚本加载完成');
    } else {
        console.error('❌ 分组功能脚本缺失:', missing);
    }
    
    // 验证DOM元素是否存在
    setTimeout(() => {
        const groupBtn = document.getElementById('group-dropdown-btn');
        const groupMenu = document.getElementById('group-dropdown-menu');
        const groupOptions = document.querySelectorAll('.group-option');
        
        console.log('🔍 分组DOM检查:');
        console.log('  - 分组按钮:', !!groupBtn);
        console.log('  - 分组菜单:', !!groupMenu);
        console.log('  - 分组选项数量:', groupOptions.length);
        
        if (groupOptions.length > 0) {
            console.log('✅ 分组DOM结构正常');
        } else {
            console.warn('⚠️ 未找到分组选项');
        }
    }, 500); // 等待DOM完全准备
});
</script>

<style>
/* 减小百分比符号和流量单位的字体大小 */
.metric-unit {
    font-size: 0.6em;
    font-weight: normal;
    vertical-align: baseline;
}

/* 移除过时的流量样式 - 现在使用统一的ID选择器，与其他指标保持一致 */

/* 拖拽手柄样式 */
.server-card:hover .drag-handle {
    opacity: 0.5;
}

.server-card.sortable-chosen .drag-handle,
.server-card.sortable-drag .drag-handle {
    opacity: 1 !important;
    cursor: grabbing !important;
}

/* 拖拽动画增强 */
.sortable-ghost {
    opacity: 0.4;
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed rgba(99, 102, 241, 0.5);
}

.sortable-drag {
    opacity: 0.9 !important;
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

/* 拖拽手柄旋转样式 */
.drag-handle i {
    transform: rotate(90deg) !important;
}

.sortable-chosen {
    opacity: 0.8;
}

/* 拖拽时的过渡动画 - 调整为不影响拖拽的属性 */
.server-card {
    transition: background-color 0.15s ease, border-color 0.15s ease;
}

/* 拖拽时禁用过渡效果，避免抖动 */
.dragging-active .server-card {
    transition: none;
}

/* 拖拽交互反馈样式 */
.server-card.drop-target {
    background-color: rgba(99, 102, 241, 0.1) !important;
    border: 2px solid rgba(99, 102, 241, 0.5) !important;
    /* 移除transform避免抖动 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    /* 添加过渡效果使变化更平滑 */
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.server-card.dragging-item {
    opacity: 0.9;
    transform: scale(1.02);
    z-index: 1000;
    /* 防止拖拽时的过渡效果 */
    transition: none !important;
}

/* 交换动画 */
.server-card.swap-animation {
    animation: swapPulse 0.3s ease-in-out;
}

@keyframes swapPulse {
    0% {
        background-color: transparent;
        opacity: 1;
    }
    50% {
        background-color: rgba(99, 102, 241, 0.15);
        opacity: 0.9;
    }
    100% {
        background-color: transparent;
        opacity: 1;
    }
}

/* 拖拽时的指针 */
.dragging-active .server-card {
    cursor: grab;
}

.dragging-active .server-card.sortable-drag {
    cursor: grabbing !important;
}

/* 拖拽预览位置指示器 */
.sortable-swap-highlight {
    background-color: rgba(99, 102, 241, 0.2) !important;
    border: 2px dashed rgba(99, 102, 241, 0.6) !important;
}
</style>

<!-- JavaScript文件已在dashboard.html中加载，避免重复引用 -->

<!-- 卡片功能模块 -->
<script src="/js/card/group-counter.js"></script>
<script src="/js/card/traffic-manager.js"></script>
<script src="/js/card/quick-tag-editor.js"></script>
<script src="/js/card/network-charts.js"></script>
<script src="/js/card/system-icons.js"></script>
<script src="/js/card/card-main.js"></script>

<!-- 内联脚本已移至独立模块文件 -->
<!-- 功能由以下模块提供：
     - group-counter.js: 分组计数功能
     - traffic-manager.js: 流量管理功能  
     - quick-tag-editor.js: 快速标签编辑功能
     - network-charts.js: 网络质量图表功能
     - card-main.js: 模块整合和初始化
-->

<script>
// 标签显示限制函数
function limitTagsDisplay(containerId, maxTags = 4) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const tags = Array.from(container.children);
    const actualTags = tags.filter(tag => !tag.classList.contains('more-indicator'));
    
    if (actualTags.length > maxTags) {
        // 隐藏超过限制数量的标签
        for (let i = maxTags; i < actualTags.length; i++) {
            actualTags[i].style.display = 'none';
        }
        
        // 移除之前的更多提示
        const existingIndicator = container.querySelector('.more-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        // 在最后一个位置显示更多提示
        if (actualTags.length > maxTags) {
            const moreIndicator = document.createElement('div');
            moreIndicator.className = 'more-indicator flex items-center justify-center text-xs px-1.5 py-0.5 rounded-md bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors';
            moreIndicator.textContent = `+${actualTags.length - maxTags}`;
            moreIndicator.title = `还有 ${actualTags.length - maxTags} 个标签，点击查看更多`;
            
            // 点击显示更多标签的功能
            moreIndicator.addEventListener('click', function(e) {
                e.stopPropagation();
                const hiddenTags = actualTags.slice(maxTags);
                hiddenTags.forEach(tag => {
                    tag.style.display = tag.style.display === 'none' ? '' : 'none';
                });
                
                if (hiddenTags[0].style.display === 'none') {
                    this.textContent = `+${actualTags.length - maxTags}`;
                    this.title = `还有 ${actualTags.length - maxTags} 个标签，点击查看更多`;
                } else {
                    this.textContent = '收起';
                    this.title = '点击收起额外标签';
                }
            });
            
            container.appendChild(moreIndicator);
        }
    }
}

// 页面加载完成后应用标签限制
document.addEventListener('DOMContentLoaded', function() {
    // 为所有服务器卡片应用标签限制
    const serverCards = document.querySelectorAll('.server-card');
    serverCards.forEach(card => {
        const sid = card.getAttribute('data-sid');
        if (sid) {
            limitTagsDisplay(sid + '_CUSTOM_TAGS', 4);
        }
    });
});

// 导出函数供其他模块使用
window.limitTagsDisplay = limitTagsDisplay;
</script>

<style>
/* 网络质量迷你图表样式 - 竖条版 */
.latency-mini-chart,
.packet-loss-mini-chart {
    display: flex;
    align-items: center;
    gap: 1px; /* 统一使用gap管理间距 */
    height: 10px; /* 调整容器高度适配6px竖条 */
    width: 100%;
    max-width: 100%;
    min-width: 0; /* 防止flex子项溢出 */
    overflow: hidden; /* 防止溢出 */
    padding: 2px 0;
}

/* 竖条样式 - 接近正方形的柱形 */
.latency-mini-chart > div,
.packet-loss-mini-chart > div {
    flex: 1 1 0; /* 等分布局，充满容器 */
    min-width: 1px; /* 最小宽度防止消失 */
    height: 6px; /* 降低高度 */
    border-radius: 1px; /* 轻微圆角 */
    opacity: 0.6; /* 保持60%透明度 */
    transition: all 0.3s ease;
}

/* 移除不需要的边距样式，现在使用gap统一管理间距 */

/* 竖条悬停效果 */
.latency-mini-chart > div:hover,
.packet-loss-mini-chart > div:hover {
    transform: scaleY(1.2);
    opacity: 1 !important; /* 悬停时显示完全不透明 */
    cursor: pointer;
}

/* 保留过渡效果，移除不必要的动画 */


/* 流量进度条动画 */
@keyframes traffic-pulse {
    0%, 100% {
        opacity: 1;
        transform: scaleX(1);
    }
    50% {
        opacity: 0.8;
        transform: scaleX(0.98);
    }
}

/* 无限流量静态样式 - 无需动画 */

/* 标签自适应样式 */
[id$="_CUSTOM_TAGS"] {
    /* 根据标签数量调整间距 */
}

/* 1个标签时居中显示 */
[id$="_CUSTOM_TAGS"][data-tag-count="1"] {
    justify-content: center;
}

/* 2个标签时使用 space-around 获得更好的间距 */
[id$="_CUSTOM_TAGS"][data-tag-count="2"] {
    justify-content: space-around;
}

/* 3个标签时保持 space-between */
[id$="_CUSTOM_TAGS"][data-tag-count="3"] {
    justify-content: space-between;
}

/* 4个标签时也使用 space-between */
[id$="_CUSTOM_TAGS"][data-tag-count="4"] {
    justify-content: space-between;
    gap: 0.125rem; /* 减小间距到2px */
}

/* 标签hover时的阴影效果 */
.tag-item:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.5px);
}

/* 添加按钮样式统一 */
.tag-add {
    flex-shrink: 0; /* 防止被压缩 */
}

.tag-add:hover {
    border-style: solid;
    background-color: rgba(148, 163, 184, 0.1);
}
</style>

<!-- 通用筛选器样式和脚本 -->
<link rel="stylesheet" href="/css/universal-filters.css">
<script type="module" src="/js/universal-filter-manager.js"></script>
<!-- URL 参数筛选处理 -->
<script src="/js/url-filter-handler.js"></script>

{%endblock%}

