<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
    <div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 duration-150 ease-in-out dashboard-card">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-2">
                    <i class="ti ti-dashboard text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">节点情况</h2>
                </div>
                <div class="flex items-center gap-1 sm:gap-2">
                    <div class="flex items-center gap-0.5 sm:gap-1 cursor-pointer expiry-filter bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg px-1.5 sm:px-2 py-1 transition-all duration-200 border border-red-100 dark:border-red-800/30 hover:border-red-200 dark:hover:border-red-700/50" data-days="3" title="点击查看3天内到期节点">
                        <i class="ti ti-clock text-red-600 dark:text-red-400 text-xs sm:text-sm"></i>
                        <span class="hidden sm:inline text-xs text-red-700 dark:text-red-300">3天:</span>
                        <span class="inline sm:hidden text-xs text-red-700 dark:text-red-300">3d</span>
                        <span class="text-xs sm:text-sm text-red-800 dark:text-red-200 font-medium" id="expiring-nodes-3">0</span>
                    </div>
                    <div class="flex items-center gap-0.5 sm:gap-1 cursor-pointer expiry-filter bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-lg px-1.5 sm:px-2 py-1 transition-all duration-200 border border-orange-100 dark:border-orange-800/30 hover:border-orange-200 dark:hover:border-orange-700/50" data-days="7" title="点击查看7天内到期节点">
                        <i class="ti ti-clock text-orange-600 dark:text-orange-400 text-xs sm:text-sm"></i>
                        <span class="hidden sm:inline text-xs text-orange-700 dark:text-orange-300">7天:</span>
                        <span class="inline sm:hidden text-xs text-orange-700 dark:text-orange-300">7d</span>
                        <span class="text-xs sm:text-sm text-orange-800 dark:text-orange-200 font-medium" id="expiring-nodes-7">0</span>
                    </div>
                    <div class="flex items-center gap-0.5 sm:gap-1 cursor-pointer expiry-filter bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg px-1.5 sm:px-2 py-1 transition-all duration-200 border border-yellow-100 dark:border-yellow-800/30 hover:border-yellow-200 dark:hover:border-yellow-700/50" data-days="30" title="点击查看30天内到期节点">
                        <i class="ti ti-clock text-yellow-600 dark:text-yellow-400 text-xs sm:text-sm"></i>
                        <span class="hidden sm:inline text-xs text-yellow-700 dark:text-yellow-300">30天:</span>
                        <span class="inline sm:hidden text-xs text-yellow-700 dark:text-yellow-300">30d</span>
                        <span class="text-xs sm:text-sm text-yellow-800 dark:text-yellow-200 font-medium" id="expiring-nodes-30">0</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-slate-50 dark:hover:bg-slate-800/80 p-3 transition-all duration-200" data-status="ALL" title="点击查看所有节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-slate-800 dark:text-white" id="total-nodes">0</div>
                        <div class="text-xs text-slate-600 dark:text-slate-400 flex items-center justify-center gap-1 mt-1">
                            <i class="ti ti-device-desktop text-indigo-600 dark:text-indigo-400 text-sm"></i>
                            <span>总节点</span>
                        </div>
                    </div>
                </div>
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-green-50 dark:hover:bg-green-900/30 p-3 transition-all duration-200" data-status="ONLINE" title="点击只查看在线节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-green-600 dark:text-green-400" id="online-nodes">0</div>
                        <div class="text-xs text-green-700 dark:text-green-300 flex items-center justify-center gap-1 mt-1">
                            <i class="ti ti-circle-check text-green-600 dark:text-green-400 text-sm"></i>
                            <span>在线节点</span>
                        </div>
                    </div>
                </div>
                <div class="rounded-xl border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-800 shadow cursor-pointer status-filter hover:bg-red-50 dark:hover:bg-red-900/30 p-3 transition-all duration-200" data-status="OFFLINE" title="点击只查看离线节点">
                    <div class="flex flex-col items-center">
                        <div class="text-2xl font-semibold text-red-600 dark:text-red-400" id="offline-nodes">0</div>
                        <div class="text-xs text-red-700 dark:text-red-300 flex items-center justify-center gap-1 mt-1">
                            <i class="ti ti-circle-x text-red-600 dark:text-red-400 text-sm"></i>
                            <span>离线节点</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-medium text-slate-600 dark:text-slate-400 flex items-center gap-2">
                        <i class="ti ti-world text-indigo-600 dark:text-indigo-400 text-base"></i>
                        <span>地区分布</span>
                        <span class="text-xs text-slate-500 dark:text-slate-500 font-normal">(点击筛选)</span>
                    </h3>
                    <div class="flex items-center gap-2">
                        <span id="region-filter-indicator" class="text-xs text-indigo-600 dark:text-indigo-400 font-medium hidden">
                            <i class="ti ti-filter text-xs mr-1"></i>筛选中
                        </span>
                        <button id="refresh-region-stats" class="btn-text !text-xs !text-indigo-600 dark:!text-indigo-400 hover:!text-indigo-700 dark:hover:!text-indigo-300 !px-2 !py-1 hover:!bg-indigo-500/10 dark:hover:!bg-indigo-500/20 transition-all duration-200 rounded-lg border border-transparent hover:!border-indigo-200 dark:hover:!border-indigo-800">
                            <i class="ti ti-refresh text-xs mr-1"></i>
                            <span>刷新</span>
                        </button>
                    </div>
                </div>
                <!-- 使用新的CSS类的地区标签容器 -->
                <div class="region-stats-grid" id="region-stats">
                    <!-- 地区标签将由JavaScript动态生成 -->
                    <div class="region-stats-loading text-slate-400 dark:text-slate-500">
                        <i class="ti ti-refresh animate-spin"></i>
                        加载地区数据...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 duration-150 ease-in-out dashboard-card">
        <div class="p-6">
            <div class="hidden md:flex items-center justify-between mb-6">
                <div class="flex items-center gap-2">
                    <i class="ti ti-wifi text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">网络情况</h2>
                </div>
                <div class="flex items-center gap-1">
                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-1" title="只影响前端动画效果，不影响数据获取频率">动画速率:</span>
                    <button id="animation-speed-normal" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800/70 active" title="正常动画速度 (只影响前端动画效果)">正常</button>
                    <button id="animation-speed-fast" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="较快动画速度 (只影响前端动画效果)">迅速</button>
                    <button id="animation-speed-slow" class="animation-speed-btn px-2 py-0.5 text-xs rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="关闭动画效果 (只影响前端动画效果)">关闭</button>
                </div>
            </div>

            <div class="flex md:hidden items-center justify-between mb-4">
                <div class="flex items-center gap-2">
                    <i class="ti ti-wifi text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded-lg"></i>
                    <h2 class="text-lg font-medium text-slate-800 dark:text-white">网络情况</h2>
                </div>
                <div class="flex items-center gap-1 ml-2" title="只影响前端动画效果，不影响数据获取频率">
                    <button id="animation-speed-normal-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800/70 active" title="正常动画速度 (只影响前端动画效果)">正常</button>
                    <button id="animation-speed-fast-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="较快动画速度 (只影响前端动画效果)">迅速</button>
                    <button id="animation-speed-slow-mobile" class="animation-speed-btn px-1.5 py-0.5 text-[10px] rounded bg-gray-100 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/70" title="关闭动画效果 (只影响前端动画效果)">关闭</button>
                </div>
            </div>

            <div class="hidden md:grid grid-cols-1 gap-6">
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <h3 class="text-sm font-medium text-slate-600 dark:text-slate-400 mb-3">实时带宽</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center mb-1.5">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-green-500 dark:text-green-400 text-xl">↓</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="current-download-speed">
                                    <span id="current-download-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="current-download-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="download-progress-container">
                                <div class="h-full rounded-full bg-green-500 dark:bg-green-400 transition-width duration-800 ease-network" style="width:0%" id="download-speed-progress"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1.5">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-blue-500 dark:text-blue-400 text-xl">↑</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="current-upload-speed">
                                    <span id="current-upload-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="current-upload-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="upload-progress-container">
                                <div class="h-full rounded-full bg-blue-500 dark:bg-blue-400 transition-width duration-800 ease-network" style="width:0%" id="upload-speed-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <h3 class="text-sm font-medium text-slate-600 dark:text-slate-400 mb-3">总流量</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-green-500 dark:text-green-400 text-xl">↓</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="total-download">
                                    <span id="total-download-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="total-download-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center font-bold w-6 h-6 text-blue-500 dark:text-blue-400 text-xl">↑</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="total-upload">
                                    <span id="total-upload-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="total-upload-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="md:hidden grid grid-cols-1 gap-4">
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-4 border border-slate-200/10 dark:border-slate-700/80">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center mb-1">
                                <span class="text-green-500 dark:text-green-400 text-base">↓</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="mobile-download-speed">
                                    <span id="mobile-download-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="mobile-download-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="mobile-download-progress-container">
                                <div class="h-full rounded-full bg-green-500 dark:bg-green-400 transition-width duration-800 ease-network" style="width:0%" id="mobile-download-speed-progress"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1">
                                <span class="text-blue-500 dark:text-blue-400 text-base">↑</span>
                                <div class="flex-1 inline-flex items-baseline justify-end" id="mobile-upload-speed">
                                    <span id="mobile-upload-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                                    <span id="mobile-upload-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
                                </div>
                            </div>
                            <div class="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden relative" id="mobile-upload-progress-container">
                                <div class="h-full rounded-full bg-blue-500 dark:bg-blue-400 transition-width duration-800 ease-network" style="width:0%" id="mobile-upload-speed-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="rounded-lg border border-slate-200 dark:border-slate-700 relative overflow-hidden bg-white dark:bg-slate-700 transition-shadow duration-150 ease-in-out mt-4 mb-4 dashboard-card">
    <div class="p-3 overflow-x-auto hide-scrollbar">
        <div class="flex items-center justify-between flex-wrap gap-2">
            <div class="flex items-center gap-2 min-w-0 flex-wrap sm:flex-nowrap">
                <div class="sort-dropdown relative">
                    <button id="sort-dropdown-btn" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-indigo-500/70 dark:hover:!border-indigo-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" aria-haspopup="true" aria-expanded="false" aria-controls="sort-dropdown-menu">
                        <i class="ti ti-arrows-sort text-sm mr-1 text-indigo-600 dark:text-indigo-400"></i>
                        <span id="current-sort-text" class="text-slate-800 dark:text-white font-medium">默认排序</span>
                        <i class="ti ti-chevron-down text-sm ml-1 text-slate-600 dark:text-slate-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200"></i>
                    </button>
                </div>

                <div class="group-dropdown relative">
                    <button id="group-dropdown-btn" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-indigo-500/70 dark:hover:!border-indigo-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" aria-haspopup="true" aria-expanded="false" aria-controls="group-dropdown-menu">
                        <i class="ti ti-folder text-sm mr-1 text-indigo-600 dark:text-indigo-400"></i>
                        <span id="current-group-text" class="text-slate-800 dark:text-white font-medium">全部节点</span>
                        <i class="ti ti-chevron-down text-sm ml-1 text-slate-600 dark:text-slate-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200"></i>
                    </button>
                </div>

                {% set viewToggleCurrentTheme = theme %}
                {% set viewToggleTargetTheme = 'card' if viewToggleCurrentTheme == 'list' else 'list' %}
                <a id="view-toggle-btn" href="/?theme={{ viewToggleTargetTheme }}" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-amber-500/70 dark:hover:!border-amber-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" onclick="handleViewToggle('{{ viewToggleCurrentTheme }}', '{{ viewToggleTargetTheme }}');">
                    <i class="ti text-sm mr-1 text-amber-500 dark:text-amber-400 {%if viewToggleCurrentTheme == 'list'%}ti-layout-grid{%else%}ti-list{%endif%}"></i>
                    <span class="text-slate-800 dark:text-white font-medium">{%if viewToggleCurrentTheme == 'list'%}卡片{%else%}列表{%endif%}</span>
                </a>
            </div>

            {%if admin%}
            <div class="flex items-center gap-2 flex-wrap sm:flex-nowrap">
                <!-- 设置下拉菜单 -->
                <div class="settings-dropdown relative">
                    <button id="settings-dropdown-btn" class="group btn-text !h-8 !px-3 !text-xs sm:!text-sm hover:!border-slate-500/70 dark:hover:!border-slate-400/70 transition-all duration-200 shadow-sm hover:shadow-md rounded-lg" aria-haspopup="true" aria-expanded="false" aria-controls="dashboard-settings-dropdown-menu">
                        <i class="ti ti-settings text-sm mr-1 text-slate-600 dark:text-slate-400"></i>
                        <span class="text-slate-800 dark:text-white font-medium">设置</span>
                        <i class="ti ti-chevron-down text-sm ml-1 text-slate-600 dark:text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-400 transition-colors duration-200"></i>
                    </button>
                </div>
                
                <!-- 撤销/重做按钮 -->
                <div class="flex items-center gap-1" id="undo-redo-buttons" style="display: none;">
                    <button id="undo-sort-btn" class="btn-icon !h-8 !w-8 opacity-50" title="撤销" disabled>
                        <i class="ti ti-arrow-back-up text-sm"></i>
                    </button>
                    <button id="redo-sort-btn" class="btn-icon !h-8 !w-8 opacity-50" title="重做" disabled>
                        <i class="ti ti-arrow-forward-up text-sm"></i>
                    </button>
                    <!-- 保存提示 -->
                    <div id="sort-save-indicator" class="flex items-center gap-1 ml-2 text-xs text-gray-500 dark:text-gray-400" style="display: none;">
                        <i class="ti ti-circle-check text-sm" id="save-status-icon"></i>
                        <span id="save-status-text">已自动保存</span>
                    </div>
                </div>
            </div>
            {%endif%}
        </div>
    </div>
</div>

<div id="sort-dropdown-menu" class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg max-h-[400px] overflow-y-auto z-50 w-60 fixed mt-2 hidden" role="menu" aria-labelledby="sort-dropdown-btn">
    <div class="p-3">
        <div class="text-sm font-medium text-slate-700 dark:text-slate-300 px-1 pb-1 mb-2 border-b border-slate-200 dark:border-slate-600">选择排序方式</div>
        <div class="grid grid-cols-2 gap-2">
            <div>
                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option active" data-sort="default" data-direction="desc">
                    <span class="text-xs font-medium">默认排序</span>
                    <i class="ti ti-chevron-down text-xs sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="name" data-direction="asc">
                    <span class="text-xs">名称</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="total-traffic" data-direction="desc">
                    <span class="text-xs">总流量</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="expiration" data-direction="asc">
                    <span class="text-xs">到期时间</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="uptime" data-direction="desc">
                    <span class="text-xs">在线时间</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>
            </div>

            <div>
                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="cpu" data-direction="desc">
                    <span class="text-xs">CPU使用率</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="memory" data-direction="desc">
                    <span class="text-xs">内存使用率</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="disk-usage" data-direction="desc">
                    <span class="text-xs">硬盘使用率</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="download" data-direction="desc">
                    <span class="text-xs">下载速度</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>

                <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg sort-option" data-sort="upload" data-direction="desc">
                    <span class="text-xs">上传速度</span>
                    <i class="ti ti-selector text-xs text-slate-500 dark:text-slate-400 sort-direction-icon"></i>
                </div>
            </div>
        </div>

        <div class="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
            <label class="flex items-center justify-between px-2 py-1 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600/50 transition-all duration-200 cursor-pointer">
                <div class="flex items-center gap-2">
                    <i class="ti ti-refresh text-sm text-indigo-600 dark:text-indigo-400"></i>
                    <span class="text-xs font-medium text-slate-800 dark:text-white">实时排序</span>
                </div>
                <div class="relative">
                    <input type="checkbox" id="realtime-sort" class="sr-only peer" checked>
                    <div class="w-9 h-5 bg-slate-200 dark:bg-slate-600 rounded-full peer peer-checked:bg-indigo-500 dark:peer-checked:bg-indigo-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-slate-900 after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm"></div>
                </div>
            </label>
        </div>
    </div>
</div>

<div id="group-dropdown-menu" class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg max-h-[400px] overflow-y-auto z-50 w-60 fixed mt-2 hidden" role="menu" aria-labelledby="group-dropdown-btn">
    <div class="p-3">
        <div class="text-sm font-medium text-slate-700 dark:text-slate-300 px-1 pb-1 mb-2 border-b border-slate-200 dark:border-slate-600">选择分组</div>
        <div class="max-h-[300px] overflow-y-auto hide-scrollbar pr-1">
            <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 rounded-lg group-option active" data-group="all">
                <div class="flex items-center gap-2">
                    <i class="ti ti-folder text-sm text-indigo-600 dark:text-indigo-400"></i>
                    <span class="text-xs font-medium">全部节点</span>
                </div>
                <span class="text-xs text-slate-600 dark:text-slate-400 group-count font-medium">{{ stats|length if stats else 0 }}</span>
            </div>

            {% for group in groups %}
            <div class="flex items-center justify-between px-3 py-2 cursor-pointer transition-colors duration-150 hover:bg-slate-100 dark:hover:bg-slate-700/50 rounded-lg group-option" data-group="{{group.id}}">
                <div class="flex items-center gap-2">
                    <i class="ti ti-folder text-sm text-slate-500 dark:text-slate-400"></i>
                    <span class="text-xs">{{group.name}}</span>
                </div>
                <span class="text-xs text-slate-600 dark:text-slate-400 group-count">{{ group.server_count if group.server_count is defined else 0 }}</span>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 设置下拉菜单 -->
<div id="dashboard-settings-dropdown-menu" class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl shadow-lg max-h-[400px] overflow-y-auto z-50 w-60 fixed mt-2 hidden" role="menu" aria-labelledby="settings-dropdown-btn">
    <div class="p-3">
        <div class="text-sm font-medium text-slate-700 dark:text-slate-300 px-1 pb-1 mb-3 border-b border-slate-200 dark:border-slate-600">管理设置</div>
        
        <!-- 快捷操作 -->
        <div class="space-y-2 mb-3">
            <a href="/admin/servers/add" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors duration-150 text-slate-700 dark:text-slate-300">
                <i class="ti ti-plus text-sm text-green-500 dark:text-green-400"></i>
                <span class="text-xs font-medium">新增节点</span>
            </a>
            <a href="/admin/groups" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors duration-150 text-slate-700 dark:text-slate-300">
                <i class="ti ti-folder text-sm text-blue-500 dark:text-blue-400"></i>
                <span class="text-xs font-medium">管理分组</span>
            </a>
        </div>
        
        <!-- 功能开关 -->
        <div class="pt-3 border-t border-slate-200 dark:border-slate-600">
            <div class="text-xs font-medium text-slate-600 dark:text-slate-400 px-1 mb-2">功能开关</div>
            
            <label class="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600/50 transition-all duration-200 cursor-pointer">
                <div class="flex items-center gap-2">
                    <i class="ti ti-arrows-move text-sm text-indigo-600 dark:text-indigo-400"></i>
                    <span class="text-xs font-medium text-slate-800 dark:text-white">拖拽排序</span>
                </div>
                <div class="relative">
                    <input type="checkbox" id="enable-drag-sort" class="sr-only peer">
                    <div class="w-9 h-5 bg-slate-200 dark:bg-slate-600 rounded-full peer peer-checked:bg-indigo-500 dark:peer-checked:bg-indigo-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-slate-900 after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm"></div>
                </div>
            </label>
            
            <label class="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600/50 transition-all duration-200 cursor-pointer">
                <div class="flex items-center gap-2">
                    <i class="ti ti-eye-off text-sm text-purple-600 dark:text-purple-400"></i>
                    <span class="text-xs font-medium text-slate-800 dark:text-white">隐私模式</span>
                </div>
                <div class="relative">
                    <input type="checkbox" id="enable-privacy-mode" class="sr-only peer">
                    <div class="w-9 h-5 bg-slate-200 dark:bg-slate-600 rounded-full peer peer-checked:bg-purple-500 dark:peer-checked:bg-purple-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-slate-900 after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm"></div>
                </div>
            </label>
        </div>
    </div>
</div>

<script>
// 为图表应用自定义主题颜色
function getThemeColors() {
    // 检查dark类是否存在来确定当前主题模式
    const isDark = document.documentElement.classList.contains('dark');

    // 设置默认颜色 - 使用indigo作为主色调
    const defaultLightColors = ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
    const defaultDarkColors = ['#6366f1', '#34d399', '#fbbf24', '#f87171', '#a78bfa'];

    // 初始化颜色数组
    let chartColors = isDark ? defaultDarkColors : defaultLightColors;

    // 尝试从CSS变量获取颜色
    const getComputedVar = (name) => getComputedStyle(document.documentElement).getPropertyValue(name).trim();

    // 检查是否存在主题变量
    if (getComputedVar('--accent-color')) {
        const accentColor = getComputedVar('--accent-color');
        chartColors[0] = accentColor; // 使用强调色作为第一个图表颜色
    }

    return chartColors;
}

// 使用新的主题监听器
function setupThemeChangeListener() {
    // 监听主题变化事件
    document.addEventListener('theme:changed', function(e) {
        if (!e.detail) return; // 防止空对象

        const isDark = e.detail.isDark;
        const theme = e.detail.theme || (isDark ? 'dark' : 'light'); // 确保始终有theme值

        console.log(`主题已切换: ${theme} (isDark: ${isDark})`);

        // 更新图表颜色
        updateChartThemes(isDark);
        
        // 触发个性化设置重新应用（修复卡片背景色问题）
        if (window.settingsSync && typeof window.settingsSync.loadAndApplySettings === 'function') {
            setTimeout(() => {
                window.settingsSync.loadAndApplySettings();
            }, 100);
        }
    });

    // 初始化时检查当前主题
    const isDark = document.documentElement.classList.contains('dark');
    updateChartThemes(isDark);

    console.log('主题变化监听器已设置');
}

// 更新图表主题
function updateChartThemes(isDark) {
    // 更新图表颜色（如果有）
    const charts = window.charts || [];
    for (const chartId in charts) {
        const chart = charts[chartId];
        if (chart && typeof chart.update === 'function') {
            // 更新图表主题
            const newOptions = getChartThemeOptions(isDark);
            chart.update({
                options: newOptions
            });
        }
    }
    
    // 更新地区分布图表背景色
    const regionStats = document.getElementById('region-stats');
    if (regionStats) {
        const regionItems = regionStats.querySelectorAll('.flex.items-center.justify-between');
        regionItems.forEach(item => {
            if (isDark) {
                item.classList.remove('bg-white', 'border-slate-200');
                item.classList.add('bg-slate-800', 'border-slate-700');
            } else {
                item.classList.remove('bg-slate-800', 'border-slate-700');
                item.classList.add('bg-white', 'border-slate-200');
            }
        });
    }
}

// 获取图表主题选项
function getChartThemeOptions(isDark) {
    return {
        theme: {
            mode: isDark ? 'dark' : 'light'
        },
        grid: {
            borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        },
        tooltip: {
            theme: isDark ? 'dark' : 'light'
        },
        colors: getThemeColors()
    };
}

// 在页面加载时设置主题监听
document.addEventListener('DOMContentLoaded', setupThemeChangeListener);
</script>

<!-- 模块化脚本加载 -->
<!-- 加载简化的度量样式系统 - 确保PC和移动端一致 -->
<link rel="stylesheet" href="/css/components/metrics-simplified.css">
<!-- 通用筛选器样式 -->
<link rel="stylesheet" href="/css/universal-filters.css">

<!-- 缓存迁移工具 - 清理旧版本缓存 -->
<script src="/js/cache-migration.js"></script>

<!-- 缓存管理器已移除，使用 /js/stats/cacheManager.js -->

<!-- 优先加载统一格式化函数 -->
<script src="/js/utils/MetricFormatter.js"></script>
<script src="/js/stats.js"></script>
<script src="/js/core.js"></script>
<script src="/js/animation.js"></script>
<script src="/js/tab-menu.js"></script>
<script src="/js/libs/sortable.min.js"></script>
<script>
// Sortable已加载，可以直接使用
if (typeof Sortable !== 'undefined') {
    console.log('✅ Sortable插件已加载，版本:', Sortable.version || '未知');
} else {
    console.warn('❌ Sortable插件未加载');
}
</script>
<script src="/js/sort.js"></script>
<script src="/js/stats/cacheManager.js"></script>

<!-- 新的模块化脚本 - 调整加载顺序，确保region-stats.js在SortManager之前加载 -->
<script src="/js/StatsInitializer.js"></script>
<script src="/js/ConnectionManager.js"></script>
<script src="/js/DataProcessor.js"></script>
<!-- 加载地区缓存管理器 -->
<script src="/js/RegionCacheManager.js"></script>
<!-- 地区分布性能优化器 - 在region-stats.js之前加载 -->
<script src="/js/region-performance-optimizer.js"></script>
<!-- 先加载region-stats.js，确保RegionStatsModule在SortManager之前初始化 -->
<script src="/js/region-stats.js"></script>
<script src="/js/universal-filter-manager.js"></script>
<script src="/js/SortManager.js">

<!-- 延迟气泡提示功能 -->
<script src="/js/latency-tooltip.js"></script>
<!-- 仪表板和其他功能脚本 -->
<script src="/js/dashboard.js"></script>
<!-- Dashboard 筛选处理 -->

<div id="connection-status" class="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2 opacity-0 transition-opacity duration-500 z-50">
    <i class="ti ti-wifi-off text-sm"></i>
    <span>连接已断开，正在重连...</span>
</div>

<style>
/* 状态筛选按钮激活状态样式 */
.status-filter {
    transition: all 0.2s ease;
}

.status-filter.active {
    background-color: rgba(99, 102, 241, 0.1) !important;
    border-color: rgba(99, 102, 241, 0.5) !important;
}

.status-filter.active[data-status="ALL"] {
    background-color: rgba(99, 102, 241, 0.1) !important;
}

.status-filter.active[data-status="ONLINE"] {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: rgba(34, 197, 94, 0.5) !important;
}

.status-filter.active[data-status="OFFLINE"] {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: rgba(239, 68, 68, 0.5) !important;
}

/* 暗色模式下的激活状态 */
.dark .status-filter.active {
    background-color: rgba(99, 102, 241, 0.2) !important;
    border-color: rgba(99, 102, 241, 0.6) !important;
}

.dark .status-filter.active[data-status="ALL"] {
    background-color: rgba(99, 102, 241, 0.2) !important;
}

.dark .status-filter.active[data-status="ONLINE"] {
    background-color: rgba(34, 197, 94, 0.2) !important;
    border-color: rgba(34, 197, 94, 0.6) !important;
}

.dark .status-filter.active[data-status="OFFLINE"] {
    background-color: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
}
</style>

<script>
// 处理视图切换
function handleViewToggle(fromView, toView) {
    // 清除第一次数据标记
    localStorage.removeItem('stats_first_data_received');

    // 网络数据缓存已禁用，清理可能存在的旧缓存
    const networkDataCache = localStorage.getItem('network_data_cache');
    if (networkDataCache) {
        try {
            localStorage.removeItem('network_data_cache');
            console.log('视图切换时已清理旧的网络数据缓存');
        } catch (e) {
            console.error('清理网络数据缓存失败:', e);
        }
    }

    // 更新连接状态，确保连接保持活跃
    localStorage.setItem('stats_connection_timestamp', Date.now().toString());

    // 如果有SharedWorker客户端，请求最新数据
    if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
        // 添加延迟，确保在视图切换后请求数据
        setTimeout(() => {
            window.sharedClient.requestLastData();
        }, 300);
    }

    // 应用视图过渡效果
    if (window.SmoothTransition && typeof window.SmoothTransition.viewTransition === 'function') {
        window.SmoothTransition.viewTransition(fromView, toView);
    }
}

// 隐私模式管理
document.addEventListener('DOMContentLoaded', function() {
    const privacyToggle = document.getElementById('enable-privacy-mode');
    
    if (privacyToggle) {
        // 从localStorage恢复隐私模式状态
        const savedPrivacyMode = localStorage.getItem('dashboard_privacy_mode');
        if (savedPrivacyMode === 'true') {
            privacyToggle.checked = true;
            document.body.classList.add('privacy-mode');
        }
        
        // 监听隐私模式切换
        privacyToggle.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('privacy-mode');
                localStorage.setItem('dashboard_privacy_mode', 'true');
            } else {
                document.body.classList.remove('privacy-mode');
                localStorage.setItem('dashboard_privacy_mode', 'false');
            }
        });
    }

    // 拖拽排序切换
    const dragToggle = document.getElementById('enable-drag-sort');
    if (dragToggle) {
        // 从localStorage恢复拖拽排序状态
        const isDragEnabled = localStorage.getItem('dragSortEnabled') === 'true';
        dragToggle.checked = isDragEnabled;
        if (isDragEnabled) {
            document.body.classList.add('drag-enabled');
        }

        dragToggle.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('drag-enabled');
                localStorage.setItem('dragSortEnabled', 'true');
            } else {
                document.body.classList.remove('drag-enabled');
                localStorage.setItem('dragSortEnabled', 'false');
            }
        });
    }

});

// 连接状态管理
document.addEventListener('DOMContentLoaded', function() {
    const connectionIndicator = document.getElementById('connection-status');
    let connectionTimer = null;

    // 监听连接状态变化
    document.addEventListener('connection:status', function(e) {
        if (!connectionIndicator) return;

        if (e.detail.status === 'disconnected') {
            // 设置一个计时器，只有断开超过3秒才显示
            clearTimeout(connectionTimer);
            connectionTimer = setTimeout(() => {
                connectionIndicator.style.opacity = '1';
            }, 3000);
        } else {
            clearTimeout(connectionTimer);
            connectionIndicator.style.opacity = '0';
        }
    });
});
</script>

<script src="/js/region-refresh.js"></script><style>
/* 隐私模式样式 - Card视图：隐藏 .server-name 容器内的文本 */
.privacy-mode .server-name {
    display: flex !important;
    align-items: center !important;
    width: 80px !important;
    height: 20px !important;
    background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);
    border-radius: 6px;
}

.privacy-mode .server-name * {
    display: none !important;
}

/* 隐私模式样式 - List视图：隐藏 .card-title-link 链接 */
.privacy-mode .card-title-link {
    display: inline-block !important;
    width: 80px !important;
    height: 16px !important;
    background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);
    border-radius: 6px;
    text-indent: -9999px !important;
    overflow: hidden !important;
}

/* 深色模式下的占位色块 */
.dark .privacy-mode .server-name {
    background: linear-gradient(90deg, #475569 0%, #64748b 50%, #475569 100%);
}

.dark .privacy-mode .card-title-link {
    background: linear-gradient(90deg, #475569 0%, #64748b 50%, #475569 100%);
}

/* 拖拽手柄样式 */
.server-card:hover .drag-handle {
    opacity: 0.5;
}

.server-card.sortable-chosen .drag-handle,
.server-card.sortable-drag .drag-handle {
    opacity: 1 !important;
    cursor: grabbing !important;
}

/* 拖拽动画增强 */
.sortable-ghost {
    opacity: 0.4;
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed rgba(99, 102, 241, 0.5);
}

.sortable-drag {
    opacity: 0.9 !important;
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.sortable-chosen {
    opacity: 0.8;
}

/* 拖拽时的过渡动画 - 调整为不影响拖拽的属性 */
.server-card {
    transition: background-color 0.15s ease, border-color 0.15s ease;
}

/* 拖拽时禁用过渡效果，避免抖动 */
.dragging-active .server-card {
    transition: none;
}

/* 撤销/重做按钮样式 */
#undo-sort-btn:not(:disabled):hover,
#redo-sort-btn:not(:disabled):hover {
    opacity: 1;
    background-color: rgba(99, 102, 241, 0.1);
}

/* 拖拽交互反馈样式 */
.server-card.drop-target {
    background-color: rgba(99, 102, 241, 0.1) !important;
    border: 2px solid rgba(99, 102, 241, 0.5) !important;
    /* 移除transform避免抖动 */
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    /* 添加过渡效果使变化更平滑 */
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.server-card.dragging-item {
    opacity: 0.9;
    transform: scale(1.02);
    z-index: 1000;
    /* 防止拖拽时的过渡效果 */
    transition: none !important;
}

/* 交换动画 */
.server-card.swap-animation {
    animation: swapPulse 0.3s ease-in-out;
}

@keyframes swapPulse {
    0% {
        background-color: transparent;
        opacity: 1;
    }
    50% {
        background-color: rgba(99, 102, 241, 0.15);
        opacity: 0.9;
    }
    100% {
        background-color: transparent;
        opacity: 1;
    }
}

/* 拖拽时的指针 */
.dragging-active .server-card {
    cursor: grab;
}

.dragging-active .server-card.sortable-drag {
    cursor: grabbing !important;
}

/* 拖拽预览位置指示器 */
.sortable-swap-highlight {
    background-color: rgba(99, 102, 241, 0.2) !important;
    border: 2px dashed rgba(99, 102, 241, 0.6) !important;
}

/* 保存状态旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 保存状态指示器样式 */
#sort-save-indicator {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark #sort-save-indicator {
    background-color: rgba(30, 41, 59, 0.9);
}
</style>
