<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组筛选功能测试</title>
    <link rel="stylesheet" href="static/css/universal-filters.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .test-controls h2 {
            margin-top: 0;
        }
        
        .control-group {
            margin: 10px 0;
        }
        
        .control-group label {
            font-weight: bold;
            margin-right: 10px;
        }
        
        button {
            padding: 5px 15px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        button.active {
            background: #007bff;
            color: white;
        }
        
        #groups-container {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
        
        .server-card {
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .server-card h3 {
            margin-top: 0;
            color: #333;
        }
        
        .server-info {
            color: #666;
            font-size: 14px;
        }
        
        .server-info span {
            display: inline-block;
            margin-right: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-badge.online {
            background: #28a745;
            color: white;
        }
        
        .status-badge.offline {
            background: #dc3545;
            color: white;
        }
        
        .debug-info {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <h1>分组筛选功能测试页面</h1>
    
    <div class="test-controls">
        <h2>筛选控制</h2>
        
        <div class="control-group">
            <label>分组筛选：</label>
            <button onclick="filterGroup('all')" class="group-btn active" data-group="all">全部节点</button>
            <button onclick="filterGroup('production')" class="group-btn" data-group="production">生产环境</button>
            <button onclick="filterGroup('development')" class="group-btn" data-group="development">开发环境</button>
            <button onclick="filterGroup('testing')" class="group-btn" data-group="testing">测试环境</button>
            <button onclick="filterGroup('staging')" class="group-btn" data-group="staging">预发布</button>
            <button onclick="filterGroup('default')" class="group-btn" data-group="default">默认分组</button>
            <button onclick="filterGroup('ops')" class="group-btn" data-group="ops">运维组</button>
            <button onclick="filterGroup('beta')" class="group-btn" data-group="beta">Beta测试</button>
        </div>
        
        <div class="control-group">
            <label>状态筛选：</label>
            <button onclick="filterStatus('ALL')" class="status-btn active" data-status="ALL">全部状态</button>
            <button onclick="filterStatus('ONLINE')" class="status-btn" data-status="ONLINE">在线</button>
            <button onclick="filterStatus('OFFLINE')" class="status-btn" data-status="OFFLINE">离线</button>
        </div>
        
        <div class="control-group">
            <label>操作：</label>
            <button onclick="resetFilters()">重置所有筛选</button>
            <button onclick="refreshFilters()">刷新筛选</button>
            <button onclick="addTestCard()">添加测试卡片</button>
        </div>
    </div>
    
    <div id="groups-container">
        <!-- 默认分组的服务器 -->
        <div class="server-card" data-group="production" data-status="ONLINE">
            <h3>生产服务器 1</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: production</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="production" data-status="ONLINE">
            <h3>生产服务器 2</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: production</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="development" data-status="ONLINE">
            <h3>开发服务器 1</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: development</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="testing" data-status="OFFLINE">
            <h3>测试服务器 1</h3>
            <div class="server-info">
                <span class="status-badge offline">离线</span>
                <span>分组: testing</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="staging" data-status="ONLINE">
            <h3>预发布服务器 1</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: staging</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <!-- 新建的自定义分组 -->
        <div class="server-card" data-group="default" data-status="ONLINE">
            <h3>默认组服务器 1</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: default</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="ops" data-status="ONLINE">
            <h3>运维组服务器 1</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: ops</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="beta" data-status="OFFLINE">
            <h3>Beta测试服务器 1</h3>
            <div class="server-info">
                <span class="status-badge offline">离线</span>
                <span>分组: beta</span>
                <span>IP: *************</span>
            </div>
        </div>
        
        <div class="server-card" data-group="beta" data-status="ONLINE">
            <h3>Beta测试服务器 2</h3>
            <div class="server-info">
                <span class="status-badge online">在线</span>
                <span>分组: beta</span>
                <span>IP: *************</span>
            </div>
        </div>
    </div>
    
    <div class="debug-info">
        <h3>调试信息</h3>
        <div id="debug-output"></div>
    </div>
    
    <script src="static/js/universal-filter-manager.js"></script>
    <script>
        // 调试输出函数
        function debugLog(message) {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            console.log(`[Debug] ${message}`);
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const state = UF.getState();
            
            // 更新分组按钮
            document.querySelectorAll('.group-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-group') === state.group);
            });
            
            // 更新状态按钮
            document.querySelectorAll('.status-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-status') === state.status);
            });
        }
        
        // 分组筛选
        function filterGroup(groupId) {
            debugLog(`筛选分组: ${groupId}`);
            UF.filterByGroup(groupId);
            updateButtonStates();
            showFilterStatus();
        }
        
        // 状态筛选
        function filterStatus(status) {
            debugLog(`筛选状态: ${status}`);
            UF.filterByStatus(status);
            updateButtonStates();
            showFilterStatus();
        }
        
        // 重置筛选
        function resetFilters() {
            debugLog('重置所有筛选');
            UF.reset();
            updateButtonStates();
            showFilterStatus();
        }
        
        // 刷新筛选
        function refreshFilters() {
            debugLog('刷新筛选');
            UF.refresh();
            showFilterStatus();
        }
        
        // 显示当前筛选状态
        function showFilterStatus() {
            const state = UF.getState();
            const container = document.getElementById('groups-container');
            const visibleCards = container.querySelectorAll('.server-card:not([data-hidden-by-group="true"])');
            const totalCards = container.querySelectorAll('.server-card').length;
            
            debugLog(`当前筛选状态: 分组=${state.group}, 状态=${state.status}`);
            debugLog(`显示卡片数: ${visibleCards.length}/${totalCards}`);
            
            // 显示每个卡片的状态
            container.querySelectorAll('.server-card').forEach((card, index) => {
                const cardGroup = card.getAttribute('data-group');
                const isHidden = card.getAttribute('data-hidden-by-group') === 'true';
                console.log(`卡片 ${index + 1}: 分组=${cardGroup}, 隐藏=${isHidden}`);
            });
        }
        
        // 添加测试卡片
        let testCardCount = 0;
        function addTestCard() {
            testCardCount++;
            const groups = ['custom-group', 'new-group', 'dynamic-group'];
            const randomGroup = groups[Math.floor(Math.random() * groups.length)];
            const isOnline = Math.random() > 0.5;
            
            const card = document.createElement('div');
            card.className = 'server-card';
            card.setAttribute('data-group', randomGroup);
            card.setAttribute('data-status', isOnline ? 'ONLINE' : 'OFFLINE');
            
            card.innerHTML = `
                <h3>动态服务器 ${testCardCount}</h3>
                <div class="server-info">
                    <span class="status-badge ${isOnline ? 'online' : 'offline'}">${isOnline ? '在线' : '离线'}</span>
                    <span>分组: ${randomGroup}</span>
                    <span>IP: 10.0.0.${testCardCount}</span>
                </div>
            `;
            
            document.getElementById('groups-container').appendChild(card);
            debugLog(`添加新卡片: 分组=${randomGroup}, 状态=${isOnline ? 'ONLINE' : 'OFFLINE'}`);
            
            // 刷新筛选以应用到新卡片
            UF.refresh();
        }
        
        // 监听筛选变化
        UF.onChange((state) => {
            debugLog(`筛选状态变化: ${JSON.stringify(state)}`);
        });
        
        // 初始化
        debugLog('页面加载完成，初始化筛选器');
        showFilterStatus();
    </script>
</body>
</html>