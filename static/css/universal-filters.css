/******************************************
 * 通用筛选器样式
 * 使用属性选择器实现筛选功能
 * 配合 universal-filter-manager.js 使用
 ******************************************/

/******************************************
 * 分组筛选
 * 改用 JavaScript 动态控制，因为 CSS 无法处理动态分组
 * 旧的静态规则已被注释，保留以供参考
 ******************************************/

/* 
 * 以下规则已废弃，改用 JavaScript 控制
 * 原因：CSS 属性选择器无法动态匹配任意分组名称
 * 
 * #groups-container[data-group]:not([data-group="all"]) .server-card:not([data-group=""]) {
 *     display: none !important;
 * }
 * 
 * #groups-container[data-group="production"] .server-card[data-group="production"],
 * #groups-container[data-group="development"] .server-card[data-group="development"],
 * #groups-container[data-group="testing"] .server-card[data-group="testing"],
 * #groups-container[data-group="staging"] .server-card[data-group="staging"] {
 *     display: block !important;
 * }
 * 
 * #groups-container[data-group]:not([data-group="all"]):not([data-group=""]) .server-card {
 *     display: none !important;
 * }
 * 
 * #groups-container[data-group]:not([data-group="all"]):not([data-group=""]) .server-card[data-group] {
 *     display: none !important;
 * }
 */

/* 分组筛选 - 使用 JavaScript 设置的 data 属性 */
.server-card[data-hidden-by-group="true"] {
    display: none !important;
}

/******************************************
 * 状态筛选
 * 根据容器的 data-status 属性控制卡片显示
 ******************************************/

/* 状态筛选 - 采用先隐藏后显示的策略 */
#groups-container[data-status="ONLINE"] .server-card,
#groups-container[data-status="OFFLINE"] .server-card {
    display: none !important;
}

/* 显示匹配状态的卡片 - 使用flex确保Grid兼容 */
#groups-container[data-status="ONLINE"] .server-card[data-status="ONLINE"] {
    display: flex !important;
}

#groups-container[data-status="OFFLINE"] .server-card[data-status="OFFLINE"] {
    display: flex !important;
}

/* ALL状态显示所有卡片 */
#groups-container[data-status="ALL"] .server-card {
    display: flex !important;
}

/******************************************
 * 到期时间筛选
 * 改用 JavaScript 动态控制，因为 CSS 无法进行数值比较
 * 使用 data-hidden-by-expiry 属性标记隐藏状态
 ******************************************/

/* 到期时间筛选 - 使用 JavaScript 设置的 data 属性 */
.server-card[data-hidden-by-expiry="true"] {
    display: none !important;
}

/******************************************
 * 地区筛选
 * 根据 data-region 属性进行匹配
 ******************************************/

/* 当容器设置了地区筛选时，隐藏所有卡片 */
#groups-container[data-region]:not([data-region=""]) .server-card {
    display: none !important;
}

/* 显示匹配地区的卡片 - 需要为每个可能的地区代码创建规则 */
#groups-container[data-region="CN"] .server-card[data-region="CN"],
#groups-container[data-region="US"] .server-card[data-region="US"],
#groups-container[data-region="JP"] .server-card[data-region="JP"],
#groups-container[data-region="KR"] .server-card[data-region="KR"],
#groups-container[data-region="SG"] .server-card[data-region="SG"],
#groups-container[data-region="HK"] .server-card[data-region="HK"],
#groups-container[data-region="TW"] .server-card[data-region="TW"],
#groups-container[data-region="GB"] .server-card[data-region="GB"],
#groups-container[data-region="DE"] .server-card[data-region="DE"],
#groups-container[data-region="FR"] .server-card[data-region="FR"],
#groups-container[data-region="NL"] .server-card[data-region="NL"],
#groups-container[data-region="CA"] .server-card[data-region="CA"],
#groups-container[data-region="AU"] .server-card[data-region="AU"],
#groups-container[data-region="IN"] .server-card[data-region="IN"],
#groups-container[data-region="BR"] .server-card[data-region="BR"],
#groups-container[data-region="RU"] .server-card[data-region="RU"],
#groups-container[data-region="IT"] .server-card[data-region="IT"],
#groups-container[data-region="ES"] .server-card[data-region="ES"],
#groups-container[data-region="MX"] .server-card[data-region="MX"],
#groups-container[data-region="AR"] .server-card[data-region="AR"] {
    display: flex !important;
}

/* 通用地区匹配 - 使用属性值相等选择器 */
#groups-container[data-region]:not([data-region=""]) .server-card[data-region] {
    /* 这个规则会被上面的具体规则覆盖 */
}

/******************************************
 * 标签筛选
 * 由 JavaScript 控制的特殊类
 ******************************************/
.server-card.tag-hidden {
    display: none !important;
}

/******************************************
 * 组合筛选优先级处理
 * 确保多个筛选条件同时生效
 ******************************************/

/* 确保被标记为隐藏的卡片始终隐藏 */
.server-card[style*="display: none"],
.server-card[style*="display:none"] {
    display: none !important;
}

/******************************************
 * 性能优化
 * 使用 will-change 提示浏览器优化
 ******************************************/
#groups-container {
    will-change: contents;
}

.server-card {
    will-change: display;
}

/******************************************
 * 过渡效果（可选）
 * 为筛选添加平滑过渡
 ******************************************/
.server-card {
    transition: opacity 0.2s ease-in-out;
}

.server-card[style*="display: none"],
.server-card.tag-hidden {
    opacity: 0 !important;
    transition: opacity 0.2s ease-in-out;
}

/******************************************
 * 辅助样式
 * 确保筛选后的布局正确
 ******************************************/

/* 防止空容器显示 */
#groups-container:empty::after {
    content: "暂无匹配的服务器";
    display: block;
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1rem;
}

/* 确保网格布局在筛选后正确显示 */
/* 注意：不要在 #groups-container 上设置网格布局，
   因为实际的网格布局应该在 #card-grid-container 上 */

/* 移除隐藏元素的间距 - 注释掉破坏Grid布局的规则 */
/* 原有的absolute定位会破坏Grid布局，已删除 */