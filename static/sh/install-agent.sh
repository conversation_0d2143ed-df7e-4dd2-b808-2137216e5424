#!/bin/bash
# DStatus 探针安装/更新脚本：支持 root 与非 root 双模式
set -Eeuo pipefail
trap 'echo "❌ 出错：行 $LINENO"; exit 1' ERR

# ========= 可由外部注入的占位变量 =========
DOWNLOAD_URL="##DOWNLOAD_URL##"
API_KEY="##API_KEY##"
API_PORT="##API_PORT##"

# ========= 判断是否 root =========
IS_ROOT=false
if [ "$(id -u)" -eq 0 ]; then
  IS_ROOT=true
  PREFIX="/usr"
  ETC_DIR="/etc/neko-status"
  SERVICE_MODE="system"
else
  IS_ROOT=false
  PREFIX="$HOME/.local"
  ETC_DIR="$HOME/.config/neko-status"
  SERVICE_MODE="user"
  mkdir -p "$PREFIX/bin" "$ETC_DIR"
fi
BIN_DIR="$PREFIX/bin"

echo "👉 安装模式：$([ "$IS_ROOT" = true ] && echo '系统级 (root)' || echo '用户级 (无 root)')"
echo "二进制安装到: $BIN_DIR"
echo "配置文件目录: $ETC_DIR"

# ========= 步骤 1/8: 检测系统架构 =========
echo "[步骤1/8] 检测系统架构..."
OS_TYPE=$(uname -s | tr 'A-Z' 'a-z')
ARCH_RAW=$(uname -m)
case "$ARCH_RAW" in
  x86_64|amd64) ARCH="amd64" ;;
  i?86)         ARCH="386"  ;;
  aarch64|arm64)ARCH="arm64";;
  armv7*|armhf) ARCH="arm7" ;;
  arm*)         grep -q "v6" /proc/cpuinfo 2>/dev/null && ARCH="arm6" || ARCH="arm7" ;;
  *)            ARCH="amd64" ;;
esac
echo "系统: $OS_TYPE，架构: $ARCH"

# ========= 步骤 2/8: 确保 wget/curl =========
echo "[步骤2/8] 检查下载工具..."
DOWNLOAD_TOOL=""
if command -v wget &>/dev/null; then
  DOWNLOAD_TOOL="wget"
elif command -v curl &>/dev/null; then
  DOWNLOAD_TOOL="curl"
else
  if [ "$IS_ROOT" = true ]; then
    echo "未检测到 wget/curl，尝试自动安装 wget..."
    if   command -v apt-get &>/dev/null;  then apt-get update -y && apt-get install wget -y
    elif command -v yum &>/dev/null;      then yum install wget -y
    elif command -v apk &>/dev/null;      then apk add wget
    else echo "❌ 无法自动安装下载工具"; exit 1; fi
    DOWNLOAD_TOOL="wget"
  else
    echo "❌ 您没有 root 权限且系统缺少 wget/curl，请先手动安装其中之一。"
    exit 1
  fi
fi
echo "使用 $DOWNLOAD_TOOL 完成下载"

# ========= 步骤 3/8: 下载探针 =========
echo "[步骤3/8] 下载探针..."
TMP_FILE=$(mktemp /tmp/neko-status.XXXXXX)
if [[ "$DOWNLOAD_URL" =~ /neko-status$ ]]; then
  DL_BASE="${DOWNLOAD_URL%/neko-status}"
else
  DL_BASE="$DOWNLOAD_URL"
fi
ARCH_URL="$DL_BASE/neko-status_${OS_TYPE}_${ARCH}"
GENERIC_URL="$DL_BASE/neko-status"

fetch() {
  local url="$1"
  if [ "$DOWNLOAD_TOOL" = "wget" ]; then
    wget -q --timeout=30 --tries=2 -O "$TMP_FILE" "$url"
  else
    curl -sSfL --connect-timeout 30 --retry 2 -o "$TMP_FILE" "$url"
  fi
}

echo "尝试架构文件：$ARCH_URL"
if ! fetch "$ARCH_URL"; then
  echo "架构文件失败，尝试通用文件：$GENERIC_URL"
  fetch "$GENERIC_URL" || { echo "❌ 所有下载尝试失败"; rm -f "$TMP_FILE"; exit 1; }
fi
[ -s "$TMP_FILE" ] || { echo "❌ 下载文件为空"; rm -f "$TMP_FILE"; exit 1; }

# ========= 步骤 4/8: 安装二进制 =========
echo "[步骤4/8] 安装探针..."
install -Dm755 "$TMP_FILE" "$BIN_DIR/neko-status"
rm -f "$TMP_FILE"
"$BIN_DIR/neko-status" -v >/dev/null || { echo "❌ 二进制无法执行"; exit 1; }
echo "探针已放置到 $BIN_DIR/neko-status"

# ========= 步骤 5/8: 写配置 =========
echo "[步骤5/8] 写配置文件..."
CONFIG_FILE="$ETC_DIR/config.yaml"
[ -f "$CONFIG_FILE" ] && cp "$CONFIG_FILE" "${CONFIG_FILE}.bak.$(date +%s)"
mkdir -p "$ETC_DIR"
cat > "$CONFIG_FILE" <<EOF
key: "$(printf '%s' "$API_KEY" | sed 's/"/\\"/g')"
port: $API_PORT
debug: false
EOF
echo "配置写入完成（已自动备份旧版）"

# ========= 步骤 6/8: 停止旧实例 =========
echo "[步骤6/8] 停止旧实例..."
pkill -f "neko-status -c $CONFIG_FILE" 2>/dev/null || true
pkill -9 -f "$BIN_DIR/neko-status -c $CONFIG_FILE" 2>/dev/null || true
sleep 1

# ========= 步骤 7/8: 创建服务 =========
echo "[步骤7/8] 创建服务..."
SERVICE_CREATED=false

if [ "$SERVICE_MODE" = "system" ]; then
  # ----- 系统级 -----
  if command -v systemctl &>/dev/null; then
    cat > /etc/systemd/system/nekonekostatus.service <<SERVICE
[Unit]
Description=nekonekostatus
After=network.target
[Service]
ExecStart=$BIN_DIR/neko-status -c $CONFIG_FILE
Restart=always
RestartSec=5
[Install]
WantedBy=multi-user.target
SERVICE
    systemctl daemon-reload
    SERVICE_CREATED=true
    SERVICE_START_CMD="systemctl start nekonekostatus"
    SERVICE_ENABLE_CMD="systemctl enable nekonekostatus"
  elif command -v rc-service &>/dev/null && [ -f /etc/alpine-release ]; then
    cat > /etc/init.d/nekonekostatus <<'OPENRC'
#!/sbin/openrc-run
command="$BIN_DIR/neko-status"
command_args="-c $CONFIG_FILE"
command_background=true
pidfile="/run/nekonekostatus.pid"
depend() { need net; }
OPENRC
    chmod +x /etc/init.d/nekonekostatus
    SERVICE_CREATED=true
    SERVICE_START_CMD="rc-service nekonekostatus start"
    SERVICE_ENABLE_CMD="rc-update add nekonekostatus default"
  elif [ -d /etc/init.d ]; then
    cat > /etc/init.d/nekonekostatus <<INITD
#!/bin/sh
### BEGIN INIT INFO
# Provides: nekonekostatus
# Required-Start: \$network \$remote_fs \$syslog
# Required-Stop:  \$network \$remote_fs \$syslog
# Default-Start:  2 3 4 5
# Default-Stop:   0 1 6
### END INIT INFO
DAEMON="$BIN_DIR/neko-status"
ARGS="-c $CONFIG_FILE"
PIDFILE="/var/run/nekonekostatus.pid"
start() { start-stop-daemon --start --background --pidfile \$PIDFILE --make-pidfile --exec \$DAEMON -- \$ARGS; }
stop()  { start-stop-daemon --stop --pidfile \$PIDFILE; rm -f \$PIDFILE; }
case "\$1" in start) start ;; stop) stop ;; restart) stop; start ;; *) echo "Usage: \$0 {start|stop|restart}"; exit 1 ;; esac
INITD
    chmod +x /etc/init.d/nekonekostatus
    SERVICE_CREATED=true
    SERVICE_START_CMD="/etc/init.d/nekonekostatus start"
    SERVICE_ENABLE_CMD="update-rc.d nekonekostatus defaults || true"
  fi

else
  # ----- 用户级 -----
  if command -v systemctl &>/dev/null && systemctl --user show-environment &>/dev/null; then
    mkdir -p "$HOME/.config/systemd/user"
    cat > "$HOME/.config/systemd/user/nekonekostatus.service" <<SERVICE
[Unit]
Description=nekonekostatus (user)
After=network.target
[Service]
ExecStart=$BIN_DIR/neko-status -c $CONFIG_FILE
Restart=always
RestartSec=5
[Install]
WantedBy=default.target
SERVICE
    systemctl --user daemon-reload
    SERVICE_CREATED=true
    SERVICE_START_CMD="systemctl --user start nekonekostatus"
    SERVICE_ENABLE_CMD="systemctl --user enable nekonekostatus"
    # 若未启用 linger，提醒一次
    loginctl show-user "$USER" | grep -q Linger=yes || \
      echo "⚠️ 若想离线时仍保持运行，可让管理员执行：loginctl enable-linger $USER"
  else
    # 无 systemd-user，提供简单脚本
    cat > "$BIN_DIR/nekonekostatus-start" <<'SCRIPT'
#!/bin/sh
nohup "$BIN_DIR/neko-status" -c "$CONFIG_FILE" > "$HOME/.local/nekonekostatus.log" 2>&1 &
echo $! > "$HOME/.local/nekonekostatus.pid"
SCRIPT
    chmod +x "$BIN_DIR/nekonekostatus-start"
    cat > "$BIN_DIR/nekonekostatus-stop" <<'SCRIPT'
#!/bin/sh
[ -f "$HOME/.local/nekonekostatus.pid" ] && kill "$(cat "$HOME/.local/nekonekostatus.pid")" 2>/dev/null
SCRIPT
    chmod +x "$BIN_DIR/nekonekostatus-stop"
    SERVICE_CREATED=true
    SERVICE_START_CMD="$BIN_DIR/nekonekostatus-start"
    SERVICE_ENABLE_CMD="echo '@reboot $SERVICE_START_CMD' | crontab -l 2>/dev/null | { cat; echo; } | crontab -"
    echo "✅ 已生成简单启动脚本，可手动添加到 crontab：@reboot $SERVICE_START_CMD"
  fi
fi

# ========= 步骤 8/8: 启动服务 =========
echo "[步骤8/8] 启动服务..."
if [ "$SERVICE_CREATED" = true ]; then
  eval "$SERVICE_START_CMD"
  sleep 3
  pgrep -f "neko-status -c $CONFIG_FILE" >/dev/null && echo "🚀 服务启动成功" || echo "⚠️ 未检测到运行进程，请检查日志。"
  # 开机自启（如适用）
  [ -n "${SERVICE_ENABLE_CMD:-}" ] && eval "$SERVICE_ENABLE_CMD" >/dev/null 2>&1
else
  echo "⚠️ 无法创建服务，请手动运行：$BIN_DIR/neko-status -c $CONFIG_FILE &"
fi

echo "🎉 安装完成。"
[ "$IS_ROOT" = false ] && echo "👉 请确认已将 $BIN_DIR 加入 PATH。若尚未生效，在 shell 配置文件中添加： export PATH=\"$BIN_DIR:\$PATH\""

exit 0