/**
 * 统一主题管理器
 * 整合所有主题切换逻辑，避免重复和冲突
 * 支持亮色主题和暗色主题
 * @updated 2024-05-19 整合所有主题功能
 */

// 立即执行函数，避免污染全局作用域
(function() {
    // 主题类型
    const THEMES = {
        LIGHT: 'light',
        DARK: 'dark'
    };

    // 主题切换速度（毫秒）
    const TRANSITION_DURATION = 150;

    // 获取端口隔离的缓存键
    function getThemeCacheKey() {
        const port = location.port || '80';
        const host = location.hostname.replace(/\./g, '_');
        return `theme_${host}_${port}`;
    }

    // 从本地存储获取主题（带端口隔离）
    function getSavedTheme() {
        return localStorage.getItem(getThemeCacheKey()) || THEMES.DARK;
    }

    // 保存主题到本地存储（带端口隔离）
    function saveTheme(theme) {
        localStorage.setItem(getThemeCacheKey(), theme);
    }

    // 获取系统主题偏好
    function getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ?
            THEMES.DARK : THEMES.LIGHT;
    }

    // 应用主题
    function applyTheme(theme, skipAnimation = false) {
        // 1. 添加切换中标记，并确保禁用所有过渡
        document.documentElement.classList.add('theme-switching');
        // 可以在CSS中定义 .theme-switching * { transition: none !important; }
        // 或者更直接地，如果知道哪些元素有过渡，可以在这里用JS移除
    
        // 2. 显示主题切换遮罩层 (确保它能覆盖所有内容)
        // 页面初始加载时跳过动画
        const overlay = document.getElementById('theme-transition-overlay');
        if (overlay && !skipAnimation) {
            overlay.classList.remove('hidden');
            overlay.style.opacity = '1'; // 使用 style.opacity 以确保立即生效且覆盖CSS过渡
            // 可以考虑给body或主内容区域也设置 opacity: 0; pointer-events: none;
            // document.body.style.opacity = '0';
        }
    
        // 3. 立即切换HTML的dark class 和 更新按钮状态
        // 移除旧的过渡类，以防影响此次class切换
        document.documentElement.classList.remove('theme-transition');
        if (theme === THEMES.DARK) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        updateThemeToggleButton(theme);
    
        // 4. 立即更新所有需要响应主题变化的元素
        // requestAnimationFrame确保在下一次重绘前执行，但可能仍有微小延迟
        // 直接调用，依赖 updateThemeElements 内部的优化
        const isDarkNow = document.documentElement.classList.contains('dark');
        updateThemeElements(isDarkNow);
    
        // 5. 触发自定义事件，通知其他组件主题已变化 (在所有核心更新后)
        // 这个事件应该在所有视觉更新基本完成后触发
        const event = new CustomEvent('theme:changed', {
            detail: { isDark: isDarkNow, theme },
            bubbles: true
        });
        document.dispatchEvent(event);
    
    
        // 6. 设置一个延时 (例如 50ms-100ms)，等待浏览器完成渲染和JS更新
        // 这个延时是为了确保所有上述更改都已生效，并且DOM稳定
        // 之前的500ms可能过长，如果问题是微小的不同步，短延时可能更好
        // TRANSITION_DURATION (150ms) 也可以考虑在这里使用，或者一个更短的值
        // 关键是这个延时要大于任何潜在的内部异步更新或CSS渲染时间
        const STABILIZATION_DELAY = 50; // 调整这个值
    
        setTimeout(() => {
            // 7. 恢复过渡效果 (如果需要平滑的遮罩消失)
            // document.documentElement.classList.add('theme-transition'); // 如果遮罩消失需要动画
    
            // 8. 隐藏主题切换遮罩层
            if (overlay && !skipAnimation) {
                // overlay.classList.remove('opacity-50'); // 之前是 opacity-50
                overlay.style.opacity = '0'; // 直接用JS控制消失
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 100); // 遮罩消失的动画时间
            }
            // document.body.style.opacity = '1'; // 恢复body
    
            // 9. 移除切换中标记，恢复正常过渡
            document.documentElement.classList.remove('theme-switching');
            // 确保在所有操作完成后再添加回主要的过渡类，如果之前移除了
            document.documentElement.classList.add('theme-transition');
    
    
        }, STABILIZATION_DELAY);
    }

    // 切换主题
    function toggleTheme() {
        // 获取当前主题
        const currentTheme = getSavedTheme();

        // 切换到相反的主题
        const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;

        // 保存主题偏好
        saveTheme(newTheme);

        // 应用新主题 (已包含过渡动画和更新元素)
        applyTheme(newTheme);

        // 不再触发theme:changed事件，避免与MutationObserver重复触发
    }

    // 更新主题切换按钮状态
    function updateThemeToggleButton(theme) {
        const themeToggleBtn = document.getElementById('theme-toggle');
        if (!themeToggleBtn) return;

        const themeToggleIcon = document.getElementById('theme-toggle-icon');
        const themeToggleText = document.getElementById('theme-toggle-text');

        if (themeToggleIcon) {
            // 支持 Tabler Icons 格式
            if (themeToggleIcon.classList.contains('ti')) {
                // 移除当前图标类
                themeToggleIcon.classList.remove('ti-moon', 'ti-sun');
                // 添加新图标类
                if (theme === THEMES.DARK) {
                    themeToggleIcon.classList.add('ti-sun');
                } else {
                    themeToggleIcon.classList.add('ti-moon');
                }
            } else {
                // 如果不是Tabler Icons，设置为Tabler Icons格式
                themeToggleIcon.className = theme === THEMES.DARK ? 'ti ti-sun' : 'ti ti-moon';
            }
        }

        if (themeToggleText) {
            themeToggleText.textContent = theme === THEMES.DARK ? '夜间模式' : '日间模式';
        }
    }

    // 更新需要响应主题变化的元素
    function updateThemeElements(isDark) {
        // 一次性添加theme-border类到所有卡片
        // 这样在初始化时就添加了类，不需要在主题切换时再次添加
        document.querySelectorAll('.card, .server-card, .dashboard-inner-card').forEach(card => {
            if (!card.classList.contains('theme-border')) {
                card.classList.add('theme-border');
            }
        });

        // 避免使用requestAnimationFrame触发重绘，因为这可能导致闪烁
        // 而是直接处理所有需要样式更新的元素

        // 处理旧的主题类名兼容性
        handleLegacyThemeClasses(isDark);

        // 更新图表颜色（如果有）
        const charts = window.charts || [];
        for (const chartId in charts) {
            const chart = charts[chartId];
            if (chart && typeof chart.update === 'function') {
                // 更新图表主题
                const newOptions = getChartThemeOptions(isDark);
                chart.update({
                    options: newOptions
                });
            }
        }

        // 触发自定义事件，通知其他组件
        const event = new CustomEvent('theme:updated', {
            detail: { isDark },
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // 处理旧的主题类名兼容性
    function handleLegacyThemeClasses(isDark) {
        // 定义旧类名到新类名的映射
        const legacyClassMappings = {
            // 文本颜色
            'text-theme-light-text': isDark ? 'text-slate-800' : 'text-white',
            'text-theme-dark-text': isDark ? 'text-white' : 'text-slate-800',
            'text-theme-light-secondary': isDark ? 'text-slate-600' : 'text-slate-300',
            'text-theme-dark-secondary': isDark ? 'text-slate-300' : 'text-slate-600',

            // 背景颜色
            'bg-theme-light-hover': isDark ? 'bg-slate-200/10' : 'bg-slate-700/10',
            'bg-theme-dark-hover': isDark ? 'bg-slate-700/10' : 'bg-slate-200/10'
        };

        // 遍历所有元素，应用新的类名
        for (const [legacyClass, newClass] of Object.entries(legacyClassMappings)) {
            document.querySelectorAll(`.${legacyClass}`).forEach(element => {
                // 移除旧类名
                element.classList.remove(legacyClass);
                // 添加新类名
                element.classList.add(newClass);
            });
        }

        // 将所有带有旧边框类的元素添加theme-border类
        document.querySelectorAll('.border-theme-light-border, .border-theme-dark-border').forEach(element => {
            // 移除旧类名
            element.classList.remove('border-theme-light-border', 'border-theme-dark-border');
            // 添加theme-border类
            element.classList.add('theme-border');
        });

        // 处理stats页面中的特定元素
        handleStatsPageElements(isDark);
    }

    // 处理stats页面中的特定元素
    function handleStatsPageElements(isDark) {
        // 处理Material Icons图标颜色
        document.querySelectorAll('.material-icons').forEach(icon => {
            // 根据图标的父元素或上下文设置颜色
            const parentText = icon.parentElement?.textContent?.trim() || '';

            // 处理流量相关图标
            if (icon.textContent === 'data_usage') {
                if (parentText.includes('已用')) {
                    icon.style.color = isDark ? '#34d399' : '#10b981'; // 绿色
                } else if (parentText.includes('剩余')) {
                    icon.style.color = isDark ? '#fbbf24' : '#f59e0b'; // 黄色
                } else if (parentText.includes('总量')) {
                    icon.style.color = isDark ? '#60a5fa' : '#3b82f6'; // 蓝色
                } else {
                    // 默认颜色
                    icon.style.color = isDark ? '#cbd5e1' : '#64748b';
                }
            } else if (icon.textContent === 'arrow_downward') {
                icon.style.color = isDark ? '#34d399' : '#10b981'; // 绿色
            } else if (icon.textContent === 'arrow_upward') {
                icon.style.color = isDark ? '#60a5fa' : '#3b82f6'; // 蓝色
            }
        });

        // 处理流量统计文本
        document.querySelectorAll('#monthly-used, #monthly-remaining, #monthly-limit, #total-download, #total-upload, #NET_IN, #NET_OUT').forEach(element => {
            if (element) {
                element.style.color = isDark ? '#f8fafc' : '#1e293b';
            }
        });

        // 处理所有带有固定Tailwind类的文本元素
        document.querySelectorAll('.text-slate-800.dark\\:text-white, .text-slate-700.dark\\:text-slate-300').forEach(element => {
            // 强制应用正确的颜色，覆盖Tailwind类
            element.style.color = isDark ? '#f8fafc' : '#1e293b';
        });

        // 处理网络设备和硬盘使用详情卡片
        // 使用更通用的方法查找卡片
        document.querySelectorAll('.card').forEach(card => {
            // 查找卡片标题
            const cardTitle = card.querySelector('h3');
            if (cardTitle && (cardTitle.textContent.includes('网络设备') || cardTitle.textContent.includes('硬盘使用详情'))) {
                // 确保卡片背景颜色正确
                card.style.backgroundColor = isDark ? 'var(--dark-card-bg-color)' : 'var(--light-card-bg-color)';

                // 处理卡片内的文本元素
                card.querySelectorAll('th, td, .text-theme-dark-text, .text-theme-light-text').forEach(textElement => {
                    textElement.style.color = isDark ? '#f8fafc' : '#1e293b';
                });

                // 处理卡片内的次要文本元素
                card.querySelectorAll('.text-theme-dark-secondary, .text-theme-light-secondary').forEach(secondaryTextElement => {
                    secondaryTextElement.style.color = isDark ? '#cbd5e1' : '#64748b';
                });
            }
        });

        // 备用方法：使用更通用的选择器
        document.querySelectorAll('#network-devices-table, #disk-usage-table').forEach(table => {
            if (table) {
                const card = table.closest('.card');
                if (card) {
                    // 确保卡片背景颜色正确
                    card.style.backgroundColor = isDark ? 'var(--dark-card-bg-color)' : 'var(--light-card-bg-color)';

                    // 处理卡片内的文本元素
                    card.querySelectorAll('th, td').forEach(textElement => {
                        if (textElement.classList.contains('text-theme-dark-secondary') ||
                            textElement.classList.contains('text-theme-light-secondary')) {
                            textElement.style.color = isDark ? '#cbd5e1' : '#64748b';
                        } else {
                            textElement.style.color = isDark ? '#f8fafc' : '#1e293b';
                        }
                    });
                }
            }
        });

        // 处理网络质量监控图表
        const networkQualityChart = document.getElementById('network-quality-chart');
        if (networkQualityChart) {
            // 处理图表容器内的文本元素
            networkQualityChart.querySelectorAll('.echarts-for-react, .echarts-wrap').forEach(chartElement => {
                // 尝试获取ECharts实例
                if (window.NetworkQualityUnifiedManager && window.NetworkQualityUnifiedManager.chart) {
                    console.log('找到网络质量监控图表实例，重新渲染');
                    // 直接调用渲染函数
                    window.NetworkQualityUnifiedManager._renderChart();
                }
            });
        }

        // 触发图表更新事件
        const chartUpdateEvent = new CustomEvent('charts:theme-changed', {
            detail: { isDark },
            bubbles: true
        });
        document.dispatchEvent(chartUpdateEvent);
    }

    // 获取图表主题选项
    function getChartThemeOptions(isDark) {
        return {
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            grid: {
                borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light'
            }
        };
    }

    // 初始化主题
    function initTheme() {
        // 设置主题初始化标记，阻止任何过渡效果
        document.documentElement.classList.add('theme-initializing');

        const savedTheme = getSavedTheme();

        // 使用applyTheme但跳过动画，避免页面加载时的闪烁
        applyTheme(savedTheme, true);

        // 使用requestAnimationFrame确保在下一次渲染前完成初始化
        requestAnimationFrame(() => {
            // 添加主题初始化标记
            document.documentElement.classList.add('theme-initialized');
            document.documentElement.classList.remove('theme-initializing');

            // 延迟设置，确保在DOM完全加载后执行，同时不会产生闪烁
            document.addEventListener('DOMContentLoaded', () => {
                // 延迟一小段时间再更新元素以确保样式已应用
                setTimeout(() => {
                    const isDark = document.documentElement.classList.contains('dark');
                    updateThemeElements(isDark);
                }, 10);
            });
        });
    }

    // 初始化主题切换按钮
    function initThemeToggleButton() {
        // 不再添加事件监听器，让各页面自行处理
        // 这样可以避免重复的事件监听器和双重触发
        const themeToggleBtn = document.getElementById('theme-toggle');
        if (themeToggleBtn) {
            console.log('主题切换按钮已找到，等待页面脚本绑定事件');
        }
    }

    // 导出到全局作用域，以便其他脚本可以使用
    window.themeManager = {
        toggleTheme: toggleTheme,
        applyTheme: applyTheme,
        getSavedTheme: getSavedTheme,
        getSystemTheme: getSystemTheme,
        updateThemeElements: updateThemeElements
    };

    // 立即初始化主题，避免闪烁
    initTheme();

    // 当DOM加载完成后，初始化主题切换按钮
    document.addEventListener('DOMContentLoaded', function() {
        initThemeToggleButton();

        // 初始化时立即检查当前主题并更新元素
        const isDark = document.documentElement.classList.contains('dark');
        updateThemeElements(isDark);

        console.log(`主题管理器已初始化，当前主题: ${isDark ? '暗色' : '亮色'}`);
    });
})();
