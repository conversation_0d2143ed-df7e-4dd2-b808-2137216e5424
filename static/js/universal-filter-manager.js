/**
 * Universal Filter Manager (UF)
 * 统一管理服务器列表的筛选状态，使用 CSS 属性选择器实现高性能筛选
 */
(function() {
    'use strict';
    
    // 筛选状态对象
    const state = {
        group: 'all',      // 分组ID
        status: 'ALL',     // 状态: ALL/ONLINE/OFFLINE
        expiry: '',        // 到期时间: ''/'3'/'7'/'30'
        region: '',        // 地区代码
        tags: new Set()    // 标签集合
    };
    
    // 回调函数列表
    const callbacks = [];
    
    /**
     * 获取筛选容器
     * 支持多个容器 ID，优先使用 groups-container，fallback 到 node-status-container
     */
    function getFilterContainer() {
        return document.getElementById('groups-container') || 
               document.getElementById('node-status-container');
    }
    
    /**
     * 更新分组可见性
     * 使用 JavaScript 控制分组筛选，因为 CSS 无法处理动态分组名称
     */
    function updateGroupVisibility() {
        const container = getFilterContainer();
        if (!container) return;
        
        const cards = container.querySelectorAll('.server-card');
        cards.forEach(card => {
            // 获取卡片的分组属性，处理 null/undefined/空字符串
            const cardGroup = card.getAttribute('data-group') || '';
            
            // 判断是否应该显示该卡片
            const shouldShow = state.group === 'all' || cardGroup === state.group;
            
            // 使用 data 属性标记分组隐藏状态
            if (shouldShow) {
                card.removeAttribute('data-hidden-by-group');
            } else {
                card.setAttribute('data-hidden-by-group', 'true');
            }
        });
    }
    
    /**
     * 更新到期时间筛选
     * 使用 JavaScript 控制，因为 CSS 无法进行数值比较
     */
    function updateExpiryVisibility() {
        const container = getFilterContainer();
        if (!container) return;
        
        const cards = container.querySelectorAll('.server-card');
        cards.forEach(card => {
            // 如果没有设置到期筛选，显示所有卡片
            if (!state.expiry || state.expiry === '') {
                card.removeAttribute('data-hidden-by-expiry');
                return;
            }
            
            // 获取卡片的到期天数
            const expiryDays = parseInt(card.getAttribute('data-expiry-days') || '999', 10);
            const filterDays = parseInt(state.expiry, 10);
            
            // 判断是否应该显示该卡片
            const shouldShow = expiryDays <= filterDays;
            
            // 使用 data 属性标记到期隐藏状态
            if (shouldShow) {
                card.removeAttribute('data-hidden-by-expiry');
            } else {
                card.setAttribute('data-hidden-by-expiry', 'true');
            }
        });
    }
    
    /**
     * 更新容器的 data-* 属性并应用筛选
     */
    function updateContainer() {
        const container = getFilterContainer();
        if (!container) return;
        
        // 设置筛选属性
        container.setAttribute('data-group', state.group);
        container.setAttribute('data-status', state.status);
        container.setAttribute('data-expiry', state.expiry || '');
        container.setAttribute('data-region', state.region || '');
        
        // 应用分组筛选（JavaScript 控制）
        updateGroupVisibility();
        
        // 应用到期时间筛选（JavaScript 控制）
        updateExpiryVisibility();
        
        // 处理标签筛选（已有逻辑）
        updateTagFilters();
    }
    
    /**
     * 处理标签筛选（标签逻辑复杂，使用 class 方式）
     */
    function updateTagFilters() {
        const serverCards = document.querySelectorAll('.server-card');
        
        serverCards.forEach(card => {
            if (state.tags.size === 0) {
                // 无标签筛选时，移除隐藏类
                card.classList.remove('tag-hidden');
            } else {
                // 检查卡片是否包含任意选中的标签
                const cardTags = card.getAttribute('data-tags');
                const hasMatchingTag = cardTags && 
                    Array.from(state.tags).some(tag => 
                        cardTags.includes(tag)
                    );
                
                if (hasMatchingTag) {
                    card.classList.remove('tag-hidden');
                } else {
                    card.classList.add('tag-hidden');
                }
            }
        });
    }
    
    /**
     * 通知所有注册的回调函数
     */
    function notifyCallbacks() {
        const currentState = getState();
        callbacks.forEach(callback => {
            try {
                callback(currentState);
            } catch (error) {
                console.error('Filter callback error:', error);
            }
        });

        // After applying filters, refresh the sort
        if (window.SortManager) {
            window.SortManager.applyCurrentSort();
        }
    }
    
    /**
     * 获取当前状态的深拷贝
     */
    function getState() {
        return {
            group: state.group,
            status: state.status,
            expiry: state.expiry,
            region: state.region,
            tags: new Set(state.tags)
        };
    }
    
    // 公开 API
    const UF = {
        /**
         * 按分组筛选
         * @param {string} groupId - 分组ID，'all' 表示全部
         */
        filterByGroup(groupId) {
            if (state.group === groupId) return;
            
            state.group = groupId || 'all';
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 按状态筛选
         * @param {string} status - 状态值: ALL/ONLINE/OFFLINE
         */
        filterByStatus(status) {
            if (state.status === status) return;
            
            state.status = status || 'ALL';
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 按到期时间筛选
         * @param {string} days - 天数: ''/'3'/'7'/'30'
         */
        filterByExpiry(days) {
            if (state.expiry === days) return;
            
            state.expiry = days || '';
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 按地区筛选
         * @param {string} regionCode - 地区代码，空字符串表示全部
         */
        filterByRegion(regionCode) {
            if (state.region === regionCode) return;
            
            state.region = regionCode || '';
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 切换标签筛选
         * @param {string} tag - 标签名称
         */
        toggleTag(tag) {
            if (!tag) return;
            
            if (state.tags.has(tag)) {
                state.tags.delete(tag);
            } else {
                state.tags.add(tag);
            }
            
            updateTagFilters();
            notifyCallbacks();
        },
        
        /**
         * 注册状态变化回调
         * @param {Function} callback - 回调函数，接收当前状态对象
         * @returns {Function} 取消注册的函数
         */
        onChange(callback) {
            if (typeof callback !== 'function') return;
            
            callbacks.push(callback);
            
            // 返回取消注册函数
            return () => {
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            };
        },
        
        /**
         * 获取当前筛选状态
         * @returns {Object} 状态对象的深拷贝
         */
        getState,
        
        /**
         * 重置所有筛选
         */
        reset() {
            state.group = 'all';
            state.status = 'ALL';
            state.expiry = '';
            state.region = '';
            state.tags.clear();
            
            updateContainer();
            notifyCallbacks();
        },
        
        /**
         * 强制刷新所有筛选
         * 用于动态添加新卡片后的重新筛选
         */
        refresh() {
            updateContainer();
            notifyCallbacks();
        }
    };
    
    // 挂载到全局
    window.UF = UF;
})();