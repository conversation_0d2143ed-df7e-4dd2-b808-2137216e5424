/**
 * DataProcessor.js
 * 数据处理模块，负责处理和转换从WebSocket接收的数据
 * 包含数据验证、格式化、计算等功能
 */

window.DataProcessor = (function() {
    'use strict';

    // 配置常量
    const CONFIG = {
        // 数据有效期（毫秒）
        DATA_EXPIRY: 5 * 60 * 1000,
        
        // 网络速度单位转换
        NETWORK_UNITS: {
            bps: 1,
            kbps: 1024,
            mbps: 1024 * 1024,
            gbps: 1024 * 1024 * 1024,
            tbps: 1024 * 1024 * 1024 * 1024
        },

        // 存储单位转换
        STORAGE_UNITS: {
            B: 1,
            KB: 1024,
            MB: 1024 * 1024,
            GB: 1024 * 1024 * 1024,
            TB: 1024 * 1024 * 1024 * 1024
        }
    };

    // 内部状态
    const state = {
        lastProcessedData: null,
        lastProcessedTime: 0,
        processingCallbacks: {
            onDataProcessed: null,
            onError: null
        }
    };

    /**
     * 调试日志函数
     */
    function log(...args) {
        if (window.DEBUG_MODE) {
            console.log('[DataProcessor]', ...args);
        }
    }

    /**
     * 设置处理回调函数
     * @param {Object} callbacks - 回调函数对象
     */
    function setCallbacks(callbacks) {
        Object.assign(state.processingCallbacks, callbacks);
    }

    /**
     * 验证消息格式
     * @param {Object} message - 待验证的消息
     * @returns {boolean} 是否有效
     */
    function validateMessage(message) {
        // 检查基本结构
        if (!message || typeof message !== 'object') {
            log('无效的消息格式:', message);
            return false;
        }

        // 检查消息类型
        if (!message.type) {
            log('消息缺少类型字段:', message);
            return false;
        }

        return true;
    }

    /**
     * 验证节点数据结构
     * @param {Object} data - 节点数据
     * @returns {Object} 验证结果
     */
    function validateNodeData(data) {
        const result = {
            isValid: false,
            nodeCount: 0,
            errors: []
        };

        // 检查数据基本结构
        if (!data || typeof data !== 'object') {
            result.errors.push('节点数据为空或格式错误');
            return result;
        }

        const nodeIds = Object.keys(data);
        result.nodeCount = nodeIds.length;

        if (result.nodeCount === 0) {
            result.errors.push('没有节点数据');
            return result;
        }

        // 验证每个节点的数据结构（放宽验证条件）
        let validNodes = 0;
        nodeIds.forEach(nodeId => {
            const node = data[nodeId];

            // 检查基本属性
            if (!node || typeof node !== 'object') {
                result.errors.push(`节点 ${nodeId} 数据格式错误`);
                return;
            }

            // 放宽验证条件：只要有基本的节点对象就认为有效
            // 不强制要求stat字段，因为某些节点可能暂时没有统计数据
            validNodes++;

            // 如果有统计数据，进行验证但不影响节点有效性
            if (node.stat && typeof node.stat === 'object') {
                // 验证统计数据结构
                if (node.stat.net) {
                    const netStat = validateNetworkStats(node.stat.net);
                    if (!netStat.isValid) {
                        log(`节点 ${nodeId} 网络数据异常:`, netStat.errors);
                    }
                }
            }
        });

        result.isValid = validNodes > 0;
        if (validNodes < result.nodeCount) {
            result.errors.push(`${result.nodeCount - validNodes} 个节点数据不完整`);
        }

        return result;
    }

    /**
     * 验证网络统计数据
     * @param {Object} netStats - 网络统计数据
     * @returns {Object} 验证结果和标准化的数据
     */
    function validateNetworkStats(netStats) {
        const result = {
            isValid: false,
            data: {
                delta: { in: 0, out: 0 },
                total: { in: 0, out: 0 }
            },
            errors: []
        };

        if (!netStats) {
            result.errors.push('网络数据为空');
            return result;
        }

        try {
            // 处理实时带宽数据
            if (netStats.delta) {
                result.data.delta.in = Math.max(0, Number(netStats.delta.in) || 0);
                result.data.delta.out = Math.max(0, Number(netStats.delta.out) || 0);
            }

            // 处理总流量数据
            if (netStats.total) {
                result.data.total.in = Math.max(0, Number(netStats.total.in) || 0);
                result.data.total.out = Math.max(0, Number(netStats.total.out) || 0);
            }

            result.isValid = true;
        } catch (error) {
            result.errors.push(`网络数据处理错误: ${error.message}`);
            log('网络数据验证失败:', error);
        }

        return result;
    }

    /**
     * 计算总计统计数据
     * @param {Object} nodeData - 节点数据
     * @returns {Object} 总计数据
     */
    function calculateTotals(nodeData) {
        const totals = {
            nodes: Object.keys(nodeData).length,
            online: 0,
            offline: 0,
            download: 0,
            upload: 0,
            downloadTotal: 0,
            uploadTotal: 0,
            groups: {},
            regions: {}
        };

        // 处理每个节点
        Object.entries(nodeData).forEach(([nodeId, node]) => {
            try {
                // 判断节点状态
                const isOnline = node.stat && 
                                typeof node.stat === 'object' && 
                                !node.stat.offline;

                // 更新节点计数
                if (isOnline) {
                    totals.online++;

                    // 处理网络数据（仅在线节点）
                    if (node.stat.net) {
                        const netStats = validateNetworkStats(node.stat.net);
                        if (netStats.isValid) {
                            totals.download += netStats.data.delta.in;
                            totals.upload += netStats.data.delta.out;
                            totals.downloadTotal += netStats.data.total.in;
                            totals.uploadTotal += netStats.data.total.out;
                        }
                    }
                } else {
                    totals.offline++;
                }

                // 更新分组统计
                const groupId = node.group_id || 'ungrouped';
                if (!totals.groups[groupId]) {
                    totals.groups[groupId] = { total: 0, online: 0 };
                }
                totals.groups[groupId].total++;
                if (isOnline) {
                    totals.groups[groupId].online++;
                }

                // 更新地区统计
                const regionCode = node.data?.location?.country?.code || 
                                 node.regionCode || 
                                 'UNKNOWN';
                if (!totals.regions[regionCode]) {
                    totals.regions[regionCode] = { total: 0, online: 0 };
                }
                totals.regions[regionCode].total++;
                if (isOnline) {
                    totals.regions[regionCode].online++;
                }

            } catch (error) {
                log(`处理节点 ${nodeId} 时出错:`, error);
            }
        });

        return totals;
    }

    /**
     * 处理节点状态数据
     * @param {string} nodeId - 节点ID
     * @param {Object} node - 节点数据
     * @returns {Object} 处理后的状态数据
     */
    function processNodeStatus(nodeId, node) {
        const statusData = {
            nodeId: nodeId,
            status: 'offline',
            cpu: { usage: 0, cores: 0 },
            memory: { usage: 0, usagePercent: 0, total: 0, used: 0 },
            disk: { usage: 0, usagePercent: 0, total: 0, used: 0 },
            network: {
                download: 0,
                upload: 0,
                downloadTotal: 0,
                uploadTotal: 0
            },
            system: {
                uptime: 0,
                lastOnline: null
            },
            metadata: {
                group: node.group_id || 'default',
                region: node.data?.location?.country?.code || 'UNKNOWN',
                expireTime: node.expire_time || 0
            }
        };

        try {
            // 判断节点状态
            if (node.status === 2) {
                statusData.status = 'hidden';
            } else if (node.stat && typeof node.stat === 'object' && !node.stat.offline) {
                statusData.status = 'online';

                // 处理CPU数据
                if (node.stat.cpu) {
                    statusData.cpu.usage = (node.stat.cpu.multi * 100).toFixed(2);
                    statusData.cpu.cores = node.stat.cpu.cores || 0;
                }

                // 处理内存数据
                if (node.stat.mem && node.stat.mem.virtual) {
                    const total = node.stat.mem.virtual.total;
                    const used = node.stat.mem.virtual.used;
                    statusData.memory.total = total;
                    statusData.memory.used = used;
                    statusData.memory.usagePercent = total > 0 ? 
                        ((used / total) * 100).toFixed(2) : 0;
                }

                // 处理硬盘数据
                if (node.stat.disk) {
                    const total = node.stat.disk.total;
                    const used = node.stat.disk.used;
                    statusData.disk.total = total;
                    statusData.disk.used = used;
                    statusData.disk.usagePercent = total > 0 ? 
                        ((used / total) * 100).toFixed(1) : 0;
                }

                // 处理网络数据
                if (node.stat.net) {
                    const netStats = validateNetworkStats(node.stat.net);
                    if (netStats.isValid) {
                        statusData.network = {
                            download: netStats.data.delta.in,
                            upload: netStats.data.delta.out,
                            downloadTotal: netStats.data.total.in,
                            uploadTotal: netStats.data.total.out
                        };
                    }
                }

                // 处理系统数据
                if (node.stat.host) {
                    statusData.system.uptime = node.stat.host.uptime || 0;
                }
            }

            // 处理离线状态的最后在线时间
            if (statusData.status === 'offline' && node.last_online) {
                statusData.system.lastOnline = node.last_online;
            }

        } catch (error) {
            log(`处理节点 ${nodeId} 状态时出错:`, error);
            statusData.status = 'error';
        }

        return statusData;
    }

    /**
     * 格式化网络速度
     * @param {number} bytesPerSecond - 字节/秒
     * @param {boolean} isBytes - 是否以字节为单位（false为位）
     * @returns {string} 格式化后的速度字符串
     */
    function formatNetworkSpeed(bytesPerSecond, isBytes = false) {
        if (isNaN(bytesPerSecond) || bytesPerSecond === 0) {
            return isBytes ? '0 B/s' : '0 bps';
        }

        const value = isBytes ? bytesPerSecond : bytesPerSecond * 8;
        const units = isBytes ? ['B/s', 'KB/s', 'MB/s', 'GB/s', 'TB/s'] : 
                               ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps'];
        const threshold = isBytes ? 1024 : 1000;

        let i = 0;
        let convertedValue = value;

        while (convertedValue >= threshold && i < units.length - 1) {
            convertedValue /= threshold;
            i++;
        }

        return `${convertedValue.toFixed(2)} ${units[i]}`;
    }

    /**
     * 格式化存储大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小字符串
     */
    function formatStorageSize(bytes) {
        if (isNaN(bytes) || bytes === 0) return '0 B';

        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let i = 0;
        let value = bytes;

        while (value >= 1024 && i < units.length - 1) {
            value /= 1024;
            i++;
        }

        return `${value.toFixed(2)} ${units[i]}`;
    }

    /**
     * 格式化剩余天数
     * @param {number} expireTimestamp - 到期时间戳（秒）
     * @returns {string} 格式化后的剩余天数
     */
    function formatRemainingDays(expireTimestamp) {
        if (!expireTimestamp) return '永久';

        const now = Math.floor(Date.now() / 1000);
        const remainingSeconds = expireTimestamp - now;
        const remainingDays = Math.ceil(remainingSeconds / (24 * 60 * 60));

        if (remainingDays < 0) {
            return '已过期';
        } else if (remainingDays === 0) {
            return '今日到期';
        }
        return `${remainingDays} 天`;
    }

    /**
     * 格式化在线时间
     * @param {number} uptimeSeconds - 在线时间（秒）
     * @returns {string} 格式化后的在线时间
     */
    function formatUptime(uptimeSeconds) {
        if (!uptimeSeconds || uptimeSeconds <= 0) return '0天';

        const days = Math.floor(uptimeSeconds / 86400);
        return `${Math.max(1, days)}天`;
    }

    /**
     * 格式化最后在线时间
     * @param {number} lastOnlineTimestamp - 最后在线时间戳（秒）
     * @returns {string} 格式化后的最后在线时间
     */
    function formatLastOnline(lastOnlineTimestamp) {
        if (!lastOnlineTimestamp) return '未知';

        const lastOnlineDate = new Date(lastOnlineTimestamp * 1000);
        const now = new Date();
        const diffMs = now - lastOnlineDate;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffDays > 30) {
            return lastOnlineDate.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } else if (diffDays > 0) {
            return `${diffDays}天前`;
        } else if (diffHours > 0) {
            return `${diffHours}小时前`;
        } else if (diffMins > 0) {
            return `${diffMins}分钟前`;
        } else {
            return '刚刚';
        }
    }

    /**
     * 处理WebSocket消息
     * @param {Object} message - WebSocket消息
     * @param {boolean} fromCache - 是否来自缓存
     * @returns {Object|null} 处理结果
     */
    function processMessage(message, fromCache = false) {
        try {
            // 验证消息格式
            if (!validateMessage(message)) {
                return null;
            }

            const { type, data, timestamp } = message;

            // 处理stats类型消息
            if (type === 'stats') {
                return processStatsMessage(data, fromCache, timestamp);
            }

            // 处理其他类型的消息
            log(`未处理的消息类型: ${type}`);
            return null;

        } catch (error) {
            log('处理消息时出错:', error);
            if (state.processingCallbacks.onError) {
                state.processingCallbacks.onError(error);
            }
            return null;
        }
    }

    /**
     * 处理stats类型消息
     * @param {Object} data - 节点数据
     * @param {boolean} fromCache - 是否来自缓存
     * @param {number} timestamp - 时间戳
     * @returns {Object} 处理结果
     */
    function processStatsMessage(data, fromCache, timestamp) {
        // 验证节点数据
        const validation = validateNodeData(data);
        if (!validation.isValid) {
            // 🐛 调试日志 - 详细显示原始数据和验证错误
            console.log('[DataProcessor] 节点数据验证失败，原始数据:', {
                data: data,
                dataKeys: data ? Object.keys(data) : [],
                dataType: typeof data,
                dataSize: data ? Object.keys(data).length : 0,
                validation: validation,
                fromCache: fromCache,
                firstNodeSample: data && Object.keys(data).length > 0 ? {
                    nodeId: Object.keys(data)[0],
                    nodeData: data[Object.keys(data)[0]]
                } : null
            });
            log('节点数据验证失败:', validation.errors);
            return null;
        }

        // 🐛 调试日志 - 验证成功时也记录
        console.log('[DataProcessor] 节点数据验证成功:', {
            nodeCount: validation.nodeCount,
            fromCache: fromCache,
            hasErrors: validation.errors.length > 0,
            errors: validation.errors
        });

        // 计算总计数据
        const totals = calculateTotals(data);

        // 处理每个节点的状态数据
        const processedNodes = {};
        Object.entries(data).forEach(([nodeId, node]) => {
            processedNodes[nodeId] = processNodeStatus(nodeId, node);
        });

        // 保存处理结果
        const result = {
            type: 'processed_stats',
            timestamp: timestamp || Date.now(),
            fromCache: fromCache,
            nodeCount: validation.nodeCount,
            totals: totals,
            nodes: processedNodes,
            rawData: data // 保留原始数据供其他模块使用
        };

        // 更新内部状态
        state.lastProcessedData = result;
        state.lastProcessedTime = Date.now();

        // 触发处理完成回调
        if (state.processingCallbacks.onDataProcessed) {
            state.processingCallbacks.onDataProcessed(result);
        }

        return result;
    }

    /**
     * 获取最后处理的数据
     * @returns {Object|null} 最后处理的数据
     */
    function getLastProcessedData() {
        // 检查数据是否过期
        if (state.lastProcessedData && 
            (Date.now() - state.lastProcessedTime) > CONFIG.DATA_EXPIRY) {
            log('缓存的处理数据已过期');
            state.lastProcessedData = null;
        }

        return state.lastProcessedData;
    }

    /**
     * 清理处理数据
     */
    function clearProcessedData() {
        state.lastProcessedData = null;
        state.lastProcessedTime = 0;
        log('已清理处理数据');
    }

    // 导出公共API
    return {
        // 核心处理功能
        processMessage: processMessage,
        calculateTotals: calculateTotals,
        processNodeStatus: processNodeStatus,

        // 数据验证
        validateMessage: validateMessage,
        validateNodeData: validateNodeData,
        validateNetworkStats: validateNetworkStats,

        // 格式化功能
        formatNetworkSpeed: formatNetworkSpeed,
        formatStorageSize: formatStorageSize,
        formatRemainingDays: formatRemainingDays,
        formatUptime: formatUptime,
        formatLastOnline: formatLastOnline,

        // 状态管理
        setCallbacks: setCallbacks,
        getLastProcessedData: getLastProcessedData,
        clearProcessedData: clearProcessedData,

        // 配置访问
        getConfig: () => ({ ...CONFIG })
    };
})();