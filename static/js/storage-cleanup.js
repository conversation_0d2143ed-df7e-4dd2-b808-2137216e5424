/**
 * 存储清理脚本
 * 用于解决首页 card 和 list 视图无法获取节点数据的问题
 */

(function() {
    // 需要清理的键列表（已禁用的缓存）
    const keysToClean = [
        'dstatus_node_cache',           // 节点数据缓存（已禁用）
        'stats_connection_active',      // WebSocket 连接状态（已禁用）
        'stats_connection_timestamp',   // WebSocket 连接时间戳（已禁用）
        'stats_first_data_received',    // 首次数据接收标记（已禁用）
        'network_data_cache',           // 网络数据缓存（已禁用）
        'connection_tab_id',            // 标签页ID（已禁用）
        'stats_connection_state',       // 连接状态（已禁用）
        'theme',                        // 旧版本主题缓存（无端口隔离）
        'personalization-settings'      // 旧版本个性化设置（无端口隔离）
    ];

    // 需要保留的键列表（带端口隔离的新版本缓存）
    const keysToKeep = [
        'wallpaper-settings',           // 壁纸设置
        'node_display_settings'         // 节点显示设置
    ];

    // 获取当前端口隔离的缓存键
    function getPortIsolatedKeys() {
        const port = location.port || '80';
        const host = location.hostname.replace(/\./g, '_');
        return [
            `theme_${host}_${port}`,                        // 端口隔离的主题缓存
            `personalization-settings_${host}_${port}`      // 端口隔离的个性化设置
        ];
    }

    /**
     * 清理存储
     */
    function cleanStorage() {
        console.log('正在清理存储...');

        // 清理 localStorage 中的特定键
        keysToClean.forEach(key => {
            if (localStorage.getItem(key)) {
                console.log(`清理已禁用的缓存键: ${key}`);
                localStorage.removeItem(key);
            }
        });

        // 清理所有端口隔离的节点缓存（已禁用）
        const allKeys = Object.keys(localStorage);
        const patterns = [
            /^dstatus_node_cache_\d+$/,   // 端口隔离的节点缓存
            /^network_data_cache_/        // 网络数据缓存
        ];

        allKeys.forEach(key => {
            if (patterns.some(pattern => pattern.test(key))) {
                console.log(`清理匹配模式的已禁用缓存: ${key}`);
                localStorage.removeItem(key);
            }
        });

        // 检查是否有 SharedWorker 连接
        if (window.sharedClient) {
            console.log('重置 SharedWorker 连接...');
            try {
                // 请求最新数据
                window.sharedClient.requestLastData();
            } catch (e) {
                console.error('请求最新数据失败:', e);
            }
        }

        console.log('存储清理完成');
    }

    /**
     * 检查数据加载状态
     */
    function checkDataLoading() {
        // 检查是否有节点卡片
        const cards = document.querySelectorAll('.server-card');

        // 如果没有卡片或卡片数量为0，可能是数据未加载
        if (!cards || cards.length === 0) {
            console.log('未检测到节点卡片，可能是数据未加载');

            // 清理存储并重新加载数据
            cleanStorage();

            // 重新初始化 WebSocket 连接
            if (typeof initSharedWorkerClient === 'function') {
                console.log('重新初始化 SharedWorker 客户端...');
                initSharedWorkerClient().then(() => {
                    if (window.sharedClient) {
                        // 立即请求最新数据
                        window.sharedClient.requestLastData();

                        // 再次请求数据，确保数据是最新的
                        setTimeout(() => {
                            if (window.sharedClient) {
                                window.sharedClient.requestLastData();
                            }
                        }, 500);
                    }
                }).catch(() => {
                    console.warn('SharedWorker 初始化失败，回退到传统 WebSocket');
                    if (typeof initWebSocket === 'function') {
                        initWebSocket();
                    }
                });
            } else if (typeof initWebSocket === 'function') {
                console.log('重新初始化 WebSocket 连接...');
                initWebSocket();
            }

            return false;
        }

        // 检查卡片是否有数据
        let hasData = false;
        cards.forEach(card => {
            // 检查是否有CPU、内存等数据
            const cpuEl = card.querySelector('[id$="_CPU"]');
            const memEl = card.querySelector('[id$="_MEM"]');

            if (cpuEl && memEl &&
                cpuEl.textContent && cpuEl.textContent !== '0%' &&
                memEl.textContent && memEl.textContent !== '0%') {
                hasData = true;
            }
        });

        if (!hasData) {
            console.log('节点卡片没有数据，尝试重新加载');
            cleanStorage();
            return false;
        }

        return true;
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        console.log('存储清理脚本已加载');

        // 延迟检查，确保其他脚本已加载
        setTimeout(() => {
            // 检查数据加载状态
            if (!checkDataLoading()) {
                console.log('数据加载异常，已尝试修复');

                // 如果修复后仍然没有数据，考虑重新加载页面
                setTimeout(() => {
                    if (!checkDataLoading()) {
                        console.log('修复后仍然没有数据，考虑手动刷新页面');
                    }
                }, 5000);
            }
        }, 2000);
    });

    // 监听统计数据同步完成事件
    document.addEventListener('statsSyncComplete', function(event) {
        const detail = event.detail || {};
        console.log(`统计数据同步完成: ${detail.nodeCount || 0} 个节点, 来源: ${detail.fromCache ? '缓存' : '实时'}`);

        // 如果节点数量为0，可能是数据加载问题
        if (detail.nodeCount === 0) {
            console.log('同步完成但节点数量为0，尝试修复');
            cleanStorage();
        }
    });
})();
