/**
 * @file cacheManager.js
 * @description Manages caching of server statistics data in localStorage.
 */

// 避免重复声明
if (typeof window.CacheManager === 'undefined') {
  window.CacheManager = (() => {
    // 缓存配置
    const CACHE_CONFIG = {
      KEY: `dstatus_node_cache_${location.port || '80'}`,
      EXPIRY: 5 * 60 * 1000, // 缓存过期时间：5分钟
      VERSION: '1.0'  // 缓存版本，用于处理缓存结构变更
    };

    /**
     * Saves nodes data to localStorage.
     * @param {Object} nodesData - The nodes data to cache.
     */
    function save(nodesData) {
      if (!nodesData) return;

      try {
        const cacheData = {
          timestamp: Date.now(),
          version: CACHE_CONFIG.VERSION,
          nodes: nodesData
        };

        localStorage.setItem(CACHE_CONFIG.KEY, JSON.stringify(cacheData));
        // 禁用日志输出
        // if (window.DEBUG_MODE) {
        //   console.log(`[CacheManager] Node data cached, ${Object.keys(nodesData).length} nodes`);
        // }
      } catch (error) {
        console.error('[<PERSON>ache<PERSON>ana<PERSON>] Failed to save node data to cache:', error);
      }
    }

    /**
     * Loads nodes data from localStorage.
     * @returns {Object|null} The cached nodes data, or null if cache is invalid, expired, or not found.
     */
    function load() {
      try {
        const cachedDataStr = localStorage.getItem(CACHE_CONFIG.KEY);
        if (!cachedDataStr) return null;

        const cachedData = JSON.parse(cachedDataStr);

        // 检查缓存版本
        if (cachedData.version !== CACHE_CONFIG.VERSION) {
          // 禁用日志输出
          // if (window.DEBUG_MODE) {
          //   console.log('[CacheManager] Cache version mismatch, ignoring old cache.');
          // }
          localStorage.removeItem(CACHE_CONFIG.KEY);
          return null;
        }

        // 检查缓存是否过期
        const now = Date.now();
        if (now - cachedData.timestamp > CACHE_CONFIG.EXPIRY) {
          // 禁用日志输出
          // if (window.DEBUG_MODE) {
          //   console.log('[CacheManager] Cache expired, ignoring.');
          // }
          localStorage.removeItem(CACHE_CONFIG.KEY);
          return null;
        }

        // 禁用日志输出
        // if (window.DEBUG_MODE) {
        //   console.log(`[CacheManager] Loaded node data from cache, ${Object.keys(cachedData.nodes).length} nodes, cached at: ${new Date(cachedData.timestamp).toLocaleString()}`);
        // }
        return cachedData.nodes;
      } catch (error) {
        console.error('[CacheManager] Failed to load data from cache:', error);
        localStorage.removeItem(CACHE_CONFIG.KEY); // Clear corrupted cache
        return null;
      }
    }

    /**
     * Clears the cache.
     */
    function clear() {
        try {
            localStorage.removeItem(CACHE_CONFIG.KEY);
            // if (window.DEBUG_MODE) {
            //     console.log('[CacheManager] Cache cleared.');
            // }
        } catch (error) {
            console.error('[CacheManager] Failed to clear cache:', error);
        }
    }

    return {
      save,
      load,
      clear,
      // Expose config if needed externally, though generally not recommended
      // getConfig: () => ({ ...CACHE_CONFIG })
    };
  })();
}