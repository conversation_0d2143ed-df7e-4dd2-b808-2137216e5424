/**
 * @file cacheManager.js
 * @description Manages caching of server statistics data in localStorage.
 */

// 避免重复声明
if (typeof window.CacheManager === 'undefined') {
  window.CacheManager = (() => {
    // 缓存配置
    const CACHE_CONFIG = {
      KEY: `dstatus_node_cache_${location.port || '80'}`,
      EXPIRY: 5 * 60 * 1000, // 缓存过期时间：5分钟
      VERSION: '1.0'  // 缓存版本，用于处理缓存结构变更
    };

    /**
     * Saves nodes data to localStorage.
     * @param {Object} nodesData - The nodes data to cache.
     * @deprecated 实时数据不再缓存，WebSocket每5秒推送最新数据
     */
    function save(nodesData) {
      // 基于第一性原理：WebSocket每5秒推送实时数据，缓存反而导致数据不一致
      // 不再缓存节点数据，直接返回
      console.debug('[CacheManager] 节点数据缓存已禁用，使用WebSocket实时数据');
      return;
    }

    /**
     * Loads nodes data from localStorage.
     * @returns {Object|null} The cached nodes data, or null if cache is invalid, expired, or not found.
     * @deprecated 实时数据不再缓存，直接使用window.lastNodeData
     */
    function load() {
      // 基于第一性原理：实时数据应该来自WebSocket，不使用过期的缓存
      console.debug('[CacheManager] 节点数据缓存已禁用，请使用window.lastNodeData获取实时数据');
      return null;
    }

    /**
     * Clears the cache.
     */
    function clear() {
        try {
            // 清理节点数据缓存（如果存在）
            localStorage.removeItem(CACHE_CONFIG.KEY);
            console.debug('[CacheManager] 节点数据缓存已清理');
        } catch (error) {
            console.error('[CacheManager] Failed to clear cache:', error);
        }
    }

    return {
      // 老接口 (保持兼容)
      save,
      load,
      clear,

      // 新接口别名 (兼容新版本CacheManager.js)
      set: save,
      get: load,
      remove: clear,

      // 兼容新版本方法
      cleanupLegacyCache: () => {
        console.log('[CacheManager] 旧版本清理功能 - 清理当前缓存');
        clear();
      },

      // Expose config if needed externally, though generally not recommended
      // getConfig: () => ({ ...CACHE_CONFIG })
    };
  })();
}