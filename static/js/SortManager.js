/**
 * SortManager.js
 * 服务器节点排序管理模块
 * 提供多维度排序功能（CPU、内存、流量、名称、到期时间等）
 */

window.SortManager = (function() {
    'use strict';

    // 内部状态管理
    const state = {
        // 当前排序配置
        currentSort: {
            type: 'default',
            direction: 'desc'
        },

        // 实时排序开关
        realtimeSortEnabled: true,

        // 防抖计时器
        debounceTimers: {
            sort: null
        },

        // 回调函数
        callbacks: {
            onSortChange: null,
            onError: null
        }
    };

    // 配置常量
    const CONFIG = {
        // 防抖延迟
        DEBOUNCE_DELAY: 100,

        // 排序动画持续时间
        SORT_ANIMATION_DURATION: 300,

        // 排序方向配置
        SORT_DIRECTIONS: {
            default: 'desc',
            cpu: 'desc',
            memory: 'desc',
            totalTraffic: 'desc',
            upload: 'desc',
            download: 'desc',
            name: 'asc',
            expiration: 'asc'  // 到期时间默认升序（剩余时间少的优先）
        },

        // 网络速度单位换算
        SPEED_UNITS: {
            'bps': 1,
            'kbps': 1000,
            'mbps': 1000000,
            'gbps': 1000000000,
            'tbps': 1000000000000
        }
    };

    // 图标常量 - 统一管理排序相关图标
    const ICON = {
        asc: 'ti ti-chevron-up',
        desc: 'ti ti-chevron-down', 
        idle: 'ti ti-selector'  // 未选中状态
    };

    /**
     * 调试日志函数
     */
    function log(...args) {
        if (window.DEBUG_MODE) {
            console.log('[SortManager]', ...args);
        }
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks - 回调函数对象
     */
    function setCallbacks(callbacks) {
        Object.assign(state.callbacks, callbacks);
    }

    /**
     * 获取所有服务器卡片（去重）
     * @returns {Array} 服务器卡片元素数组
     */
    function getAllCards() {
        const allCards = Array.from(document.querySelectorAll('.server-card'));

        // 使用Set去重，避免重复处理相同的节点
        const uniqueCards = [];
        const seenSids = new Set();

        for (const card of allCards) {
            const sid = card.dataset.sid;
            if (sid && !seenSids.has(sid)) {
                seenSids.add(sid);
                uniqueCards.push(card);
            }
        }

        return uniqueCards;
    }

    /**
     * 获取可见的服务器卡片
     * @returns {Array} 可见的服务器卡片数组
     */
    function getVisibleCards() {
        return getAllCards().filter(card => window.getComputedStyle(card).display !== 'none');
    }

    /**
     * 应用排序
     * @param {string} type - 排序类型
     * @param {string} direction - 排序方向
     */
    function applySort(type, direction) {
        log('应用排序:', type, direction);

        // 更新当前排序状态
        state.currentSort = { type, direction };

        // 同步UI状态（确保撤销/重做后UI状态正确）
        renderSortUI();

        // 获取可见的卡片
        const cards = getVisibleCards();
        if (cards.length === 0) {
            log('没有可排序的卡片');
            return;
        }

        // 保存拖拽状态
        const dragStates = cards.map(card => ({
            element: card,
            draggable: card.getAttribute('draggable'),
            dragEvents: card.getAttribute('data-has-drag-events') === 'true'
        }));

        // 排序卡片
        cards.sort((a, b) => {
            // 优先按在线状态排序
            const isOnlineA = a.querySelector('[id$="_status_indicator"]')?.classList.contains('bg-green-500') || false;
            const isOnlineB = b.querySelector('[id$="_status_indicator"]')?.classList.contains('bg-green-500') || false;

            if (isOnlineA !== isOnlineB) {
                return isOnlineA ? -1 : 1;
            }

            // 获取排序值
            const valueA = getSortValue(a, type);
            const valueB = getSortValue(b, type);

            // 相同值时的处理
            if (valueA === valueB) {
                // 优先使用随机数排序（如果存在）
                if (a.dataset.sortRandom && b.dataset.sortRandom) {
                    return Number(a.dataset.sortRandom) - Number(b.dataset.sortRandom);
                }

                // 回退到top值排序
                const topA = Number(a.dataset.top || 0);
                const topB = Number(b.dataset.top || 0);
                return topB - topA;
            }

            // 根据值类型进行比较，支持字符串与数字
            let compareResult;
            if (typeof valueA === 'string' || typeof valueB === 'string') {
                // 使用 localeCompare 进行自然排序，忽略大小写并按数字排序
                compareResult = valueA.localeCompare(valueB, undefined, {
                    numeric: true,
                    sensitivity: 'base'
                });
            } else {
                // 数值直接相减即可
                compareResult = valueA - valueB;
            }

            return direction === 'asc' ? compareResult : -compareResult;
        });

        // 应用排序到DOM
        applySortedOrderToDOM(cards, dragStates);

        // 触发回调
        if (state.callbacks.onSortChange) {
            state.callbacks.onSortChange(type, direction, cards.length);
        }

        log('排序完成，处理卡片数:', cards.length);
    }

    /**
     * 获取排序值
     * @param {Element} card - 卡片元素
     * @param {string} type - 排序类型
     * @returns {number|string} 排序值
     */
    function getSortValue(card, type) {
        switch (type) {
            case 'default':
                return Number(card.dataset.top || 0);

            case 'cpu':
                return Number(card.querySelector('[id$="_CPU"]')?.dataset.cpu || 0);

            case 'memory':
                return Number(card.querySelector('[id$="_MEM"]')?.dataset.memory || 0);

            case 'download':
                // 优先使用原始数据
                if (card.dataset.downloadRaw) {
                    return Number(card.dataset.downloadRaw);
                }
                // 解析显示文本
                const downloadText = card.querySelector('[id$="_NET_IN"]')?.textContent || '0 bps';
                return parseSpeedValue(downloadText);

            case 'upload':
                // 优先使用原始数据
                if (card.dataset.uploadRaw) {
                    return Number(card.dataset.uploadRaw);
                }
                // 解析显示文本
                const uploadText = card.querySelector('[id$="_NET_OUT"]')?.textContent || '0 bps';
                return parseSpeedValue(uploadText);

            case 'expiration':
                const expireText = card.querySelector('[id$="_EXPIRE_TIME"]')?.textContent;
                if (expireText === '永久') return Number.MAX_SAFE_INTEGER;
                if (expireText === '已过期') return -1;
                if (expireText === '今日到期') return 0;
                const days = parseInt(expireText?.match(/\d+/)?.[0] || 0);
                return days;

            case 'name':
                const nameElement = card.querySelector('.server-name');
                return nameElement ? nameElement.textContent.trim().toLowerCase() : '';

            case 'total-traffic':
                return Number(card.dataset.totalTraffic || 0);

            case 'disk-usage':
                return Number(card.dataset.disk || 0);

            case 'uptime':
                if (card.dataset.uptime) {
                    return Number(card.dataset.uptime);
                }
                const uptimeElement = card.querySelector('[id$="_UPTIME"]');
                if (uptimeElement?.dataset.uptime) {
                    return Number(uptimeElement.dataset.uptime);
                }
                // 解析文本中的天数
                const uptimeText = uptimeElement?.textContent || '';
                const uptimeDays = uptimeText.match(/(\d+)\s*天/);
                return uptimeDays ? Number(uptimeDays[1]) : 0;

            default:
                return 0;
        }
    }

    /**
     * 解析网络速度值
     * @param {string} speedText - 速度文本（如 "100 Mbps"）
     * @returns {number} 速度值（bps）
     */
    function parseSpeedValue(speedText) {
        const match = speedText.match(/^([\d.]+)\s*(\w+)$/);
        if (!match) return 0;

        const [_, value, unit] = match;
        const numValue = parseFloat(value);
        const multiplier = CONFIG.SPEED_UNITS[unit.toLowerCase()] || 1;

        return numValue * multiplier;
    }

    /**
     * 将排序后的顺序应用到DOM
     * @param {Array} sortedCards - 排序后的卡片数组
     * @param {Array} dragStates - 拖拽状态数组
     */
    function applySortedOrderToDOM(sortedCards, dragStates) {
        // 查找容器 - 优先使用卡片的父容器，确保在嵌套结构中也能正确工作
        let container = null;

        // 如果有卡片，使用第一张卡片的父容器
        if (sortedCards.length > 0) {
            container = sortedCards[0].parentElement;
            log('使用卡片的父容器:', container.id || '无ID');
        }

        // 如果没有找到父容器，尝试使用常见的容器选择器
        if (!container) {
            // 优先使用更具体的ID选择器
            container = document.querySelector('#card-grid-container') ||
                       document.querySelector('[id^="card-grid-"]') ||
                       document.querySelector('.server-grid') ||
                       document.querySelector('#node-card-content .grid') ||
                       document.querySelector('.grid');

            if (container) {
                log('使用选择器找到卡片容器:', container.id || '无ID');
            }
        }

        if (!container) {
            log('未找到卡片容器，跳过DOM排序');
            return;
        }

        // 保存当前位置用于动画
        sortedCards.forEach(card => {
            card.classList.add('card-position-transition');
            const rect = card.getBoundingClientRect();
            card.dataset.oldTop = rect.top;
            card.dataset.oldLeft = rect.left;
        });

        // 使用文档片段提高性能
        const fragment = document.createDocumentFragment();
        sortedCards.forEach(card => fragment.appendChild(card));

        // 更新DOM
        container.innerHTML = '';
        container.appendChild(fragment);

        // 应用FLIP动画
        requestAnimationFrame(() => {
            sortedCards.forEach(card => {
                const oldTop = parseFloat(card.dataset.oldTop || 0);
                const oldLeft = parseFloat(card.dataset.oldLeft || 0);
                const newRect = card.getBoundingClientRect();

                const deltaY = oldTop - newRect.top;
                const deltaX = oldLeft - newRect.left;

                if (Math.abs(deltaY) > 1 || Math.abs(deltaX) > 1) {
                    card.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                    card.style.transition = 'none';

                    // 强制重排并应用动画
                    void card.offsetWidth;
                    card.style.transition = `transform ${CONFIG.SORT_ANIMATION_DURATION}ms ease-out`;
                    card.style.transform = '';
                }

                // 清理临时数据
                delete card.dataset.oldTop;
                delete card.dataset.oldLeft;
            });
        });

        // 恢复拖拽状态
        dragStates.forEach(({ element, draggable, dragEvents }) => {
            if (draggable) element.setAttribute('draggable', draggable);
            if (dragEvents) element.setAttribute('data-has-drag-events', 'true');
        });

        // 清理过渡类
        setTimeout(() => {
            sortedCards.forEach(card => {
                card.classList.remove('card-position-transition');
                card.style.transition = '';
            });
        }, CONFIG.SORT_ANIMATION_DURATION);
    }

    /**
     * 应用当前排序（带实时排序检查）
     */
    function applyCurrentSort() {
        if (!state.realtimeSortEnabled) {
            log('实时排序已禁用，跳过排序');
            return;
        }

        // 防抖处理
        if (state.debounceTimers.sort) {
            clearTimeout(state.debounceTimers.sort);
        }

        state.debounceTimers.sort = setTimeout(() => {
            applySort(state.currentSort.type, state.currentSort.direction);
        }, CONFIG.DEBOUNCE_DELAY);
    }

    /**
     * 设置实时排序状态
     * @param {boolean} enabled - 是否启用实时排序
     */
    function setRealtimeSort(enabled) {
        state.realtimeSortEnabled = !!enabled;
        log('实时排序状态:', state.realtimeSortEnabled);

        if (enabled) {
            applyCurrentSort();
        }
    }

    /**
     * 统一渲染排序UI状态 - 确保任意时刻只有一个排序项高亮，图标状态统一
     */
    function renderSortUI() {
        log('渲染排序UI状态:', state.currentSort);
        
        // 获取所有排序选项（支持多种选择器）
        const sortElements = document.querySelectorAll('.sort-option, .sort-btn');
        
        sortElements.forEach(element => {
            const sortType = element.dataset.sort;
            const isActive = sortType === state.currentSort.type;
            
            // 更新激活状态
            if (isActive) {
                element.classList.add('active');
                // 更新方向数据属性
                element.dataset.direction = state.currentSort.direction;
            } else {
                element.classList.remove('active');
            }
            
            // 更新图标
            const iconElement = element.querySelector('.sort-direction-icon, i');
            if (iconElement) {
                if (isActive) {
                    // 激活状态：显示方向图标
                    iconElement.className = ICON[state.currentSort.direction] || ICON.desc;
                } else {
                    // 非激活状态：显示默认图标
                    iconElement.className = ICON.idle + ' text-slate-500 dark:text-slate-400';
                }
            }
        });
        
        log('排序UI渲染完成');
    }

    /**
     * 统一处理排序点击事件
     * @param {Event} event - 点击事件
     */
    function handleSortClick(event) {
        const element = event.currentTarget;
        const sortType = element.dataset.sort;
        
        if (!sortType) {
            log('排序类型未定义，忽略点击');
            return;
        }
        
        // 计算新方向
        let newDirection;
        if (sortType === state.currentSort.type) {
            // 同一排序类型：切换方向
            newDirection = state.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // 不同排序类型：使用默认方向
            newDirection = CONFIG.SORT_DIRECTIONS[sortType] || 'desc';
        }
        
        // 更新内部状态
        state.currentSort = {
            type: sortType,
            direction: newDirection
        };
        
        log('排序状态更新:', state.currentSort);
        
        // 统一渲染UI
        renderSortUI();
        
        // 应用排序
        applySort(sortType, newDirection);
    }

    /**
     * 初始化排序控件
     */
    function initSortControls() {
        // 检查是否有TabMenuSystem处理排序
        if (window.TabMenuSystem) {
            log('TabMenuSystem已接管排序控件');
            return;
        }

        // 统一处理所有排序元素，移除直接DOM操作
        const sortElements = document.querySelectorAll('.sort-option, .sort-btn');
        sortElements.forEach(element => {
            // 移除可能存在的旧事件监听器
            element.removeEventListener('click', handleSortClick);
            // 添加统一的事件处理器
            element.addEventListener('click', handleSortClick);
        });

        // 实时排序复选框
        const realtimeSortCheckbox = document.getElementById('realtime-sort');
        if (realtimeSortCheckbox) {
            realtimeSortCheckbox.addEventListener('change', () => {
                setRealtimeSort(realtimeSortCheckbox.checked);
            });
            // 同步状态
            setRealtimeSort(realtimeSortCheckbox.checked);
        }

        // 初始化UI状态
        renderSortUI();
        
        log('排序控件初始化完成，共注册', sortElements.length, '个排序元素');
    }

    /**
     * 获取当前排序状态
     * @returns {Object} 当前排序配置
     */
    function getCurrentSort() {
        return { ...state.currentSort };
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    function getState() {
        return {
            currentSort: { ...state.currentSort },
            realtimeSortEnabled: state.realtimeSortEnabled
        };
    }

    /**
     * 销毁管理器
     */
    function destroy() {
        // 清理定时器
        if (state.debounceTimers.sort) {
            clearTimeout(state.debounceTimers.sort);
        }

        // 重置状态
        state.debounceTimers = {};

        log('SortManager 已销毁');
    }

    /**
     * 初始化SortManager
     * @param {Object} options - 配置选项
     */
    function init(options = {}) {
        // 应用配置
        if (options.config) {
            Object.assign(CONFIG, options.config);
        }

        // 设置回调
        if (options.callbacks) {
            setCallbacks(options.callbacks);
        }

        // 初始化状态
        if (options.initialSort) {
            state.currentSort = { ...options.initialSort };
        }

        if (options.realtimeSort !== undefined) {
            state.realtimeSortEnabled = !!options.realtimeSort;
        }

        // 初始化排序控件
        initSortControls();

        log('SortManager 初始化完成');
    }

    // 导出公共API
    return {
        // 初始化和配置
        init,
        getState,
        destroy,
        setCallbacks,
        setRealtimeSort,

        // 排序功能
        applySort,
        applyCurrentSort,
        getCurrentSort,

        // 工具函数
        getAllCards,
        getVisibleCards
    };
})();