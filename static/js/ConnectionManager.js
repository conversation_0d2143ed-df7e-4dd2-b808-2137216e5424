/**
 * ConnectionManager.js
 * 统一管理WebSocket连接和数据同步的模块
 * 支持传统WebSocket和SharedWorker两种连接模式
 */

// 使用立即执行函数表达式创建模块
window.ConnectionManager = (function() {
    // 配置参数
    const config = {
        reconnectInterval: 3000,      // 重连间隔（毫秒）
        maxReconnectAttempts: 10,     // 最大重连尝试次数
        healthCheckInterval: 30000,   // 健康检查间隔（毫秒）
        debug: false                  // 调试模式
    };

    // 内部状态
    const state = {
        connectionMode: null,          // 'websocket' 或 'sharedworker'
        isConnected: false,           // 连接状态
        reconnectAttempts: 0,         // 重连尝试次数
        reconnectTimer: null,         // 重连定时器
        healthCheckTimer: null,       // 健康检查定时器
        ws: null,                     // 普通WebSocket实例
        sharedClient: null,           // SharedWorker客户端实例
        lastDataTimestamp: 0,         // 最后数据接收时间戳
        nodeId: null,                 // 节点ID，用于单节点连接
        lastActiveTime: Date.now(),   // 最后活跃时间
        callbacks: {                  // 回调函数集合
            onMessage: null,
            onConnected: null,
            onDisconnected: null,
            onError: null
        }
    };

    /**
     * 调试日志函数
     * @param {...any} args - 日志参数
     */
    function log(...args) {
        if (config.debug || window.DEBUG_MODE) {
            console.log('[ConnectionManager]', ...args);
        }
    }

    /**
     * 初始化连接
     * @param {Object} options - 连接配置选项
     */
    function init(options = {}) {
        // 合并选项到配置
        Object.assign(config, options.config || {});

        // 保存回调函数
        if (options.callbacks) {
            Object.assign(state.callbacks, options.callbacks);
        }

        // 保存节点ID
        if (options.nodeId) {
            state.nodeId = options.nodeId;
        }

        log('初始化连接管理器', { config, nodeId: state.nodeId });

        // 尝试使用SharedWorker连接
        if (typeof SharedWorker !== 'undefined' && typeof StatsSharedClient !== 'undefined') {
            log('检测到SharedWorker支持，尝试使用SharedWorker连接');
            initSharedWorkerConnection();
        } else {
            log('不支持SharedWorker，使用传统WebSocket连接');
            initWebSocketConnection();
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // 启动健康检查
        startHealthCheck();

        return {
            isConnected: () => state.isConnected,
            getConnectionMode: () => state.connectionMode,
            reconnect: reconnect,
            requestLatestData: requestLatestData,
            close: close
        };
    }

    /**
     * 初始化SharedWorker连接
     */
    function initSharedWorkerConnection() {
        try {
            // 清理可能存在的WebSocket连接
            if (state.ws) {
                state.ws.close();
                state.ws = null;
            }

            // 设置连接模式
            state.connectionMode = 'sharedworker';
            log('正在初始化SharedWorker连接');

            // 创建SharedWorker客户端
            state.sharedClient = StatsSharedClient.getInstance({
                nodeId: state.nodeId,
                debug: config.debug,
                onMessage: handleWsMessage,
                onConnected: () => {
                    state.isConnected = true;
                    state.reconnectAttempts = 0;
                    state.lastActiveTime = Date.now();
                    log('SharedWorker连接已建立');

                    // 清除重连定时器
                    if (state.reconnectTimer) {
                        clearTimeout(state.reconnectTimer);
                        state.reconnectTimer = null;
                    }

                    // 触发连接成功回调
                    if (state.callbacks.onConnected) {
                        state.callbacks.onConnected();
                    }

                    // 连接状态不再缓存到localStorage，由页面状态管理
                    // localStorage.setItem('stats_connection_active', 'true');
                    // localStorage.setItem('stats_connection_timestamp', Date.now().toString());
                },
                onDisconnected: () => {
                    state.isConnected = false;
                    log('SharedWorker连接已断开');

                    // 连接状态不再使用localStorage存储
                    // localStorage.removeItem('stats_connection_active');
                    // localStorage.removeItem('stats_connection_timestamp');

                    // 触发断开连接回调
                    if (state.callbacks.onDisconnected) {
                        state.callbacks.onDisconnected();
                    }

                    // 设置重连
                    scheduleReconnect();
                }
            });

            // 请求最新数据
            setTimeout(() => {
                requestLatestData();
            }, 500); // 短暂延迟，确保连接已建立

        } catch (error) {
            log('SharedWorker连接失败，回退到传统WebSocket', error);
            state.sharedClient = null;
            initWebSocketConnection();
        }
    }

    /**
     * 初始化传统WebSocket连接
     */
    function initWebSocketConnection() {
        // 清理可能存在的WebSocket
        closeWebSocket();

        // 设置连接模式和状态
        state.connectionMode = 'websocket';
        state.isConnected = false;

        try {
            const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = state.nodeId
                ? `${protocol}//${location.host}/ws/stats/${state.nodeId}`
                : `${protocol}//${location.host}/ws/stats`;

            log(`正在连接WebSocket: ${wsUrl}`);
            state.ws = new WebSocket(wsUrl);

            // 连接打开事件
            state.ws.onopen = () => {
                state.isConnected = true;
                state.reconnectAttempts = 0;
                state.lastActiveTime = Date.now();
                log('WebSocket连接已建立');

                // 清除重连定时器
                if (state.reconnectTimer) {
                    clearTimeout(state.reconnectTimer);
                    state.reconnectTimer = null;
                }

                // 触发连接成功回调
                if (state.callbacks.onConnected) {
                    state.callbacks.onConnected();
                }
            };

            // 接收消息事件
            state.ws.onmessage = (event) => {
                handleWsMessage(JSON.parse(event.data), false);
            };

            // 连接关闭事件
            state.ws.onclose = () => {
                state.isConnected = false;
                log('WebSocket连接已关闭');

                // 触发断开连接回调
                if (state.callbacks.onDisconnected) {
                    state.callbacks.onDisconnected();
                }

                // 清理WebSocket实例
                state.ws = null;

                // 设置重连
                scheduleReconnect();
            };

            // 连接错误事件
            state.ws.onerror = (error) => {
                log('WebSocket连接错误', error);
                state.isConnected = false;

                // 触发错误回调
                if (state.callbacks.onError) {
                    state.callbacks.onError(error);
                }
            };
        } catch (error) {
            log('WebSocket初始化失败', error);
            state.isConnected = false;

            // 触发错误回调
            if (state.callbacks.onError) {
                state.callbacks.onError(error);
            }

            // 设置重连
            scheduleReconnect();
        }
    }

    /**
     * 处理WebSocket消息
     * @param {Object} message - 接收到的消息
     * @param {boolean} fromCache - 是否来自缓存
     */
    function handleWsMessage(message, fromCache = false) {
        // 更新最后数据接收时间
        state.lastDataTimestamp = Date.now();
        state.lastActiveTime = Date.now();

        // 检查消息格式
        if (!message || typeof message !== 'object') {
            log('接收到无效消息格式', message);
            return;
        }

        // 检查是否处于标签页恢复状态
        const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
        const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

        // 如果正在从休眠中恢复且应该阻止数据更新，仅缓存数据而不更新UI
        if (isRecoveringFromSleep && shouldBlockDataUpdates) {
            // 仅对stats类型消息进行缓存
            if (message.type === 'stats' && message.data) {
                // 保存数据到缓存
                if (window.CacheManager && typeof window.CacheManager.save === 'function') {
                    window.CacheManager.save(message.data);
                }

                // 如果有网络数据，缓存到TabLifecycleHandler
                if (window.TabLifecycleHandler) {
                    window.TabLifecycleHandler.cachedLatestData = message.data;
                }
            }
            return; // 不继续处理
        }

        // 处理消息
        if (state.callbacks.onMessage) {
            state.callbacks.onMessage(message, fromCache);
        }
    }

    /**
     * 处理页面可见性变化
     */
    function handleVisibilityChange() {
        const isVisible = !document.hidden;
        
        if (isVisible) {
            log('页面变为可见，尝试请求最新数据');
            state.lastActiveTime = Date.now();
            
            // 检查连接状态
            if (!state.isConnected) {
                log('页面可见但连接未建立，尝试重新连接');
                reconnect();
            } else {
                // 请求最新数据
                requestLatestData();
            }
        } else {
            log('页面变为不可见');
        }
    }

    /**
     * 请求最新数据
     */
    function requestLatestData() {
        // 检查是否处于标签页恢复状态
        const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
        const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

        // 如果正在从休眠中恢复且应该阻止数据更新，则不请求数据
        if (isRecoveringFromSleep && shouldBlockDataUpdates) {
            log('标签页正在从休眠中恢复，暂不请求数据');
            return;
        }

        log('请求最新数据');

        // 根据连接模式请求数据
        if (state.connectionMode === 'sharedworker' && state.sharedClient) {
            state.sharedClient.requestLastData();
        } else if (state.connectionMode === 'websocket' && state.ws && state.ws.readyState === WebSocket.OPEN) {
            // WebSocket模式下，服务器会自动推送数据，不需要显式请求
            // 但我们可以发送一个请求更新的消息
            try {
                state.ws.send(JSON.stringify({ type: 'request_update', timestamp: Date.now() }));
            } catch (error) {
                log('发送更新请求失败', error);
            }
        } else {
            log('无法请求数据：连接未建立');
            reconnect(); // 尝试重新连接
        }
    }

    /**
     * 安排重新连接
     */
    function scheduleReconnect() {
        // 增加重连尝试次数
        state.reconnectAttempts++;

        // 计算重连延迟（使用指数退避策略）
        const delay = Math.min(
            config.reconnectInterval * Math.pow(1.5, state.reconnectAttempts - 1),
            60000 // 最大不超过60秒
        );

        log(`安排重新连接，尝试次数: ${state.reconnectAttempts}，延迟: ${delay}ms`);

        // 清除已有的重连定时器
        if (state.reconnectTimer) {
            clearTimeout(state.reconnectTimer);
        }

        // 设置重连定时器
        state.reconnectTimer = setTimeout(() => {
            state.reconnectTimer = null;
            reconnect();
        }, delay);
    }

    /**
     * 重新连接
     */
    function reconnect() {
        log('执行重新连接');

        // 如果已经达到最大重连尝试次数，先重置计数
        if (state.reconnectAttempts >= config.maxReconnectAttempts) {
            log(`已达到最大重连尝试次数(${config.maxReconnectAttempts})，重置计数`);
            state.reconnectAttempts = 0;
        }

        // 根据当前连接模式重新连接
        if (state.connectionMode === 'sharedworker') {
            initSharedWorkerConnection();
        } else {
            initWebSocketConnection();
        }
    }

    /**
     * 启动健康检查
     */
    function startHealthCheck() {
        // 清除已有的健康检查定时器
        if (state.healthCheckTimer) {
            clearInterval(state.healthCheckTimer);
        }

        // 设置健康检查定时器
        state.healthCheckTimer = setInterval(() => {
            const now = Date.now();
            const timeSinceLastActive = now - state.lastActiveTime;

            // 如果超过健康检查间隔的2倍没有活动，且当前显示连接已建立，则视为连接异常
            if (timeSinceLastActive > config.healthCheckInterval * 2 && state.isConnected) {
                log(`健康检查：检测到可能的连接问题，${timeSinceLastActive}ms 无活动`);
                
                // 检查实际连接状态
                if (state.connectionMode === 'sharedworker' && state.sharedClient) {
                    state.sharedClient.checkConnection();
                } else if (state.connectionMode === 'websocket' && state.ws) {
                    // 如果WebSocket已关闭但状态未更新，强制更新状态
                    if (state.ws.readyState !== WebSocket.OPEN) {
                        log('健康检查：WebSocket已关闭但状态未更新，强制更新状态');
                        state.isConnected = false;
                        reconnect();
                    }
                }
            }
        }, config.healthCheckInterval);

        log(`健康检查已启动，间隔: ${config.healthCheckInterval}ms`);
    }

    /**
     * 关闭WebSocket连接
     */
    function closeWebSocket() {
        if (state.ws) {
            try {
                state.ws.close();
            } catch (e) {
                // 忽略错误
            }
            state.ws = null;
        }
    }

    /**
     * 关闭连接管理器
     */
    function close() {
        log('关闭连接管理器');

        // 停止健康检查
        if (state.healthCheckTimer) {
            clearInterval(state.healthCheckTimer);
            state.healthCheckTimer = null;
        }

        // 清除重连定时器
        if (state.reconnectTimer) {
            clearTimeout(state.reconnectTimer);
            state.reconnectTimer = null;
        }

        // 关闭WebSocket连接
        closeWebSocket();

        // 关闭SharedWorker客户端
        if (state.sharedClient) {
            if (typeof state.sharedClient.destroy === 'function') {
                state.sharedClient.destroy();
            }
            state.sharedClient = null;
        }

        // 移除事件监听器
        document.removeEventListener('visibilitychange', handleVisibilityChange);

        // 重置状态
        state.isConnected = false;
        state.connectionMode = null;
    }

    // 导出公共API
    return {
        init: init,
        getConfig: () => ({ ...config }),
        isSharedWorkerSupported: () => typeof SharedWorker !== 'undefined' && typeof StatsSharedClient !== 'undefined',
        setDebugMode: (mode) => { config.debug = !!mode; }
    };
})();