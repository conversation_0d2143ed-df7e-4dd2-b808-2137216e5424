/**
 * 地区分布性能优化器
 * 解决DOM操作瓶颈和缓存问题
 * 向后兼容，渐进式增强现有功能
 */

class RegionPerformanceOptimizer {
    constructor() {
        // DOM元素缓存 - 避免重复查询
        this.domCache = {
            cardContainer: null,
            allCards: null,
            regionStats: null,
            lastCacheTime: 0,
            cacheTTL: 10000 // 10秒缓存有效期（从5秒增加到10秒）
        };

        // 地区数据缓存
        this.regionDataCache = {
            data: null,
            hash: '',
            timestamp: 0,
            ttl: 30000 // 30秒缓存
        };

        // 性能监控
        this.performanceStats = {
            domCacheHits: 0,
            dataCacheHits: 0,
            totalQueries: 0
        };

        // 防抖控制
        this.debounceTimeout = null;
        this.isProcessing = false;
        
        // 缓存失效防抖
        this.invalidateDebounce = null;
        
        // 记录上次节点数量用于智能缓存失效
        this.lastNodeCount = 0;

        // 初始化
        this.init();
    }

    init() {
        console.debug('[性能优化器] 初始化完成');
        
        // 监听数据更新事件，实现智能缓存失效
        document.addEventListener('statsSyncComplete', (event) => {
            // 只有当节点数量变化时才使缓存失效
            if (event.detail && event.detail.nodeCount !== this.lastNodeCount) {
                this.lastNodeCount = event.detail.nodeCount;
                this.invalidateCache();
            }
        });

        // 定期清理缓存
        setInterval(() => this.cleanupCache(), 60000);
    }

    /**
     * 获取缓存的DOM元素
     * @returns {Object} DOM元素缓存
     */
    getCachedDOMElements() {
        const now = Date.now();
        
        // 检查缓存是否有效
        if (this.domCache.cardContainer && 
            (now - this.domCache.lastCacheTime) < this.domCache.cacheTTL) {
            this.performanceStats.domCacheHits++;
            console.debug('[性能优化器] DOM缓存命中');
            return this.domCache;
        }

        // 重新缓存DOM元素
        this.performanceStats.totalQueries++;
        
        const cardContainer = document.getElementById('card-grid-container') ||
                            document.querySelector('[id^="card-grid-"]') ||
                            document.querySelector('.grid');

        const allCards = cardContainer ? 
            Array.from(cardContainer.querySelectorAll('.server-card')) : [];

        const regionStats = document.getElementById('region-stats');

        // 更新缓存
        this.domCache = {
            cardContainer,
            allCards,
            regionStats,
            lastCacheTime: now,
            cacheTTL: this.domCache.cacheTTL
        };

        console.debug(`[性能优化器] DOM缓存更新: ${allCards.length}张卡片`);
        return this.domCache;
    }

    /**
     * 高性能地区筛选
     * @param {string} regionCode - 地区代码
     */
    optimizedFilterByRegion(regionCode) {
        // 防抖处理
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }

        this.debounceTimeout = setTimeout(() => {
            this._executeFilter(regionCode);
        }, 50);
    }

    /**
     * 执行筛选操作
     * @private
     */
    _executeFilter(regionCode) {
        if (this.isProcessing) {
            console.debug('[性能优化器] 正在处理中，跳过重复请求');
            return;
        }

        this.isProcessing = true;
        const startTime = performance.now();

        try {
            const { cardContainer, allCards } = this.getCachedDOMElements();
            
            if (!cardContainer || allCards.length === 0) {
                console.warn('[性能优化器] 未找到有效的卡片容器或卡片');
                return;
            }

            // 使用DocumentFragment批量操作DOM
            this._batchUpdateCardVisibility(allCards, regionCode);

            // 更新容器状态
            this._updateContainerState(cardContainer);

            const endTime = performance.now();
            console.debug(`[性能优化器] 筛选完成: ${regionCode}, 耗时: ${(endTime - startTime).toFixed(2)}ms`);

        } catch (error) {
            console.error('[性能优化器] 筛选失败:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 批量更新卡片可见性
     * @private
     */
    _batchUpdateCardVisibility(allCards, regionCode) {
        // 使用新的统一筛选器
        if (window.UF) {
            window.UF.filterByRegion(regionCode || 'ALL');
            
            // 统计结果
            let visibleCount = 0;
            let hiddenCount = 0;
            allCards.forEach(card => {
                if (card.style.display === 'none') {
                    hiddenCount++;
                } else {
                    visibleCount++;
                }
            });
            
            console.debug(`[性能优化器] 卡片更新: ${visibleCount}显示, ${hiddenCount}隐藏`);
            return { visibleCount, hiddenCount };
        }
        
        // 后备方案
        const fragment = document.createDocumentFragment();
        const visibleCards = [];
        const hiddenCards = [];

        // 分类卡片
        allCards.forEach(card => {
            const cardRegion = card.dataset.region;
            const shouldShow = !regionCode || cardRegion === regionCode;

            if (shouldShow) {
                visibleCards.push(card);
            } else {
                hiddenCards.push(card);
            }
        });

        // 批量隐藏
        if (hiddenCards.length > 0) {
            hiddenCards.forEach(card => {
                card.style.display = 'none';
            });
        }

        // 批量显示
        if (visibleCards.length > 0) {
            visibleCards.forEach(card => {
                card.style.display = '';
            });
        }

        console.debug(`[性能优化器] 卡片更新: ${visibleCards.length}显示, ${hiddenCards.length}隐藏`);
        
        return { visibleCount: visibleCards.length, hiddenCount: hiddenCards.length };
    }

    /**
     * 更新容器状态
     * @private
     */
    _updateContainerState(container) {
        // 确保容器可见性
        container.style.display = 'grid';
        container.style.minHeight = '100px';

        // 强制重新计算布局
        requestAnimationFrame(() => {
            container.classList.add('layout-updated');
            void container.offsetHeight; // 触发重排
            container.classList.remove('layout-updated');
        });
    }

    /**
     * 缓存地区统计数据
     * @param {Object} regionData - 地区数据
     * @returns {boolean} 是否使用缓存
     */
    cacheRegionData(regionData) {
        const dataHash = this._generateDataHash(regionData);
        const now = Date.now();

        // 检查缓存是否有效
        if (this.regionDataCache.hash === dataHash && 
            (now - this.regionDataCache.timestamp) < this.regionDataCache.ttl) {
            this.performanceStats.dataCacheHits++;
            return true; // 使用缓存
        }

        // 更新缓存
        this.regionDataCache = {
            data: regionData,
            hash: dataHash,
            timestamp: now,
            ttl: this.regionDataCache.ttl
        };

        return false; // 需要更新
    }

    /**
     * 生成数据哈希
     * @private
     */
    _generateDataHash(data) {
        if (!data || typeof data !== 'object') return '';
        
        return JSON.stringify(data).length.toString(36) + 
               Object.keys(data).length.toString(36);
    }

    /**
     * 清理过期缓存
     */
    cleanupCache() {
        const now = Date.now();

        // 清理DOM缓存
        if ((now - this.domCache.lastCacheTime) > this.domCache.cacheTTL * 2) {
            this.invalidateCache();
        }

        // 清理数据缓存
        if ((now - this.regionDataCache.timestamp) > this.regionDataCache.ttl * 2) {
            this.regionDataCache = {
                data: null,
                hash: '',
                timestamp: 0,
                ttl: this.regionDataCache.ttl
            };
        }

        console.debug('[性能优化器] 缓存清理完成');
    }

    /**
     * 使缓存无效（带防抖）
     */
    invalidateCache() {
        // 清除之前的防抖定时器
        if (this.invalidateDebounce) {
            clearTimeout(this.invalidateDebounce);
        }
        
        // 设置防抖定时器（3秒内多次调用只执行最后一次）
        this.invalidateDebounce = setTimeout(() => {
            this.domCache.lastCacheTime = 0;
            this.regionDataCache.timestamp = 0;
            // 使用 debug 级别减少日志输出
            console.debug('[性能优化器] 缓存已失效');
        }, 3000);
    }

    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        const cacheHitRate = this.performanceStats.totalQueries > 0 ? 
            (this.performanceStats.domCacheHits / this.performanceStats.totalQueries * 100).toFixed(1) : 0;

        return {
            ...this.performanceStats,
            cacheHitRate: `${cacheHitRate}%`,
            memoryUsage: this._estimateMemoryUsage()
        };
    }

    /**
     * 估算内存使用
     * @private
     */
    _estimateMemoryUsage() {
        const cacheSize = JSON.stringify(this.domCache).length + 
                         JSON.stringify(this.regionDataCache).length;
        return `${(cacheSize / 1024).toFixed(1)}KB`;
    }

    /**
     * 重置优化器
     */
    reset() {
        this.domCache = {
            cardContainer: null,
            allCards: null,
            regionStats: null,
            lastCacheTime: 0,
            cacheTTL: 10000
        };

        this.regionDataCache = {
            data: null,
            hash: '',
            timestamp: 0,
            ttl: 30000
        };

        this.performanceStats = {
            domCacheHits: 0,
            dataCacheHits: 0,
            totalQueries: 0
        };

        console.debug('[性能优化器] 已重置');
    }
}

// 创建全局实例
window.RegionPerformanceOptimizer = new RegionPerformanceOptimizer();

// 暴露调试接口
window.debugRegionPerformance = function() {
    const stats = window.RegionPerformanceOptimizer.getPerformanceStats();
    console.table(stats);
    return stats;
};

console.debug('[性能优化器] 模块加载完成');