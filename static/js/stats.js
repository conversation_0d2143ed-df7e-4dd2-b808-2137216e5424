/**
 * @file stats-simplified.js
 * @description 简化版的服务器状态监控前端脚本
 * 使用模块化设计，大幅减少代码重复
 */

// ==================== 全局辅助函数 ====================

// 网络速度格式化函数 - 统一版本
if (typeof window.strbps === 'undefined') {
    window.strbps = function(bps) {
        if (isNaN(bps) || bps === 0) return '0 <span class="metric-unit">bps</span>';
        const k = 1024;
        const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps'];
        const i = Math.floor(Math.log(bps) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const value = (bps / Math.pow(k, unitIndex)).toFixed(2);
        return value + ' <span class="metric-unit">' + sizes[unitIndex] + '</span>';
    };
}

// 字节大小格式化函数
if (typeof window.strB === 'undefined') {
    window.strB = function(bytes) {
        if (isNaN(bytes) || bytes === 0) return '0 <span class="metric-unit">B</span>';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return (bytes / Math.pow(k, i)).toFixed(2) + ' <span class="metric-unit">' + sizes[i] + '</span>';
    };
}

// ==================== 流量和硬盘环形图管理器 ====================

/**
 * 流量和硬盘显示管理器
 * 负责更新环形图和相关状态显示
 */
window.TrafficDisplayManager = (function() {
    
    // 颜色配置
    const CHART_COLORS = {
        disk: {
            normal: '#f59e0b',    // 琥珀色
            warning: '#f59e0b',   // 橙色 (>80%)
            critical: '#ef4444'   // 红色 (>90%)
        },
        traffic: {
            normal: '#06b6d4',    // 青色
            warning: '#f59e0b',   // 橙色 (>80%)
            critical: '#ef4444'   // 红色 (>90%)
        },
        background: 'rgb(var(--muted-rgb, 226 232 240))' // 背景环颜色
    };
    
    /**
     * 根据使用率获取颜色
     */
    function getUsageColor(percentage, type = 'disk') {
        if (percentage >= 90) return CHART_COLORS[type].critical;
        if (percentage >= 80) return CHART_COLORS[type].warning;
        return CHART_COLORS[type].normal;
    }
    
    /**
     * 更新条形图
     */
    function updateBarChart(barElement, percentage, type = 'disk') {
        if (!barElement) return;
        
        // 设置进度条宽度
        barElement.style.width = `${percentage}%`;
        
        // 更新颜色
        const color = getUsageColor(percentage, type);
        if (type === 'disk') {
            barElement.className = `h-full rounded-full transition-all duration-500 ${color === CHART_COLORS.disk.critical ? 'bg-red-500' : color === CHART_COLORS.disk.warning ? 'bg-orange-500' : 'bg-amber-500'}`;
        } else if (type === 'traffic') {
            barElement.className = `h-full rounded-full transition-all duration-500 ${color === CHART_COLORS.traffic.critical ? 'bg-red-500' : color === CHART_COLORS.traffic.warning ? 'bg-orange-500' : 'bg-cyan-500'}`;
        }
    }
    
    /**
     * 更新硬盘显示
     */
    function updateDiskUsage(sid, diskData) {
        const barElement = document.getElementById(`${sid}_DISK_BAR`);
        const usageElement = document.getElementById(`${sid}_DISK_USAGE`);
        
        if (diskData && diskData.total > 0) {
            const usagePercentage = Math.round((diskData.used / diskData.total) * 100);
            const usedGB = Math.round(diskData.used / 1024 / 1024 / 1024);
            const totalGB = Math.round(diskData.total / 1024 / 1024 / 1024);
            
            if (barElement && usageElement) {
                updateBarChart(barElement, usagePercentage, 'disk');
                usageElement.innerHTML = `${usagePercentage}<span class="metric-unit">%</span>`;
                
                // 更新数据属性
                usageElement.setAttribute('data-disk', usagePercentage);
                usageElement.setAttribute('data-total-disk', diskData.total);
                
                // 更新tooltip
                const container = barElement.closest('[title]');
                if (container) {
                    container.title = `硬盘使用: ${usagePercentage}% (${usedGB}G/${totalGB}G)`;
                }
            }
        }
    }
    
    /**
     * 更新流量显示
     */
    function updateTrafficUsage(sid, trafficStats) {
        const barElement = document.getElementById(`${sid}_TRAFFIC_BAR`);
        const percentageElement = document.getElementById(`${sid}_TRAFFIC_PERCENTAGE`);
        const unlimitedElement = document.getElementById(`${sid}_TRAFFIC_UNLIMITED`);
        const monthlyElement = document.getElementById(`${sid}_TRAFFIC_MONTHLY`);
        const containerElement = document.getElementById(`${sid}_TRAFFIC_CONTAINER`);
        
        if (!trafficStats) return;
        
        if (trafficStats.unlimited || trafficStats.limit === 0 || trafficStats.remaining === -1) {
            // 无限制流量
            if (containerElement) containerElement.classList.add('hidden');
            if (unlimitedElement) unlimitedElement.classList.remove('hidden');
            
            // 显示当月使用流量（不带小数点）
            if (monthlyElement && trafficStats.monthlyUsed !== undefined) {
                const monthlyUsed = Math.floor(trafficStats.monthlyUsed / (1024 * 1024 * 1024)); // 转换为GB，去掉小数
                monthlyElement.textContent = `${monthlyUsed}GB`;
            } else if (monthlyElement) {
                monthlyElement.textContent = '无限';
            }
        } else {
            // 有限制流量
            if (containerElement) containerElement.classList.remove('hidden');
            if (unlimitedElement) unlimitedElement.classList.add('hidden');
            
            if (barElement && percentageElement) {
                const ratio = Math.round(trafficStats.ratio || 0);
                updateBarChart(barElement, ratio, 'traffic');
                percentageElement.textContent = `${ratio}%`;
                
                // 更新tooltip
                const container = barElement.closest('[title]');
                if (container) {
                    const remaining = formatBytes(trafficStats.remaining || 0);
                    container.title = `流量使用: ${ratio}% (剩余: ${remaining})`;
                }
            }
        }
    }
    
    /**
     * 字节格式化函数
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    /**
     * 更新节点的所有图表
     */
    function updateNodeCharts(sid, nodeData) {
        // 更新硬盘使用率
        if (nodeData.stat && nodeData.stat.disk) {
            updateDiskUsage(sid, nodeData.stat.disk);
        }
        
        // 更新流量统计
        if (nodeData.traffic_stats) {
            updateTrafficUsage(sid, nodeData.traffic_stats);
        }
    }
    
    // 公开API
    return {
        updateNodeCharts,
        updateDiskUsage,
        updateTrafficUsage,
        updateBarChart,
        getUsageColor,
        formatBytes
    };
})();

// ==================== 兼容性代理函数 ====================
// 为向后兼容保留的函数代理，实际功能委托给相应模块

// 数据处理函数代理（委托给DataProcessor）
function calculateTotals(data) {
    return window.DataProcessor ? window.DataProcessor.calculateTotals(data) : null;
}

function formatRemainingDays(expireTimestamp) {
    return window.DataProcessor ?
           window.DataProcessor.formatRemainingDays(expireTimestamp) :
           (expireTimestamp ? `${Math.ceil((expireTimestamp - Date.now()/1000)/(24*60*60))} 天` : '永久');
}

// 筛选函数代理到统一筛选器
function filterByGroup(groupId) {
    if (!window.UF) {
        console.error('UnifiedFilter 未初始化');
        return;
    }
    window.UF.filterByGroup(groupId);
}

function filterByStatus(status) {
    if (!window.UF) {
        console.error('UnifiedFilter 未初始化');
        return;
    }
    window.UF.filterByStatus(status);
}

function filterByExpiry(days) {
    if (!window.UF) {
        console.error('UnifiedFilter 未初始化');
        return;
    }
    window.UF.filterByExpiry(days);
}

function filterByRegion(regionCode) {
    if (!window.UF) {
        console.error('UnifiedFilter 未初始化');
        return;
    }
    window.UF.filterByRegion(regionCode);
}

// 排序函数代理到排序管理器
function applySort(type, direction) {
    if (!window.SortManager) {
        console.error('SortManager 未初始化');
        return;
    }
    window.SortManager.applySort(type, direction);
}

function applyCurrentSort() {
    if (!window.SortManager) {
        console.error('SortManager 未初始化');
        return;
    }
    window.SortManager.applyCurrentSort();
}

// updateGroupCounts 功能已废弃，分组计数由 universal-filter-manager 内部处理

// 应用分组过滤
function applyGroupFilter(groupId) {
    filterByGroup(groupId);
}

// ==================== 连接管理相关函数 ====================

/**
 * 动态加载SharedWorker客户端脚本
 */
function loadSharedClientScript() {
    return new Promise((resolve, reject) => {
        if (window.StatsSharedClient) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = '/js/stats-shared-client-minimal.js';
        script.onload = resolve;
        script.onerror = () => reject(new Error('无法加载SharedWorker客户端脚本'));
        document.head.appendChild(script);
    });
}

/**
 * 初始化SharedWorker客户端
 */
async function initSharedWorkerClient() {
    // 检测安卓设备，直接使用传统 WebSocket
    const isAndroid = /Android/i.test(navigator.userAgent || '');
    if (isAndroid) {
        console.log('[Stats] 检测到安卓设备，使用传统WebSocket连接');
        initWebSocket();
        return;
    }
    
    try {
        await loadSharedClientScript();

        if (window.sharedClient) {
            return; // 已初始化
        }

        // 检查连接状态
        const hasActiveConnection = typeof StatsSharedClient.hasActiveConnection === 'function' &&
                                    StatsSharedClient.hasActiveConnection();
        const isConnectionStale = typeof StatsSharedClient.isConnectionStale === 'function' &&
                                  StatsSharedClient.isConnectionStale(60000);

        if (hasActiveConnection && !isConnectionStale) {
            localStorage.setItem('stats_connection_active', 'true');
            localStorage.setItem('stats_connection_timestamp', Date.now().toString());
        } else if (hasActiveConnection && isConnectionStale) {
            localStorage.removeItem('stats_connection_active');
            localStorage.removeItem('stats_connection_timestamp');
        } else {
            localStorage.removeItem('stats_connection_active');
            localStorage.removeItem('stats_connection_timestamp');
        }

        // 创建SharedWorker客户端
        window.sharedClient = StatsSharedClient.getInstance({
            debug: window.DEBUG_MODE || false,
            onMessage: handleWsMessage,
            onConnected: () => {
                if (window.statsReconnectTimer) {
                    clearTimeout(window.statsReconnectTimer);
                    window.statsReconnectTimer = null;
                }
                localStorage.setItem('stats_connection_active', 'true');
                localStorage.setItem('stats_connection_timestamp', Date.now().toString());
            },
            onDisconnected: () => {
                localStorage.removeItem('stats_connection_active');
                localStorage.removeItem('stats_connection_timestamp');
                if (window.statsReconnectTimer) clearTimeout(window.statsReconnectTimer);
                window.statsReconnectTimer = setTimeout(() => {
                    initSharedWorkerClient();
                }, 3000);
            }
        });
    } catch (error) {
        console.error('初始化SharedWorker客户端失败:', error);
        initWebSocket();
    }
}

/**
 * 初始化传统WebSocket连接
 */
function initWebSocket() {
    if (window.statsWs) {
        window.statsWs.close();
        window.statsWs = null;
    }

    const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${location.host}/ws/stats`;

    window.statsWs = new WebSocket(wsUrl);

    if (window.statsWs) {
        window.statsWs.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                // 🐛 调试日志 - 检查WebSocket消息是否接收
                console.log('[WebSocket] 接收到消息:', {
                    type: message.type,
                    hasData: !!message.data,
                    dataKeys: message.data ? Object.keys(message.data) : [],
                    dataSize: message.data ? Object.keys(message.data).length : 0,
                    fullMessage: message // 显示完整消息用于诊断
                });
                handleWsMessage(message, false);
            } catch (error) {
                console.error('处理WebSocket消息错误:', error);
            }
        };

        window.statsWs.onopen = () => {
            // 🐛 调试日志 - 检查WebSocket连接是否成功
            console.log('[WebSocket] 连接成功，URL:', wsUrl);
            if (window.statsReconnectTimer) {
                clearTimeout(window.statsReconnectTimer);
                window.statsReconnectTimer = null;
            }
        };

        window.statsWs.onclose = () => {
            window.statsWs = null;
            if (window.statsReconnectTimer) clearTimeout(window.statsReconnectTimer);
            window.statsReconnectTimer = setTimeout(() => {
                initWebSocket();
            }, 3000);
        };

        window.statsWs.onerror = (error) => {
            console.error('WebSocket连接错误:', error);
        };
    }
}

// ==================== 数据处理函数 ====================

/**
 * 处理WebSocket消息（主要入口点）
 */
function handleWsMessage(message, fromCache = false) {
    try {
        // 🐛 调试日志 - 检查handleWsMessage是否被调用
        console.log('[Stats] handleWsMessage 被调用:', {
            type: message.type,
            fromCache: fromCache,
            hasDataProcessor: !!window.DataProcessor
        });
        
        // 检查标签页恢复状态
        const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
        const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

        // 休眠恢复状态下只缓存数据
        if (isRecoveringFromSleep && shouldBlockDataUpdates) {
            if (message.type === 'stats' && message.data) {
                window.StatsController.saveToCache(message.data);

                // 缓存网络数据到TabLifecycleHandler
                if (window.DataProcessor) {
                    const totals = window.DataProcessor.calculateTotals(message.data);
                    if (totals && window.TabLifecycleHandler) {
                        window.TabLifecycleHandler.cachedLatestData = message.data;
                        window.TabLifecycleHandler.cachedNetworkData = {
                            downloadSpeed: totals.download,
                            uploadSpeed: totals.upload,
                            totalDownload: totals.downloadTotal,
                            totalUpload: totals.uploadTotal
                        };
                        window.TabLifecycleHandler.cachedNetworkTimestamp = Date.now();
                    }
                }
            }
            return;
        }

        // 使用DataProcessor处理消息
        if (window.DataProcessor) {
            const processedResult = window.DataProcessor.processMessage(message, fromCache);
            if (processedResult) {
                handleProcessedData(processedResult);
            }
        } else {
            // 后备处理逻辑
            handleWsMessageFallback(message, fromCache);
        }
    } catch (error) {
        console.error('处理数据消息错误:', error);
    }
}

/**
 * 处理DataProcessor处理后的数据
 */
function handleProcessedData(processedResult) {
    try {
        const { type, totals, nodes, rawData, fromCache } = processedResult;

        if (type !== 'processed_stats') {
            return;
        }

        // 保存到缓存
        window.StatsController.saveToCache(rawData);

        // 更新总体统计
        updateTotalStats({
            ...totals,
            nodes: rawData,
            rawData: rawData,
            fromCache: fromCache
        });

        // 更新仪表板网络数据
        if (window.DashboardModule && typeof window.DashboardModule.updateDashboardNetwork === 'function') {
            window.DashboardModule.updateDashboardNetwork({
                downloadSpeed: totals.download,
                uploadSpeed: totals.upload,
                totalDownload: totals.downloadTotal,
                totalUpload: totals.uploadTotal
            });
        }

        // 更新节点显示
        Object.entries(rawData).forEach(([sid, node]) => {
            updateNodeDisplay(sid, {
                ...node,
                processedData: nodes[sid]
            });
            
            // 更新流量和硬盘环形图
            if (window.TrafficDisplayManager) {
                window.TrafficDisplayManager.updateNodeCharts(sid, node);
            }
        });

        // 强制刷新排序属性
        document.querySelectorAll('.server-card').forEach(card => {
            if (card.dataset.cpu) {
                card.dataset.cpu = card.querySelector('[id$="_CPU"]')?.dataset.cpu || card.dataset.cpu;
            }
            if (card.dataset.memory) {
                card.dataset.memory = card.querySelector('[id$="_MEM"]')?.dataset.memory || card.dataset.memory;
            }
        });

        // 触发同步完成事件
        setTimeout(() => {
            document.dispatchEvent(new CustomEvent('statsSyncComplete', {
                detail: {
                    timestamp: Date.now(),
                    nodeCount: totals.nodes,
                    fromCache: fromCache
                }
            }));
        }, 50);

        // 保存最新数据
        window.lastNodeData = rawData;

    } catch (error) {
        console.error('处理已处理数据时出错:', error);
    }
}

/**
 * 后备消息处理逻辑
 */
function handleWsMessageFallback(message, fromCache = false) {
    const { type, data } = message;

    if (type === 'stats' && data && typeof data === 'object') {
        const nodeCount = Object.keys(data).length;
        if (nodeCount === 0) return;

        window.StatsController.saveToCache(data);

        // 计算简单的统计数据
        const totals = {
            nodes: nodeCount,
            online: 0,
            offline: 0,
            download: 0,
            upload: 0,
            downloadTotal: 0,
            uploadTotal: 0,
            groups: {}
        };

        Object.entries(data).forEach(([sid, node]) => {
            const isOnline = node.stat && typeof node.stat === 'object' && !node.stat.offline;

            if (isOnline) {
                totals.online++;
                if (node.stat.net) {
                    const deltaIn = Math.max(0, Number(node.stat.net.delta?.in || 0));
                    const deltaOut = Math.max(0, Number(node.stat.net.delta?.out || 0));
                    const totalIn = Math.max(0, Number(node.stat.net.total?.in || 0));
                    const totalOut = Math.max(0, Number(node.stat.net.total?.out || 0));

                    totals.download += deltaIn;
                    totals.upload += deltaOut;
                    totals.downloadTotal += totalIn;
                    totals.uploadTotal += totalOut;
                }
            } else {
                totals.offline++;
            }

            const groupId = node.group_id || 'ungrouped';
            if (!totals.groups[groupId]) {
                totals.groups[groupId] = { total: 0, online: 0 };
            }
            totals.groups[groupId].total++;
            if (isOnline) totals.groups[groupId].online++;
        });

        updateTotalStats({ ...totals, nodes: data, rawData: data });

        if (window.DashboardModule && typeof window.DashboardModule.updateDashboardNetwork === 'function') {
            window.DashboardModule.updateDashboardNetwork({
                downloadSpeed: totals.download,
                uploadSpeed: totals.upload,
                totalDownload: totals.downloadTotal,
                totalUpload: totals.uploadTotal
            });
        }

        Object.entries(data).forEach(([sid, node]) => {
            updateNodeDisplay(sid, node);
        });

        window.lastNodeData = data;
    }
}

// ==================== 节点显示更新函数 ====================

/**
 * 同步更新桌面+移动端元素
 *   baseSuffix  : 例 "_CPU" / "_DISK_USAGE"
 *   html        : 要写入的 innerHTML（或纯文本）
 *   datasetObj  : 需同步到 data-* 的键值，如 { cpu: '67.5' }
 */
function syncField(card, baseSuffix, html, datasetObj = {}) {
    const selector = `[id$="${baseSuffix}"], [id$="${baseSuffix}_MOBILE"]`;
    card.querySelectorAll(selector).forEach(el => {
        // innerHTML vs textContent：简单判断里是否含 '<'
        if (html.indexOf('<') === -1) {
            el.textContent = html;
        } else {
            el.innerHTML = html;
        }
        Object.entries(datasetObj).forEach(([k, v]) => (el.dataset[k] = v));
    });
}

/**
 * 更新节点显示
 */
function updateNodeDisplay(sid, node) {
    const card = document.querySelector(`.server-card[data-sid="${sid}"]`);
    if (!card) return;

    // 添加平滑过渡效果
    if (!card.style.transition) {
        card.style.transition = 'all 0.3s ease';
    }

    card.classList.add('updating');

    // 设置地区代码
    const regionCode = node.data?.location?.code || node.regionCode || 'UNKNOWN';
    card.dataset.region = regionCode;

    // 更新节点数据
    if (node.stat) {
        // CPU数据
        if (node.stat.cpu) {
            const cpuValue = (node.stat.cpu.multi * 100).toFixed(2);
            card.dataset.cpu = cpuValue;

            const cpuElements = card.querySelectorAll(`[id$="_CPU"]`);
            cpuElements.forEach(el => {
                el.innerHTML = `${cpuValue}<span class="metric-unit">%</span>`;
                el.dataset.cpu = cpuValue;
            });

            // 更新移动端CPU显示
            const cpuMobileEl = card.querySelector(`[id$="_CPU_MOBILE"]`);
            if (cpuMobileEl) {
                cpuMobileEl.textContent = `${(cpuValue * 1).toFixed(1)}%`;
                cpuMobileEl.dataset.cpu = cpuValue;
            }

            const cpuProgress = card.querySelector(`[id$="_CPU_progress"]`);
            if (cpuProgress) {
                cpuProgress.style.width = `${Math.min(100, Math.max(0, cpuValue))}%`;
            }
        }

        // 内存数据
        if (node.stat.mem && node.stat.mem.virtual) {
            const memValue = ((node.stat.mem.virtual.used / node.stat.mem.virtual.total) * 100).toFixed(2);
            card.dataset.memory = memValue;

            const memElements = card.querySelectorAll(`[id$="_MEM"]`);
            memElements.forEach(el => {
                el.innerHTML = `${memValue}<span class="metric-unit">%</span>`;
                el.dataset.memory = memValue;
            });

            // 更新移动端内存显示
            const memMobileEl = card.querySelector(`[id$="_MEM_MOBILE"]`);
            if (memMobileEl) {
                memMobileEl.textContent = `${(memValue * 1).toFixed(1)}%`;
                memMobileEl.dataset.memory = memValue;
            }

            const memProgress = card.querySelector(`[id$="_MEM_progress"]`);
            if (memProgress) {
                memProgress.style.width = `${Math.min(100, Math.max(0, memValue))}%`;
            }
        }

        // 网络数据
        if (node.stat.net) {
            if (node.stat.net.delta) {
                const downloadRaw = Number(node.stat.net.delta.in) || 0;
                const uploadRaw = Number(node.stat.net.delta.out) || 0;

                card.dataset.download = node.stat.net.delta.in;
                card.dataset.upload = node.stat.net.delta.out;
                card.dataset.downloadRaw = downloadRaw;
                card.dataset.uploadRaw = uploadRaw;

                const netInElements = card.querySelectorAll(`[id$="_NET_IN"]`);
                netInElements.forEach(el => {
                    el.innerHTML = window.strbps(node.stat.net.delta.in * 8);
                    el.dataset.download = node.stat.net.delta.in;
                    el.dataset.downloadRaw = downloadRaw;
                });

                // 更新移动端下载速度显示
                const netInMobileEl = card.querySelector(`[id$="_NET_IN_MOBILE"]`);
                if (netInMobileEl) {
                    netInMobileEl.innerHTML = window.strbps(node.stat.net.delta.in * 8);
                    netInMobileEl.dataset.download = node.stat.net.delta.in;
                    netInMobileEl.dataset.downloadRaw = downloadRaw;
                }

                const netOutElements = card.querySelectorAll(`[id$="_NET_OUT"]`);
                netOutElements.forEach(el => {
                    el.innerHTML = window.strbps(node.stat.net.delta.out * 8);
                    el.dataset.upload = node.stat.net.delta.out;
                    el.dataset.uploadRaw = uploadRaw;
                });

                // 更新移动端上传速度显示
                const netOutMobileEl = card.querySelector(`[id$="_NET_OUT_MOBILE"]`);
                if (netOutMobileEl) {
                    netOutMobileEl.innerHTML = window.strbps(node.stat.net.delta.out * 8);
                    netOutMobileEl.dataset.upload = node.stat.net.delta.out;
                    netOutMobileEl.dataset.uploadRaw = uploadRaw;
                }
            }

            if (node.stat.net.total) {
                card.dataset.totalDownload = node.stat.net.total.in;
                card.dataset.totalUpload = node.stat.net.total.out;
                card.dataset.totalTraffic = Number(node.stat.net.total.in) + Number(node.stat.net.total.out);
                card.dataset.downloadTotal = node.stat.net.total.in;
                card.dataset.uploadTotal = node.stat.net.total.out;

                const netInTotalElements = card.querySelectorAll(`[id$="_NET_IN_TOTAL"]`);
                netInTotalElements.forEach(el => {
                    el.innerHTML = window.strB(node.stat.net.total.in);
                    el.dataset.totalDownload = node.stat.net.total.in;
                    el.dataset.downloadTotal = node.stat.net.total.in;
                });

                const netOutTotalElements = card.querySelectorAll(`[id$="_NET_OUT_TOTAL"]`);
                netOutTotalElements.forEach(el => {
                    el.innerHTML = window.strB(node.stat.net.total.out);
                    el.dataset.totalUpload = node.stat.net.total.out;
                    el.dataset.uploadTotal = node.stat.net.total.out;
                });
            }
        }
    }

    // 更新状态指示器
    const status = window.getNodeStatus(node);
    card.dataset.status = status;

    const indicators = card.querySelectorAll('[id$="_status_indicator"]');
    indicators.forEach(indicator => {
        indicator.classList.remove('bg-green-500', 'bg-red-500');
        indicator.classList.add(status === window.NodeStatus.ONLINE ? 'bg-green-500' : 'bg-red-500');
        indicator.classList.add('rounded-full');
        if (window.innerWidth < 640) {
            indicator.classList.add('w-1.5', 'h-1.5');
        } else {
            indicator.classList.add('w-2', 'h-2');
        }
    });

    // 更新到期时间
    if (node.expire_time !== undefined) {
        card.dataset.expiration = node.expire_time;
        const expireElements = card.querySelectorAll(`[id$="_EXPIRE_TIME"]`);
        expireElements.forEach(el => {
            el.textContent = formatRemainingDays(node.expire_time);
            el.dataset.expiration = node.expire_time;
        });

        // 更新移动端到期时间显示
        const expireMobileEl = card.querySelector(`[id$="_EXPIRE_TIME_MOBILE"]`);
        if (expireMobileEl) {
            expireMobileEl.textContent = formatRemainingDays(node.expire_time);
            expireMobileEl.dataset.expire = node.expire_time;
        }
    }

    // 更新在线时间和最后在线时间
    const isOnline = node.stat && !node.stat.offline;

    // 处理离线状态遮罩的显示/隐藏
    const offlineContainer = card.querySelector(`[id="${sid}_OFFLINE_CONTAINER"]`);
    if (offlineContainer) {
        if (isOnline) {
            offlineContainer.classList.add('hidden');
        } else {
            offlineContainer.classList.remove('hidden');
        }
    }

    if (isOnline && node.stat && node.stat.host && node.stat.host.uptime !== undefined) {
        // 显示在线时间
        const uptimeContainers = card.querySelectorAll(`[id$="_UPTIME"]`);
        uptimeContainers.forEach(el => {
            const container = el.closest('div');
            if (container) container.classList.remove('hidden');
        });

        const lastOnlineContainers = card.querySelectorAll(`[id$="_LAST_ONLINE_CONTAINER"]`);
        lastOnlineContainers.forEach(container => {
            container.classList.add('hidden');
        });

        card.dataset.uptime = node.stat.host.uptime;
        const uptimeElements = card.querySelectorAll(`[id$="_UPTIME"]`);
        uptimeElements.forEach(el => {
            const uptime = node.stat.host.uptime;
            const days = Math.floor(uptime / 86400);
            const displayDays = days > 0 ? days : 1;
            el.textContent = `${displayDays}天`;
            el.dataset.uptime = node.stat.host.uptime;
        });
    } else {
        // 显示最后在线时间
        const uptimeElements = card.querySelectorAll(`[id$="_UPTIME"]`);
        uptimeElements.forEach(el => {
            const container = el.closest('div');
            if (container) container.classList.add('hidden');
        });

        const lastOnlineContainers = card.querySelectorAll(`[id$="_LAST_ONLINE_CONTAINER"]`);
        lastOnlineContainers.forEach(container => {
            container.classList.remove('hidden');
        });

        const lastOnlineElements = card.querySelectorAll(`[id$="_LAST_ONLINE"]`);
        lastOnlineElements.forEach(el => {
            if (node.last_online) {
                const lastOnlineDate = new Date(node.last_online * 1000);
                const now = new Date();
                const diffMs = now - lastOnlineDate;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                const diffDays = Math.floor(diffMs / 86400000);

                let lastOnlineText = '';
                if (diffDays > 30) {
                    lastOnlineText = lastOnlineDate.toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } else if (diffDays > 0) {
                    lastOnlineText = `${diffDays}天前离线`;
                } else if (diffHours > 0) {
                    lastOnlineText = `${diffHours}小时前离线`;
                } else if (diffMins > 0) {
                    lastOnlineText = `${diffMins}分钟前离线`;
                } else {
                    lastOnlineText = '刚刚离线';
                }

                const isOfflineBadge = el.closest('[id$="_OFFLINE_BADGE"]');
                el.textContent = lastOnlineText;

                el.setAttribute('title', lastOnlineDate.toLocaleString());
                el.dataset.lastOnline = node.last_online;
            } else {
                const isOfflineBadge = el.closest('[id$="_OFFLINE_BADGE"]');
                if (isOfflineBadge) {
                    el.textContent = '离线';
                } else {
                    el.textContent = '未知';
                }
                el.dataset.lastOnline = '0';
            }
        });
    }

    // 更新硬盘使用率
    if (node.stat && node.stat.disk && node.stat.disk.total > 0) {
        const diskUsagePercent = ((node.stat.disk.used / node.stat.disk.total) * 100).toFixed(1);
        const usedGB = Math.round(node.stat.disk.used / 1024 / 1024 / 1024);
        const totalGB = Math.round(node.stat.disk.total / 1024 / 1024 / 1024);
        card.dataset.disk = diskUsagePercent;
        
        // 使用syncField同时更新PC和移动端
        syncField(card, '_DISK_USAGE', `${diskUsagePercent}<span class="metric-unit">%</span>`, {
            disk: diskUsagePercent,
            totalDisk: node.stat.disk.total
        });
    } else {
        card.dataset.disk = 0;
        
        // 使用syncField同时更新PC和移动端
        syncField(card, '_DISK_USAGE', 'N/A', {
            disk: 0,
            totalDisk: 0
        });
    }

    // 更新延迟显示
    if (typeof node.latency === 'number') {
        // 使用syncField同时更新PC和移动端
        syncField(card, '_LATENCY', `${node.latency}<span class="metric-unit">ms</span>`, {
            latency: node.latency
        });
        
        // 更新父元素的data属性（用于气泡提示）
        card.querySelectorAll(`[id$="_LATENCY"], [id$="_LATENCY_MOBILE"]`).forEach(el => {
            el.parentElement.dataset.latency = node.latency;
            // 添加详细信息到data属性供气泡提示使用
            if (node.latency_details) {
                el.parentElement.dataset.latencyDetails = JSON.stringify(node.latency_details);
            }
        });
    }

    // 更新丢包率显示
    if (typeof node.packet_loss === 'number') {
        // 使用syncField同时更新PC和移动端
        syncField(card, '_PACKET_LOSS', `${node.packet_loss}<span class="metric-unit">%</span>`, {
            packetLoss: node.packet_loss
        });
        
        // 更新父元素的data属性
        card.querySelectorAll(`[id$="_PACKET_LOSS"], [id$="_PACKET_LOSS_MOBILE"]`).forEach(el => {
            el.parentElement.dataset.packetLoss = node.packet_loss;
            // 添加详细信息到data属性
            if (node.latency_details) {
                el.parentElement.dataset.latencyDetails = JSON.stringify(node.latency_details);
            }
        });
    }

    // 更新网络图表（如果模块已加载）
    if (typeof window.updateNetworkCharts === 'function' && 
        typeof node.latency === 'number' && 
        typeof node.packet_loss === 'number') {
        window.updateNetworkCharts(sid, node.latency, node.packet_loss);
    }

    // 更新卡片状态样式
    const currentStatus = card.dataset.status || '';
    if (currentStatus !== status) {
        card.classList.remove('online', 'offline');
        if (status === window.NodeStatus.ONLINE) {
            card.classList.add('online');
        } else if (status === window.NodeStatus.OFFLINE) {
            card.classList.add('offline');
        }
    }

    // 完成更新
    setTimeout(() => {
        card.classList.remove('updating');
    }, 50);
}

// ==================== 总体统计更新函数 ====================

/**
 * 更新总体统计信息
 */
function updateTotalStats(totals) {
    // 🐛 调试日志 - 检查stats.js中的updateTotalStats是否被调用
    console.log('[Stats] updateTotalStats 被调用，检查DashboardModule:', {
        totals: totals,
        hasDashboardModule: !!window.DashboardModule,
        hasUpdateMethod: !!(window.DashboardModule && typeof window.DashboardModule.updateTotalStats === 'function')
    });
    
    // 使用仪表盘模块
    if (window.DashboardModule && typeof window.DashboardModule.updateTotalStats === 'function') {
        const dataWithSource = {
            ...totals,
            fromCache: !!totals.fromCache
        };
        window.DashboardModule.updateTotalStats(dataWithSource);
        return;
    }

    // 后备实现
    try {
        if (!totals || typeof totals !== 'object') {
            return;
        }

        const stats = {
            nodes: typeof totals.nodes === 'object' ?
                  Object.keys(totals.nodes || {}).length :
                  Math.max(0, Number(totals.nodes) || 0),
            online: Math.max(0, Number(totals.online) || 0),
            offline: Math.max(0, Number(totals.offline) || 0),
            download: Math.max(0, Number(totals.download) || 0),
            upload: Math.max(0, Number(totals.upload) || 0),
            downloadTotal: Math.max(0, Number(totals.downloadTotal) || 0),
            uploadTotal: Math.max(0, Number(totals.uploadTotal) || 0)
        };

        // 更新基本统计
        const totalNodesEl = document.getElementById('total-nodes');
        const onlineNodesEl = document.getElementById('online-nodes');
        const offlineNodesEl = document.getElementById('offline-nodes');

        if (totalNodesEl) totalNodesEl.textContent = stats.nodes;
        if (onlineNodesEl) onlineNodesEl.textContent = stats.online;
        if (offlineNodesEl) offlineNodesEl.textContent = stats.offline;

        // 更新数据来源标识
        const dataSourceElements = document.querySelectorAll('[id$="data-source"]');
        dataSourceElements.forEach(el => {
            el.textContent = totals.fromCache ? '(缓存数据)' : '(实时数据)';
            el.style.color = totals.fromCache ? '#f59e0b' : '#10b981';
        });

        // 更新分组数量
        // updateGroupCounts(); // 已废弃，计数由 UF 内部处理
    } catch (error) {
        console.error('更新总体统计出错:', error);
    }
}

// ==================== 初始化相关函数 ====================

/**
 * 初始化网络数据显示
 */
function initNetworkDataDisplay() {
    const loadingText = '--';

    const elements = {
        downloadSpeed: document.querySelectorAll('[id$="_NET_IN"]'),
        uploadSpeed: document.querySelectorAll('[id$="_NET_OUT"]'),
        totalDownload: document.querySelectorAll('[id$="_NET_IN_TOTAL"]'),
        totalUpload: document.querySelectorAll('[id$="_NET_OUT_TOTAL"]')
    };

    elements.downloadSpeed.forEach(el => el.textContent = loadingText);
    elements.uploadSpeed.forEach(el => el.textContent = loadingText);
    elements.totalDownload.forEach(el => el.textContent = loadingText);
    elements.totalUpload.forEach(el => el.textContent = loadingText);

    document.querySelectorAll('.progress-bar').forEach(bar => {
        bar.style.width = '0%';
    });
}

/**
 * 从缓存加载并显示数据
 */
function loadCachedData() {
    const cachedNodes = window.StatsController.loadFromCache();
    if (!cachedNodes) {
        console.debug('没有找到缓存数据');
        return;
    }

    // 只在首次加载时显示缓存数据
    if (window.cacheDisplayed) {
        console.debug('缓存数据已经显示过，跳过');
        return;
    }

    window.cacheDisplayed = true;
    console.debug('加载缓存数据，节点数:', Object.keys(cachedNodes).length);

    // 计算总计数据（简化版）
    const totals = {
        nodes: Object.keys(cachedNodes).length,
        online: 0,
        offline: 0,
        download: 0,
        upload: 0,
        downloadTotal: 0,
        uploadTotal: 0,
        groups: {}
    };

    Object.entries(cachedNodes).forEach(([sid, node]) => {
        const isOnline = node.stat && typeof node.stat === 'object' && !node.stat.offline;

        if (isOnline) {
            totals.online++;
            if (node.stat.net) {
                const deltaIn = Math.max(0, Number(node.stat.net.delta?.in || 0));
                const deltaOut = Math.max(0, Number(node.stat.net.delta?.out || 0));
                const totalIn = Math.max(0, Number(node.stat.net.total?.in || 0));
                const totalOut = Math.max(0, Number(node.stat.net.total?.out || 0));

                totals.download += deltaIn;
                totals.upload += deltaOut;
                totals.downloadTotal += totalIn;
                totals.uploadTotal += totalOut;
            }
        } else {
            totals.offline++;
        }

        const groupId = node.group_id || 'ungrouped';
        if (!totals.groups[groupId]) {
            totals.groups[groupId] = { total: 0, online: 0 };
        }
        totals.groups[groupId].total++;
        if (isOnline) totals.groups[groupId].online++;
    });

    // 更新显示
    updateTotalStats({
        ...totals,
        nodes: cachedNodes,
        rawData: cachedNodes,
        fromCache: true
    });

    // 添加过渡效果
    document.querySelectorAll('.server-card').forEach(card => {
        card.style.transition = 'all 0.3s ease';
    });

    // 更新节点显示
    Object.entries(cachedNodes).forEach(([sid, node]) => {
        updateNodeDisplay(sid, {
            ...node,
            fromCache: true
        });
    });
    
    // 保存到全局变量供其他模块使用
    window.lastNodeData = cachedNodes;
    
    // 触发地区统计更新
    if (window.RegionStatsModule && typeof window.RegionStatsModule.update === 'function') {
        window.RegionStatsModule.update(cachedNodes);
    }
}

// ==================== 事件监听器初始化 ====================

/**
 * 初始化页面事件监听器
 */
function initEventListeners() {
    // 页面卸载事件
    window.addEventListener('beforeunload', () => {
        sessionStorage.setItem('scroll_position', window.scrollY.toString());

        if (window.connectionManager) {
            // 由连接管理器处理清理
        }

        // 兼容旧版本
        if (window.statsWs) {
            window.statsWs.close();
        }
        if (window.statsReconnectTimer) {
            clearTimeout(window.statsReconnectTimer);
        }
    });

    // 初始化完成事件
    document.addEventListener('statsInitializerReady', () => {
        console.log('StatsInitializer ready, modules loaded');
    });
}

// ==================== 主初始化函数 ====================

/**
 * 主页面初始化
 */
async function initializePage() {
    try {
        // 1. 使用StatsInitializer进行统一初始化
        if (window.StatsInitializer) {
            await window.StatsInitializer.initialize({
                debug: window.DEBUG_MODE || false
            });
        } else {
            console.warn('StatsInitializer未找到，使用后备初始化');
            // 后备初始化逻辑
        }

        // 2. 从缓存加载数据
        loadCachedData();

        // 3. 初始化连接
        await initSharedWorkerClient().then(() => {
            if (window.sharedClient) {
                const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
                const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

                if (isRecoveringFromSleep) {
                    setTimeout(() => {
                        if (!shouldBlockDataUpdates) {
                            window.sharedClient.requestLastData();
                        }
                    }, 1000);
                } else {
                    window.sharedClient.requestLastData();
                }
            }
        }).catch(() => {
            console.warn('SharedWorker初始化失败，回退到传统WebSocket');
            initWebSocket();
        });

        console.log('页面初始化完成');
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
}

// ==================== 页面加载事件 ====================

document.addEventListener('DOMContentLoaded', () => {
    // 生成标签页ID
    const tabId = 'tab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('connection_tab_id', tabId);

    // 初始化网络数据显示
    initNetworkDataDisplay();

    // 恢复滚动位置
    const scrollPosition = sessionStorage.getItem('scroll_position');
    if (scrollPosition) {
        setTimeout(() => {
            window.scrollTo(0, parseInt(scrollPosition));
            sessionStorage.removeItem('scroll_position');
        }, 100);
    }

    // 初始化事件监听器
    initEventListeners();

    // 初始化延迟气泡提示（如果LatencyTooltip模块已加载）
    if (window.LatencyTooltip) {
        window.LatencyTooltip.init();
    }

    // 执行主初始化
    initializePage();
});