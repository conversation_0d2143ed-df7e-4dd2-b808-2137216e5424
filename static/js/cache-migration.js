/**
 * 缓存迁移工具
 * 用于清理旧版本缓存并迁移到新的缓存管理系统
 * 
 * 使用方法：
 * 1. 在浏览器控制台运行：window.CacheMigration.migrate()
 * 2. 或在页面加载时自动执行
 */

window.CacheMigration = (function() {
    'use strict';

    // 需要清理的旧版本缓存键
    const LEGACY_CACHE_KEYS = [
        // 节点数据相关（已禁用）
        'dstatus_node_cache',
        'dashboard_node_data',
        'dashboard_last_update',
        'passive_servers_cache',

        // 网络数据相关（已禁用）
        'network_data_cache',
        'last_network_data',

        // WebSocket连接相关（已禁用）
        'stats_connection_active',
        'stats_connection_timestamp',
        'stats_first_data_received',
        'connection_tab_id',
        'stats_connection_state',

        // 统计数据相关
        'stats_data_cache',
        'stats_last_sync',

        // 旧版本主题缓存（无端口隔离）
        'theme',

        // 旧版本个性化设置缓存（无端口隔离）
        'personalization-settings',

        // 其他旧版本缓存
        'dstatus_cache_v1',
        'dstatus_cache_1.0',
        'dstatus_settings',
        'dstatus_metadata'
    ];

    /**
     * 执行缓存迁移
     * @returns {Object} 迁移结果
     */
    function migrate() {
        console.log('===== 开始缓存迁移 =====');
        
        const result = {
            cleanedKeys: [],
            totalCleaned: 0,
            errors: [],
            timestamp: new Date().toISOString()
        };

        try {
            // 1. 清理明确的旧版本键
            LEGACY_CACHE_KEYS.forEach(key => {
                if (localStorage.getItem(key) !== null) {
                    try {
                        localStorage.removeItem(key);
                        result.cleanedKeys.push(key);
                        console.log(`✓ 清理旧缓存键: ${key}`);
                    } catch (e) {
                        result.errors.push(`清理 ${key} 失败: ${e.message}`);
                    }
                }
            });

            // 2. 清理所有匹配旧版本模式的键
            const allKeys = Object.keys(localStorage);
            const patterns = [
                /^dstatus_cache_(?!2\.0_)/,  // 非2.0版本的缓存
                /^dstatus_v1_/,               // v1版本缓存
                /^stats_cache_/,              // 旧的统计缓存
                /^network_cache_/,            // 旧的网络缓存
                /^dstatus_node_cache_\d+$/,   // 端口隔离的节点缓存（已禁用）
                /^network_data_cache_/,       // 网络数据缓存（已禁用）
                /^stats_connection_/          // 连接状态缓存（已禁用）
            ];

            allKeys.forEach(key => {
                if (patterns.some(pattern => pattern.test(key))) {
                    try {
                        localStorage.removeItem(key);
                        result.cleanedKeys.push(key);
                        console.log(`✓ 清理匹配模式的缓存: ${key}`);
                    } catch (e) {
                        result.errors.push(`清理 ${key} 失败: ${e.message}`);
                    }
                }
            });

            result.totalCleaned = result.cleanedKeys.length;

            // 3. 如果CacheManager可用，执行其清理功能
            if (window.CacheManager && typeof window.CacheManager.cleanupLegacyCache === 'function') {
                console.log('执行 CacheManager 清理...');
                window.CacheManager.cleanupLegacyCache();
            }

            // 4. 重置相关状态
            resetConnectionStates();

            console.log('===== 缓存迁移完成 =====');
            console.log(`共清理 ${result.totalCleaned} 个缓存键`);
            
            if (result.errors.length > 0) {
                console.warn('迁移过程中的错误:', result.errors);
            }

        } catch (error) {
            console.error('缓存迁移失败:', error);
            result.errors.push(`总体错误: ${error.message}`);
        }

        return result;
    }

    /**
     * 重置连接状态
     */
    function resetConnectionStates() {
        // 如果有活跃的WebSocket连接，请求重新同步
        if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
            console.log('请求重新同步数据...');
            window.sharedClient.requestLastData();
        }

        // 触发数据刷新事件
        document.dispatchEvent(new CustomEvent('cacheCleared', {
            detail: { reason: 'migration' }
        }));
    }

    /**
     * 检查是否需要迁移
     * @returns {boolean}
     */
    function needsMigration() {
        // 检查是否存在任何旧版本缓存键
        return LEGACY_CACHE_KEYS.some(key => localStorage.getItem(key) !== null) ||
               Object.keys(localStorage).some(key => 
                   key.startsWith('dstatus_cache_') && !key.startsWith('dstatus_cache_2.0_')
               );
    }

    /**
     * 获取缓存统计信息
     * @returns {Object}
     */
    function getCacheStats() {
        const stats = {
            totalKeys: 0,
            legacyKeys: 0,
            currentVersionKeys: 0,
            otherKeys: 0,
            totalSize: 0
        };

        Object.keys(localStorage).forEach(key => {
            stats.totalKeys++;
            const value = localStorage.getItem(key) || '';
            stats.totalSize += key.length + value.length;

            if (LEGACY_CACHE_KEYS.includes(key)) {
                stats.legacyKeys++;
            } else if (key.startsWith('dstatus_cache_2.0_')) {
                stats.currentVersionKeys++;
            } else {
                stats.otherKeys++;
            }
        });

        stats.totalSizeKB = (stats.totalSize / 1024).toFixed(2);
        return stats;
    }

    // 页面加载时自动检查并迁移
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (needsMigration()) {
                console.log('检测到旧版本缓存，自动执行迁移...');
                setTimeout(migrate, 1000); // 延迟1秒执行，确保其他脚本已加载
            }
        });
    } else {
        // 如果脚本是动态加载的，立即检查
        if (needsMigration()) {
            console.log('检测到旧版本缓存，建议执行迁移...');
            console.log('运行 window.CacheMigration.migrate() 来清理旧缓存');
        }
    }

    // 公共API
    return {
        migrate: migrate,
        needsMigration: needsMigration,
        getCacheStats: getCacheStats,
        resetConnectionStates: resetConnectionStates
    };
})();