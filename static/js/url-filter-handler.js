/**
 * URL 参数筛选处理器
 * 处理从 URL 参数应用筛选的逻辑
 */
(function() {
    'use strict';
    
    /**
     * 从 URL 参数应用筛选
     */
    function applyFiltersFromURL() {
        const params = new URLSearchParams(window.location.search);
        
        // 处理状态筛选
        const status = params.get('status');
        if (status && window.UF) {
            window.UF.filterByStatus(status);
            
            // 更新状态筛选按钮的激活状态
            document.querySelectorAll('.status-filter').forEach(btn => {
                if (btn.getAttribute('data-status') === status) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }
        
        // 处理到期时间筛选
        const expiry = params.get('expiry');
        if (expiry && window.UF) {
            window.UF.filterByExpiry(expiry);
            
            // 更新到期筛选按钮的激活状态
            document.querySelectorAll('.expiry-filter').forEach(btn => {
                if (btn.getAttribute('data-days') === expiry) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }
        
        // 处理地区筛选
        const region = params.get('region');
        if (region && window.UF) {
            window.UF.filterByRegion(region);
            
            // 更新地区筛选的激活状态
            document.querySelectorAll('.region-tag').forEach(tag => {
                if (tag.getAttribute('data-region') === region) {
                    tag.classList.add('active');
                } else {
                    tag.classList.remove('active');
                }
            });
        }
        
        // 处理分组筛选
        const group = params.get('group');
        if (group && window.UF) {
            window.UF.filterByGroup(group);
        }
        
        // 清理 URL 参数（避免刷新时重复应用）
        if (status || expiry || region || group) {
            // 保留 theme 参数
            const theme = params.get('theme');
            const newUrl = new URL(window.location.origin + window.location.pathname);
            if (theme) {
                newUrl.searchParams.set('theme', theme);
            }
            
            // 使用 replaceState 避免创建历史记录
            window.history.replaceState(null, '', newUrl.toString());
        }
    }
    
    // 等待 UniversalFilterManager 初始化后再应用筛选
    function tryApplyFilters() {
        if (window.UF) {
            applyFiltersFromURL();
        } else {
            // 如果 UF 还未初始化，稍后重试
            setTimeout(tryApplyFilters, 100);
        }
    }
    
    // DOM 加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', tryApplyFilters);
    } else {
        tryApplyFilters();
    }
})();