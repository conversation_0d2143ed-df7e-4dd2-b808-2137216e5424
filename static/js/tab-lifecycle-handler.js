/**
 * 标签页生命周期处理脚本
 * 用于解决浏览器标签休眠和数据同步问题
 */

// 创建全局对象，便于其他模块访问
window.TabLifecycleHandler = {};

(function() {
    // 调试信息 - 确认脚本加载
    console.log('标签页生命周期处理脚本已加载 - 版本: 1.0.2');

    // 检测安卓设备
    const isAndroidDevice = /Android/i.test(navigator.userAgent || '');
    
    // 配置
    const config = {
        // 标签页被认为是"长时间休眠"的阈值（毫秒）
        longSleepThreshold: 300000, // 300秒，降低阈值提高检测灵敏度

        // 最小时间间隔阈值，用于检测异常时间间隔（毫秒）
        minTimeGapThreshold: 5000, // 5秒

        // 数据平滑过渡的持续时间（毫秒）
        transitionDuration: 1500,

        // 重连尝试间隔（毫秒）
        reconnectInterval: 2000,

        // 最大重连尝试次数
        maxReconnectAttempts: 5,

        // 是否显示重连提示弹窗
        showReconnectDialog: false,
        
        // 自动重连超时时间（毫秒）
        autoReconnectTimeout: 1000, // 1秒后自动重连，仅在showReconnectDialog=false时生效

        // 调试模式
        debug: true,
        
        // 安卓设备特殊处理：禁用数据更新拦截以解决实时更新问题
        disableDataInterceptionOnAndroid: isAndroidDevice
    };

    // 状态
    const state = {
        // 上次活跃时间戳
        lastActiveTime: Date.now(),

        // 上次可见性变化时间戳
        lastVisibilityChange: Date.now(),

        // 上次性能时间戳（用于更精确的时间测量）
        lastPerfTime: performance.now(),

        // 预期的正常时间间隔（毫秒）
        expectedInterval: 1000, // 默认1秒

        // 当前页面是否可见
        isVisible: !document.hidden,

        // 是否处于长时间休眠后恢复状态
        isRecoveringFromSleep: false,

        // 重连尝试次数
        reconnectAttempts: 0,

        // 重连定时器
        reconnectTimer: null,

        // 数据过渡动画定时器
        transitionTimers: new Map(),

        // 上次应用过渡效果的时间戳
        lastTransitionTime: 0,

        // 过渡效果节流间隔（毫秒）
        transitionThrottleInterval: 3000, // 增加到 3 秒，确保足够的节流时间

        // 是否有待处理的过渡效果
        pendingTransition: false,

        // 过渡效果节流定时器
        transitionThrottleTimer: null,

        // 数据更新计数器（用于恢复期间）
        dataUpdateCounter: 0,

        // 恢复期间缓存的最新数据
        cachedLatestData: null,

        // 是否阻止数据更新
        blockDataUpdates: false
    };

    /**
     * 日志函数
     */
    function log(...args) {
        if (config.debug || window.DEBUG_MODE) {
            console.log('[TabLifecycle]', ...args);
        }
    }

    /**
     * 检测标签页是否经历了长时间休眠
     */
    function checkForLongSleep() {
        const now = Date.now();
        const timeSinceLastActive = now - state.lastActiveTime;

        // 使用performance.now()获取更精确的时间
        const perfNow = performance.now();
        const lastPerfTime = state.lastPerfTime || perfNow;
        const perfTimeDiff = perfNow - lastPerfTime;

        console.log(`[标签页生命周期] 休眠检测: 当前时间=${now}, 上次活跃时间=${state.lastActiveTime}, 时间差=${Math.round(timeSinceLastActive / 1000)}秒, 性能时间差=${Math.round(perfTimeDiff)}ms`);

        // 更新活跃时间和性能时间
        state.lastActiveTime = now;
        state.lastPerfTime = perfNow;

        // 检测条件：时间差超过阈值 或 性能时间差异常（大于阈值但小于预期的正常间隔）
        if (timeSinceLastActive > config.longSleepThreshold ||
            (perfTimeDiff > config.minTimeGapThreshold && perfTimeDiff < state.expectedInterval * 0.8)) {

            console.log(`[标签页生命周期] 检测到休眠: 时间差=${Math.round(timeSinceLastActive / 1000)}秒, 性能时间差=${Math.round(perfTimeDiff)}ms`);
            return true;
        }

        console.log(`[标签页生命周期] 未检测到休眠: 时间差=${Math.round(timeSinceLastActive / 1000)}秒, 性能时间差=${Math.round(perfTimeDiff)}ms`);
        return false;
    }

    /**
     * 处理标签页从休眠中恢复
     */
    function handleWakeFromSleep() {
        console.log('[标签页生命周期] 开始处理标签页从休眠中恢复');

        // 标记为正在从休眠中恢复
        state.isRecoveringFromSleep = true;
        console.log(`[标签页生命周期] 恢复状态标记设置为: ${state.isRecoveringFromSleep}`);

        // 重置重连尝试次数和数据更新计数器
        state.reconnectAttempts = 0;
        state.dataUpdateCounter = 0;

        // 重置过渡效果状态
        state.lastTransitionTime = 0;
        state.pendingTransition = false;

        // 清除所有过渡定时器
        clearAllTransitionTimers();

        // 清除节流定时器
        if (state.transitionThrottleTimer) {
            clearTimeout(state.transitionThrottleTimer);
            state.transitionThrottleTimer = null;
        }

        // 清除所有数据缓存
        console.log('[标签页生命周期] 调用 clearDataCache()');
        clearDataCache();

        // 显示"正在加载"状态
        console.log('[标签页生命周期] 调用 showLoadingState()');
        showLoadingState();

        // 清理连接存储，但不尝试重连
        console.log('[标签页生命周期] 清理连接存储');
        cleanConnectionStorage();

        // 添加一个全局标志，禁止自动重连
        window.disableAutoReconnect = true;
        console.log(`[标签页生命周期] 禁止自动重连`);

        // 根据配置决定是否显示重连遮罩层
        if (config.showReconnectDialog) {
            // 显示重连遮罩层，倒计时15秒
            showReconnectOverlay(15);
        } else {
            // 静默处理，设置一个较短的定时器后自动重连
            console.log(`[标签页生命周期] 配置为静默处理，${config.autoReconnectTimeout/1000}秒后自动重连`);
            setTimeout(() => {
                handleReconnectButtonClick();
            }, config.autoReconnectTimeout);
        }

        // 应用数据平滑过渡（强制应用，忽略节流控制）
        console.log('[标签页生命周期] 调用 applyDataTransitions()');
        applyDataTransitions(true);

        // 设置更长的阻止数据更新时间
        const blockDuration = config.transitionDuration * 3; // 增加到三倍过渡时间

        // 立即阻止数据更新
        state.blockDataUpdates = true;
        console.log(`[标签页生命周期] 阻止数据更新，持续 ${blockDuration}ms`);

        // 一段时间后重置恢复状态
        const resetTimeout = config.transitionDuration * 1.5;
        console.log(`[标签页生命周期] 设置 ${resetTimeout}ms 后重置恢复状态`);

        setTimeout(() => {
            // 在重置恢复状态前，应用最新缓存的数据
            if (state.cachedLatestData) {
                console.log(`[标签页生命周期] 恢复状态重置前应用最新缓存数据`);
                applyLatestData();
            } else {
                // 如果没有缓存数据，直接恢复正常数据更新
                state.blockDataUpdates = false;
            }

            state.isRecoveringFromSleep = false;
            console.log(`[标签页生命周期] 恢复状态已重置为: ${state.isRecoveringFromSleep}`);

            // 清除所有可能的待处理过渡效果
            if (state.transitionThrottleTimer) {
                clearTimeout(state.transitionThrottleTimer);
                state.transitionThrottleTimer = null;
            }
            state.pendingTransition = false;

            // 如果配置为静默处理，已经通过上面的定时器处理重连了
        }, resetTimeout);
    }

    /**
     * 尝试重新连接
     */
    function attemptReconnect() {
        // 如果禁止自动重连，直接返回
        if (window.disableAutoReconnect) {
            log('禁止自动重连，跳过重连尝试');
            return;
        }

        // 如果已达到最大尝试次数，停止尝试
        if (state.reconnectAttempts >= config.maxReconnectAttempts) {
            log('达到最大重连尝试次数，停止尝试');
            return;
        }

        // 增加尝试次数
        state.reconnectAttempts++;

        log(`尝试重新连接 (${state.reconnectAttempts}/${config.maxReconnectAttempts})`);

        // 清理可能影响连接的存储项
        cleanConnectionStorage();

        // 尝试重新初始化连接
        if (window.sharedClient) {
            log('重新请求数据从SharedWorker');
            window.sharedClient.requestLastData();
        } else if (typeof initSharedWorkerClient === 'function') {
            log('重新初始化SharedWorker客户端');
            initSharedWorkerClient().then(() => {
                if (window.sharedClient) {
                    window.sharedClient.requestLastData();
                }
            }).catch(() => {
                log('SharedWorker初始化失败，回退到传统WebSocket');
                if (typeof initWebSocket === 'function') {
                    initWebSocket();
                }
            });
        } else if (typeof initWebSocket === 'function') {
            log('重新初始化WebSocket连接');
            initWebSocket();
        }

        // 如果仍未达到最大尝试次数，设置下一次尝试
        if (state.reconnectAttempts < config.maxReconnectAttempts) {
            state.reconnectTimer = setTimeout(attemptReconnect, config.reconnectInterval);
        }
    }

    /**
     * 显示重连遮罩层
     * @param {number} countdown - 倒计时秒数
     */
    function showReconnectOverlay(countdown = 15) {
        // 检查是否已存在遮罩层
        let overlay = document.getElementById('reconnect-overlay');
        if (overlay) {
            overlay.remove(); // 移除现有遮罩层，避免重复
        }

        // 创建遮罩层
        overlay = document.createElement('div');
        overlay.id = 'reconnect-overlay';
        overlay.className = 'fixed inset-0 bg-gray-900 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-80 z-50 flex items-center justify-center';

        // 创建内容容器
        const container = document.createElement('div');
        container.className = 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full';

        // 创建标题
        const title = document.createElement('h3');
        title.className = 'text-xl font-semibold mb-4 text-gray-900 dark:text-white';
        title.textContent = '连接已休眠';

        // 创建说明文本
        const description = document.createElement('p');
        description.className = 'mb-4 text-gray-700 dark:text-gray-300';
        description.textContent = '此标签页已经长时间处于非活动状态，连接已休眠。点击下方按钮刷新连接并获取最新数据。';

        // 创建倒计时文本
        const countdownEl = document.createElement('p');
        countdownEl.className = 'mb-4 text-gray-600 dark:text-gray-400 text-sm';
        countdownEl.textContent = `${countdown}秒后将自动刷新连接...`;

        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'flex justify-end';

        // 创建刷新按钮
        const button = document.createElement('button');
        button.id = 'reconnect-button';
        button.className = 'px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
        button.textContent = '立即刷新连接';

        // 添加按钮点击事件
        button.addEventListener('click', handleReconnectButtonClick);

        // 组装DOM
        buttonContainer.appendChild(button);
        container.appendChild(title);
        container.appendChild(description);
        container.appendChild(countdownEl);
        container.appendChild(buttonContainer);
        overlay.appendChild(container);

        // 添加到body
        document.body.appendChild(overlay);

        console.log('[标签页生命周期] 显示重连遮罩层');

        // 开始倒计时
        let remainingSeconds = countdown;
        const countdownInterval = setInterval(() => {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(countdownInterval);
                handleReconnectButtonClick(); // 倒计时结束，自动重连
            } else {
                countdownEl.textContent = `${remainingSeconds}秒后将自动刷新连接...`;
            }
        }, 1000);

        // 保存倒计时定时器，以便在需要时清除
        window.reconnectCountdownTimer = countdownInterval;
    }

    /**
     * 隐藏重连遮罩层
     */
    function hideReconnectOverlay() {
        const overlay = document.getElementById('reconnect-overlay');
        if (overlay) {
            overlay.remove();
            console.log('[标签页生命周期] 隐藏重连遮罩层');
        }

        // 清除倒计时定时器
        if (window.reconnectCountdownTimer) {
            clearInterval(window.reconnectCountdownTimer);
            window.reconnectCountdownTimer = null;
        }
    }

    /**
     * 处理重连按钮点击事件
     */
    function handleReconnectButtonClick() {
        console.log('[标签页生命周期] 刷新连接');

        // 隐藏遮罩层
        hideReconnectOverlay();

        // 允许自动重连
        window.disableAutoReconnect = false;

        // 重置重连尝试次数
        state.reconnectAttempts = 0;

        // 清理连接存储
        cleanConnectionStorage();

        // 重置恢复状态
        state.isRecoveringFromSleep = false;
        state.blockDataUpdates = false;

        // 重新初始化连接
        if (typeof initSharedWorkerClient === 'function') {
            console.log('[标签页生命周期] 重新初始化SharedWorker客户端');
            initSharedWorkerClient().then(() => {
                if (window.sharedClient) {
                    window.sharedClient.requestLastData();
                }
            }).catch(() => {
                console.warn('[标签页生命周期] SharedWorker初始化失败，回退到传统WebSocket');
                if (typeof initWebSocket === 'function') {
                    initWebSocket();
                }
            });
        } else if (typeof initWebSocket === 'function') {
            console.log('[标签页生命周期] 重新初始化WebSocket连接');
            initWebSocket();
        }
    }

    /**
     * 清理可能影响连接的存储项
     */
    function cleanConnectionStorage() {
        try {
            // 清理连接状态相关的存储项
            localStorage.removeItem('stats_connection_active');
            localStorage.removeItem('stats_first_data_received');
            localStorage.removeItem('stats_connection_timestamp'); // 同时清除时间戳，避免错误识别连接状态
            localStorage.removeItem('connection_tab_id'); // 清除标签页ID

            log('已清理连接存储');
        } catch (e) {
            console.error('清理连接存储失败:', e);
        }
    }

    /**
     * 应用数据平滑过渡（带节流控制）
     * @param {boolean} force - 是否强制应用，忽略节流控制
     */
    function applyDataTransitions(force = false) {
        const now = Date.now();
        const timeSinceLastTransition = now - state.lastTransitionTime;

        // 如果距离上次应用过渡效果的时间小于节流间隔，且不是强制应用
        if (!force && timeSinceLastTransition < state.transitionThrottleInterval) {
            // 标记有待处理的过渡效果
            if (!state.pendingTransition) {
                state.pendingTransition = true;

                // 清除之前的定时器（如果有）
                if (state.transitionThrottleTimer) {
                    clearTimeout(state.transitionThrottleTimer);
                }

                // 设置定时器，在节流间隔结束后应用过渡效果
                const remainingTime = state.transitionThrottleInterval - timeSinceLastTransition;
                state.transitionThrottleTimer = setTimeout(() => {
                    state.pendingTransition = false;
                    applyDataTransitions(true); // 强制应用
                }, remainingTime);

                console.log(`[标签页生命周期] 过渡效果被节流，将在 ${remainingTime}ms 后应用`);
            }
            return;
        }

        // 更新上次应用过渡效果的时间
        state.lastTransitionTime = now;
        state.pendingTransition = false;

        console.log('[标签页生命周期] 开始应用数据平滑过渡');

        // 为所有需要平滑过渡的元素添加过渡样式
        const cpuMemElements = document.querySelectorAll('[id$="_CPU"], [id$="_MEM"], [id$="_SWAP"], [id$="_DISK"]');
        const networkElements = document.querySelectorAll('.network-speed, .network-total');
        const progressBarElements = document.querySelectorAll('.progress-bar');

        console.log(`[标签页生命周期] 找到需要过渡的元素: CPU/内存=${cpuMemElements.length}, 网络=${networkElements.length}, 进度条=${progressBarElements.length}`);

        const transitionElements = [
            ...cpuMemElements,
            ...networkElements,
            ...progressBarElements
        ];

        // 应用过渡样式
        let appliedCount = 0;
        transitionElements.forEach(el => {
            if (!el) return;

            // 保存原始过渡样式
            const originalTransition = el.style.transition;

            // 应用新的过渡样式
            el.style.transition = `all ${config.transitionDuration}ms ease-in-out`;
            appliedCount++;

            // 存储定时器，用于恢复原始样式
            const timerId = setTimeout(() => {
                el.style.transition = originalTransition;
                state.transitionTimers.delete(el);
            }, config.transitionDuration + 100);

            // 存储定时器ID，以便在需要时清除
            state.transitionTimers.set(el, timerId);
        });

        console.log(`[标签页生命周期] 已应用过渡样式到 ${appliedCount} 个元素，过渡时间: ${config.transitionDuration}ms`);

        // 不重置数据更新计数器，让它继续累加，以便跟踪恢复期间收到的所有数据更新
        // state.dataUpdateCounter = 0;
    }

    /**
     * 清除所有过渡定时器
     */
    function clearAllTransitionTimers() {
        state.transitionTimers.forEach((timerId, el) => {
            clearTimeout(timerId);
            // 恢复元素的原始过渡样式
            if (el && el.style) {
                el.style.transition = '';
            }
        });

        state.transitionTimers.clear();
    }

    /**
     * 处理页面可见性变化
     */
    function handleVisibilityChange() {
        const now = Date.now();
        const wasVisible = state.isVisible;
        state.isVisible = !document.hidden;

        // 计算不可见持续时间
        const invisibleDuration = state.isVisible && !wasVisible
            ? now - state.lastVisibilityChange
            : 0;

        // 更新上次可见性变化时间
        state.lastVisibilityChange = now;

        // 始终输出调试信息，不受debug模式影响
        console.log(`[标签页生命周期] 页面可见性变化: ${wasVisible ? '可见' : '不可见'} -> ${state.isVisible ? '可见' : '不可见'}`);

        // 如果页面变为可见
        if (state.isVisible && !wasVisible) {
            console.log(`[标签页生命周期] 页面不可见持续时间: ${Math.round(invisibleDuration / 1000)}秒`);

            // 检查是否经历了长时间休眠
            const isLongSleep = invisibleDuration > config.longSleepThreshold;
            const isDetectedSleep = checkForLongSleep();

            console.log(`[标签页生命周期] 休眠检测结果: 时间阈值=${isLongSleep}, 休眠检测=${isDetectedSleep}`);

            if (isLongSleep || isDetectedSleep) {
                console.log(`[标签页生命周期] 检测到长时间休眠，开始处理恢复过程`);
                handleWakeFromSleep();
            } else {
                console.log(`[标签页生命周期] 未检测到长时间休眠，不需要处理`);
            }
        }
        // 如果页面变为不可见
        else if (!state.isVisible && wasVisible) {
            console.log(`[标签页生命周期] 页面变为不可见，清除定时器`);

            // 清除所有过渡定时器
            clearAllTransitionTimers();

            // 清除重连定时器
            if (state.reconnectTimer) {
                clearTimeout(state.reconnectTimer);
                state.reconnectTimer = null;
            }
        }
    }

    /**
     * 清除数据缓存
     */
    function clearDataCache() {
        console.log('[标签页生命周期] 开始清除数据缓存');

        try {
            // 清除节点数据缓存
            const nodeCache = window.CacheManager?.load();
            console.log(`[标签页生命周期] 节点缓存状态: ${nodeCache ? '存在' : '不存在'}`);
            window.CacheManager?.clear();

            // 清除网络数据缓存
            const networkCache = localStorage.getItem('network_data_cache');
            console.log(`[标签页生命周期] 网络缓存状态: ${networkCache ? '存在' : '不存在'}`);
            localStorage.removeItem('network_data_cache');

            // 清除首次数据接收标记
            const firstDataReceived = localStorage.getItem('stats_first_data_received');
            console.log(`[标签页生命周期] 首次数据接收标记: ${firstDataReceived || '不存在'}`);
            localStorage.removeItem('stats_first_data_received');

            // 清除 WebSocket 数据缓存
            if (window.sharedClient && typeof window.sharedClient.clearCache === 'function') {
                console.log('[标签页生命周期] 清除 SharedWorker 数据缓存');
                window.sharedClient.clearCache();
            } else {
                console.log(`[标签页生命周期] SharedWorker 客户端状态: ${window.sharedClient ? '存在但无clearCache方法' : '不存在'}`);
            }

            // 清除全局数据缓存
            if (window.lastNodeData) {
                console.log('[标签页生命周期] 清除全局数据缓存 lastNodeData');
                window.lastNodeData = null;
            }

            // 清除恢复期间缓存的数据
            state.cachedLatestData = null;
            state.blockDataUpdates = false;

            console.log('[标签页生命周期] 数据缓存清除完成');
        } catch (e) {
            console.error('[标签页生命周期] 清除数据缓存失败:', e);
        }
    }

    /**
     * 显示“正在加载”状态
     */
    function showLoadingState() {
        console.log('[标签页生命周期] 开始显示“正在加载”状态');

        try {
            // 将所有数值元素设置为加载状态
            const loadingText = '--';

            // CPU、内存等百分比元素
            const cpuMemElements = document.querySelectorAll('[id$="_CPU"], [id$="_MEM"], [id$="_SWAP"], [id$="_DISK"]');
            console.log(`[标签页生命周期] 找到 ${cpuMemElements.length} 个 CPU/内存元素`);

            cpuMemElements.forEach(el => {
                if (el && el.textContent) {
                    el.setAttribute('data-original-value', el.textContent);
                    el.textContent = loadingText;
                }
            });

            // 带宽和流量元素
            const netElements = document.querySelectorAll('[id$="_NET_IN"], [id$="_NET_OUT"], [id$="_NET_IN_TOTAL"], [id$="_NET_OUT_TOTAL"]');
            console.log(`[标签页生命周期] 找到 ${netElements.length} 个网络元素`);

            netElements.forEach(el => {
                if (el && el.textContent) {
                    el.setAttribute('data-original-value', el.textContent);
                    el.textContent = loadingText;
                }
            });

            // 进度条元素
            const progressBars = document.querySelectorAll('.progress-bar');
            console.log(`[标签页生命周期] 找到 ${progressBars.length} 个进度条元素`);

            progressBars.forEach(bar => {
                if (bar) {
                    bar.setAttribute('data-original-width', bar.style.width);
                    bar.style.width = '0%';
                }
            });

            console.log('[标签页生命周期] 加载状态显示完成');
        } catch (e) {
            console.error('[标签页生命周期] 显示加载状态失败:', e);
        }
    }

    /**
     * 拦截并处理数据更新
     * @param {Event} event - 原始事件
     * @returns {boolean} - 是否阻止事件继续传播
     */
    function interceptDataUpdate(event) {
        // 安卓设备跳过数据拦截，解决实时更新问题
        if (config.disableDataInterceptionOnAndroid) {
            console.log('[标签页生命周期] 安卓设备跳过数据拦截，允许正常更新');
            return false; // 不拦截，允许正常处理
        }
        
        // 如果正在从休眠中恢复，阻止原始事件
        if (state.isRecoveringFromSleep && state.blockDataUpdates) {
            console.log(`[标签页生命周期] 拦截数据更新事件 ${event.type}，避免频繁刷新`);

            // 缓存最新数据，但不更新 DOM
            if (event.detail && event.detail.data) {
                // 根据事件类型处理不同的数据
                if (event.type === 'trafficDataUpdated') {
                    // 缓存流量数据
                    if (!window.TabLifecycleHandler.cachedTrafficData) {
                        window.TabLifecycleHandler.cachedTrafficData = {};
                    }

                    // 保存各类流量数据
                    const data = event.detail.data;
                    if (data.hs) window.TabLifecycleHandler.cachedTrafficData.hs = data.hs;
                    if (data.ds) window.TabLifecycleHandler.cachedTrafficData.ds = data.ds;
                    if (data.ms) window.TabLifecycleHandler.cachedTrafficData.ms = data.ms;

                    console.log(`[标签页生命周期] 缓存了流量数据`);
                } else if (event.type === 'networkQualityUpdate') {
                    // 缓存网络质量数据
                    window.TabLifecycleHandler.cachedNetworkQualityData = event.detail.data;
                    console.log(`[标签页生命周期] 缓存了网络质量数据`);
                } else {
                    // 缓存节点数据
                    state.cachedLatestData = event.detail.data;
                    console.log(`[标签页生命周期] 缓存了最新数据，包含 ${Object.keys(event.detail.data).length} 个节点`);
                }
            }

            // 阻止事件传播
            event.stopImmediatePropagation();
            event.preventDefault();
            return true;
        }

        return false; // 不拦截，允许正常处理
    }

    /**
     * 应用缓存的最新数据
     */
    function applyLatestData() {
        // 在应用数据前先应用过渡效果
        applyDataTransitions(true);

        // 检查是否有缓存的数据
        const hasNodeData = state.cachedLatestData && typeof window.updateNodeStats === 'function';
        const hasNetworkData = window.TabLifecycleHandler && window.TabLifecycleHandler.cachedNetworkData;
        const hasTrafficData = window.TabLifecycleHandler && window.TabLifecycleHandler.cachedTrafficData;
        const hasNetworkQualityData = window.TabLifecycleHandler && window.TabLifecycleHandler.cachedNetworkQualityData;

        if (!hasNodeData && !hasNetworkData && !hasTrafficData && !hasNetworkQualityData) {
            console.log(`[标签页生命周期] 没有缓存的数据需要应用`);
            state.blockDataUpdates = false;
            return;
        }

        console.log(`[标签页生命周期] 准备应用缓存数据: 节点数据=${hasNodeData}, 网络数据=${hasNetworkData}, 流量数据=${hasTrafficData}, 网络质量数据=${hasNetworkQualityData}`);

        // 使用全局函数更新节点数据
        setTimeout(() => {
            // 先应用网络数据，因为它可能会影响仪表盘显示
            if (hasNetworkData) {
                console.log(`[标签页生命周期] 应用缓存的网络数据`);
                try {
                    window.TabLifecycleHandler.applyNetworkData();
                } catch (e) {
                    console.error(`[标签页生命周期] 应用网络数据失败:`, e);
                }
            }

            // 然后应用节点数据
            if (hasNodeData) {
                console.log(`[标签页生命周期] 应用最新缓存数据，包含 ${Object.keys(state.cachedLatestData).length} 个节点`);
                try {
                    window.updateNodeStats(state.cachedLatestData);
                    state.cachedLatestData = null;
                } catch (e) {
                    console.error(`[标签页生命周期] 应用节点数据失败:`, e);
                }
            }

            // 应用流量数据
            if (hasTrafficData) {
                console.log(`[标签页生命周期] 应用缓存的流量数据`);
                try {
                    // 触发自定义事件，通知其他组件数据已更新
                    const trafficUpdateEvent = new CustomEvent('trafficDataUpdated', {
                        detail: {
                            timestamp: Date.now(),
                            data: window.TabLifecycleHandler.cachedTrafficData,
                            fromCache: true
                        }
                    });

                    // 临时禁用拦截，以便事件能够正常处理
                    const tempBlockState = state.blockDataUpdates;
                    state.blockDataUpdates = false;

                    document.dispatchEvent(trafficUpdateEvent);

                    // 恢复拦截状态
                    state.blockDataUpdates = tempBlockState;

                    // 清除缓存
                    window.TabLifecycleHandler.cachedTrafficData = null;
                } catch (e) {
                    console.error(`[标签页生命周期] 应用流量数据失败:`, e);
                }
            }

            // 应用网络质量数据
            if (hasNetworkQualityData) {
                console.log(`[标签页生命周期] 应用缓存的网络质量数据`);
                try {
                    window.TabLifecycleHandler.applyNetworkQualityData();
                } catch (e) {
                    console.error(`[标签页生命周期] 应用网络质量数据失败:`, e);
                }
            }

            // 恢复正常数据更新
            state.blockDataUpdates = false;
            console.log(`[标签页生命周期] 恢复正常数据更新流程`);

            // 等待一段时间再触发数据更新请求，确保前面的更新已完成
            if (window.requestLatestData && typeof window.requestLatestData === 'function') {
                setTimeout(() => {
                    console.log(`[标签页生命周期] 请求最新数据`);
                    window.requestLatestData();
                }, 1000); // 增加延迟时间，确保前面的更新已完成
            }
        }, config.transitionDuration + 100);
    }

    /**
     * 处理数据更新事件
     * @param {CustomEvent} event - 数据更新事件
     */
    function handleDataUpdate(event) {
        // 如果正在拦截数据更新，则不处理
        if (interceptDataUpdate(event)) {
            return;
        }

        // 获取事件详情
        const detail = event.detail || {};

        // 如果正在从休眠中恢复
        if (state.isRecoveringFromSleep) {
            // 增加数据更新计数
            state.dataUpdateCounter++;

            // 记录数据更新
            console.log(`[标签页生命周期] 从休眠恢复后收到数据更新 (${state.dataUpdateCounter}): ${detail.nodeCount || 0} 个节点, 来源: ${detail.fromCache ? '缓存' : '实时'}`);

            // 仅对第一次数据更新应用过渡效果，后续更新则拦截
            if (state.dataUpdateCounter === 1) {
                applyDataTransitions(true);

                // 在第一次数据更新后开始拦截后续更新
                state.blockDataUpdates = true;
                console.log(`[标签页生命周期] 开始拦截后续数据更新`);

                // 缓存当前数据
                if (detail.data) {
                    state.cachedLatestData = detail.data;
                }
            }
        }
    }

    /**
     * 初始化
     */
    function init() {
        console.log('[标签页生命周期] 开始初始化标签页生命周期处理器');
        
        // 安卓设备特殊处理日志
        if (config.disableDataInterceptionOnAndroid) {
            console.log('[标签页生命周期] 🤖 检测到安卓设备，已禁用数据更新拦截以确保实时更新正常工作');
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', handleVisibilityChange);
        console.log('[标签页生命周期] 已注册 visibilitychange 事件监听器');

        // 监听数据更新事件
        document.addEventListener('statsSyncComplete', handleDataUpdate, true); // 使用捕获阶段，确保在其他处理程序之前执行
        console.log('[标签页生命周期] 已注册 statsSyncComplete 事件监听器 (捕获阶段)');

        // 监听所有可能的数据更新事件
        [
            'nodeDataUpdate',
            'networkDataUpdate',
            'dashboardUpdate',
            'trafficDataUpdated',
            'networkQualityUpdate'  // 添加网络质量更新事件
        ].forEach(eventName => {
            document.addEventListener(eventName, interceptDataUpdate, true);
            console.log(`[标签页生命周期] 已注册 ${eventName} 事件拦截器`);
        });

        // 添加对 WebSocket 请求的拦截
        if (window.StatsSharedClient && window.StatsSharedClient.prototype) {
            const originalRequestLastData = window.StatsSharedClient.prototype.requestLastData;
            window.StatsSharedClient.prototype.requestLastData = function() {
                // 检查是否处于标签页恢复状态
                const isRecoveringFromSleep = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
                const shouldBlockDataUpdates = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

                if (isRecoveringFromSleep && shouldBlockDataUpdates) {
                    console.log('[TabLifecycle] 拦截 requestLastData 调用，标签页正在从休眠中恢复');
                    return; // 跳过请求
                }

                // 调用原始方法
                return originalRequestLastData.apply(this, arguments);
            };
            console.log('[标签页生命周期] 已拦截 StatsSharedClient.prototype.requestLastData 方法');
        } else {
            console.log('[标签页生命周期] 无法拦截 StatsSharedClient.prototype.requestLastData 方法，对象不存在');
        }

        // 检查当前页面可见性状态
        state.isVisible = !document.hidden;
        console.log(`[标签页生命周期] 当前页面可见性状态: ${state.isVisible ? '可见' : '不可见'}`);

        // 定期检查标签页状态
        setInterval(() => {
            // 如果页面可见，更新活跃时间
            if (state.isVisible) {
                state.lastActiveTime = Date.now();

                // 更新性能时间
                const perfNow = performance.now();
                const lastPerfTime = state.lastPerfTime || perfNow;
                const perfTimeDiff = perfNow - lastPerfTime;

                // 更新预期的正常时间间隔（使用移动平均值）
                if (perfTimeDiff > 0 && perfTimeDiff < 10000) { // 忽略异常大的值
                    state.expectedInterval = (state.expectedInterval * 0.9) + (perfTimeDiff * 0.1);
                    state.lastPerfTime = perfNow;
                }
            }
        }, 5000);
        console.log('[标签页生命周期] 已设置定期检查器');

        // 初始化完成后立即检查一次
        setTimeout(() => {
            console.log('[标签页生命周期] 执行初始检查');
            if (state.isVisible) {
                const isLongSleep = checkForLongSleep();
                console.log(`[标签页生命周期] 初始检查结果: 休眠检测=${isLongSleep}`);

                if (isLongSleep) {
                    console.log('[标签页生命周期] 初始检查发现长时间休眠，开始处理');
                    handleWakeFromSleep();
                }
            }
        }, 1000);

        console.log('[标签页生命周期] 初始化完成');

        // 组件注册系统
        const componentRegistry = {
            // 已注册的组件列表
            components: [],

            // 注册组件
            register: function(component) {
                if (!component || typeof component !== 'object') {
                    console.error('[标签页生命周期] 无法注册组件: 组件必须是一个对象');
                    return false;
                }

                // 检查组件是否已注册
                const existingIndex = this.components.findIndex(c => c.id === component.id);
                if (existingIndex >= 0) {
                    console.log(`[标签页生命周期] 组件 ${component.id} 已注册，将被更新`);
                    this.components[existingIndex] = component;
                    return true;
                }

                // 添加到组件列表
                this.components.push(component);
                console.log(`[标签页生命周期] 组件 ${component.id || '(未命名)'} 已注册`);
                return true;
            },

            // 注销组件
            unregister: function(componentId) {
                const initialLength = this.components.length;
                this.components = this.components.filter(c => c.id !== componentId);

                if (this.components.length < initialLength) {
                    console.log(`[标签页生命周期] 组件 ${componentId} 已注销`);
                    return true;
                }

                console.warn(`[标签页生命周期] 组件 ${componentId} 未找到，无法注销`);
                return false;
            },

            // 获取所有组件
            getAll: function() {
                return this.components;
            },

            // 获取指定组件
            get: function(componentId) {
                return this.components.find(c => c.id === componentId);
            },

            // 通知所有组件休眠
            notifySleep: function() {
                console.log(`[标签页生命周期] 通知 ${this.components.length} 个组件进入休眠状态`);
                this.components.forEach(component => {
                    if (component && typeof component.onSleep === 'function') {
                        try {
                            component.onSleep();
                        } catch (e) {
                            console.error(`[标签页生命周期] 组件 ${component.id || '(未命名)'} 处理休眠事件失败:`, e);
                        }
                    }
                });
            },

            // 通知所有组件唤醒
            notifyWake: function() {
                console.log(`[标签页生命周期] 通知 ${this.components.length} 个组件唤醒`);
                this.components.forEach(component => {
                    if (component && typeof component.onWake === 'function') {
                        try {
                            component.onWake();
                        } catch (e) {
                            console.error(`[标签页生命周期] 组件 ${component.id || '(未命名)'} 处理唤醒事件失败:`, e);
                        }
                    }
                });
            },

            // 应用过渡效果到所有组件
            applyTransition: function(force = false) {
                console.log(`[标签页生命周期] 对 ${this.components.length} 个组件应用过渡效果`);
                this.components.forEach(component => {
                    if (component && typeof component.applyTransition === 'function') {
                        try {
                            component.applyTransition(force);
                        } catch (e) {
                            console.error(`[标签页生命周期] 组件 ${component.id || '(未命名)'} 应用过渡效果失败:`, e);
                        }
                    }
                });
            },

            // 检查所有组件是否应该更新
            shouldComponentsUpdate: function() {
                // 如果没有组件，返回默认值
                if (this.components.length === 0) {
                    return !state.isRecoveringFromSleep || !state.blockDataUpdates;
                }

                // 检查每个组件是否应该更新
                let shouldUpdate = true;
                this.components.forEach(component => {
                    if (component && typeof component.shouldUpdate === 'function') {
                        try {
                            const result = component.shouldUpdate();
                            shouldUpdate = shouldUpdate && result;
                        } catch (error) {
                            console.error('[标签页生命周期] 组件更新检查出错:', error);
                        }
                    }
                });
                
                // 返回最终结果与全局状态的组合
                return shouldUpdate && (!state.isRecoveringFromSleep || !state.blockDataUpdates);
            }
        };

        // 将组件注册系统添加到全局对象
        window.TabLifecycleHandler.componentRegistry = componentRegistry;
    }

    // 公开API
    window.TabLifecycleHandler = {
        ...window.TabLifecycleHandler,
        
        // 状态访问器
        get isRecoveringFromSleep() { return state.isRecoveringFromSleep; },
        get blockDataUpdates() { return state.blockDataUpdates; },
        get isAndroidDevice() { return config.disableDataInterceptionOnAndroid; },
        
        // 方法
        init: init,
        
        // 强制重置状态（用于调试）
        forceReset: function() {
            state.isRecoveringFromSleep = false;
            state.blockDataUpdates = false;
            console.log('[标签页生命周期] 状态已强制重置');
        }
    };

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // DOM已经加载完成，立即初始化
        setTimeout(init, 0);
    }
})();
