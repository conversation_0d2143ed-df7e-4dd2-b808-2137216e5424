/**
 * StatsInitializer.js
 * 统一的初始化和配置管理模块
 * 负责管理全局变量、初始化各个子模块、处理模块间的依赖关系
 */

window.StatsInitializer = (function() {
    'use strict';

    // 初始化状态跟踪
    const initState = {
        isInitialized: false,
        initializationStarted: false,
        modules: {
            connectionManager: false,
            dataProcessor: false,
            sortManager: false,
            dashboardModule: false,
            regionStatsModule: false,
            tabLifecycleHandler: false
        },
        errors: []
    };

    // 全局配置整合
    const GLOBAL_CONFIG = {
        // WebSocket配置
        websocket: {
            reconnectDelay: 3000,
            maxReconnectAttempts: 5,
            heartbeatInterval: 30000
        },

        // 缓存配置
        cache: {
            key: 'dstatus_node_cache',
            expiry: 5 * 60 * 1000, // 5分钟
            version: '1.1'
        },

        // 网络单位配置
        network: {
            units: {
                KB: 1024,
                MB: 1024 * 1024,
                GB: 1024 * 1024 * 1024,
                TB: 1024 * 1024 * 1024 * 1024,
                Kbps: 1000,
                Mbps: 1000 * 1000,
                Gbps: 1000 * 1000 * 1000,
                Tbps: 1000 * 1000 * 1000 * 1000
            }
        },

        // 节点状态配置
        nodeStatus: {
            ONLINE: 'online',
            OFFLINE: 'offline',
            HIDDEN: 'hidden'
        },

        // 排序配置
        sort: {
            defaultDirection: 'desc',
            directions: {
                default: 'desc',
                cpu: 'desc',
                memory: 'desc',
                totalTraffic: 'desc',
                upload: 'desc',
                download: 'desc',
                name: 'asc',
                expiration: 'asc'
            }
        },

        // 调试模式
        debug: false
    };

    /**
     * 调试日志函数
     */
    function log(...args) {
        if (GLOBAL_CONFIG.debug || window.DEBUG_MODE) {
            console.log('[StatsInitializer]', ...args);
        }
    }

    /**
     * 初始化全局变量（只初始化一次）
     */
    function initGlobalVariables() {
        // WebSocket连接管理
        if (typeof window.statsWs === 'undefined') {
            window.statsWs = null;
            window.statsReconnectTimer = null;
        }

        // SharedWorker客户端
        if (typeof window.sharedClient === 'undefined') {
            window.sharedClient = null;
        }

        // 当前分组跟踪
        if (typeof window.currentGroupId === 'undefined') {
            window.currentGroupId = 'all';
        }

        // 缓存配置
        if (typeof window.CACHE_CONFIG === 'undefined') {
            window.CACHE_CONFIG = GLOBAL_CONFIG.cache;
        }

        // 缓存显示状态
        if (typeof window.cacheDisplayed === 'undefined') {
            window.cacheDisplayed = false;
        }

        // 网络单位常量
        if (typeof window.KB === 'undefined') {
            Object.assign(window, GLOBAL_CONFIG.network.units);
        }

        // 节点状态常量
        if (typeof window.NodeStatus === 'undefined') {
            window.NodeStatus = GLOBAL_CONFIG.nodeStatus;
        }

        // 排序配置
        if (typeof window.SortConfig === 'undefined') {
            window.SortConfig = GLOBAL_CONFIG.sort;
        }

        // 节点样式配置
        if (typeof window.NodeStyleConfig === 'undefined') {
            window.NodeStyleConfig = {
                [window.NodeStatus.ONLINE]: {
                    indicator: 'bg-green-500',
                    card: 'online',
                    text: 'text-gray-200',
                    title: '在线'
                },
                [window.NodeStatus.OFFLINE]: {
                    indicator: 'bg-red-500',
                    card: 'offline',
                    text: 'text-gray-400',
                    title: '离线'
                },
                [window.NodeStatus.HIDDEN]: {
                    indicator: 'bg-gray-500',
                    card: 'hidden',
                    text: 'text-gray-400',
                    title: '隐藏'
                }
            };
        }

        // 设置存储键
        if (typeof window.SETTINGS_KEY === 'undefined') {
            window.SETTINGS_KEY = 'node_display_settings';
        }

        // 初始化完成标记
        if (typeof window.initializationCompleted === 'undefined') {
            window.initializationCompleted = false;
        }

        log('全局变量初始化完成');
    }

    /**
     * 初始化通用工具函数
     */
    function initUtilityFunctions() {
        // 字节格式化函数
        if (typeof window.strB === 'undefined') {
            window.strB = function(bytes) {
                if (isNaN(bytes) || bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
            };
        }

        // 网络速度格式化函数 - 统一版本，避免重复定义
        if (typeof window.strbps === 'undefined') {
            window.strbps = function(bps) {
                if (isNaN(bps) || bps === 0) return '0 <span class="metric-unit">bps</span>';
                const k = 1024;
                const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps'];
                const i = Math.floor(Math.log(bps) / Math.log(k));
                const unitIndex = Math.min(i, sizes.length - 1);
                const value = (bps / Math.pow(k, unitIndex)).toFixed(2);
                return value + ' <span class="metric-unit">' + sizes[unitIndex] + '</span>';
            };
        }

        // 元素获取函数
        if (typeof window.E === 'undefined') {
            window.E = function(id) {
                return document.getElementById(id);
            };
        }

        // 已移除旧版气泡提示函数，现在使用LatencyTooltip模块

        // 节点状态判断函数
        if (typeof window.getNodeStatus === 'undefined') {
            window.getNodeStatus = function(node) {
                if (node.status === 2) return window.NodeStatus.HIDDEN;
                if (node?.stat?.offline) return window.NodeStatus.OFFLINE;
                const isValidStat = node?.stat && typeof node.stat === 'object';
                return isValidStat ? window.NodeStatus.ONLINE : window.NodeStatus.OFFLINE;
            };
        }

        log('通用工具函数初始化完成');
    }

    /**
     * 初始化StatsController（如果不存在）
     */
    function initStatsController() {
        if (typeof window.StatsController !== 'undefined') {
            log('StatsController已存在，跳过初始化');
            return;
        }

        // 创建简化的StatsController
        window.StatsController = {
            // 防抖计时器
            updateTimer: null,
            lastUpdateTime: 0,
            MIN_UPDATE_INTERVAL: 300,

            // 缓存管理
            saveToCache(nodesData) {
                // 基于第一性原理：WebSocket每5秒推送实时数据，不再缓存节点数据
                console.debug('[StatsInitializer] 节点数据缓存已禁用，跳过缓存保存');
                return;
            },

            loadFromCache() {
                // 基于第一性原理：WebSocket每5秒推送实时数据，不再使用过期缓存
                console.debug('[StatsInitializer] 节点数据缓存已禁用，等待WebSocket实时数据');
                return null;
            },

            /**
             * 验证缓存数据是否应该被使用
             * 通过检查当前系统的服务器数量来判断是否为重装后的空系统
             */
            async validateCacheAgainstServer() {
                try {
                    const response = await fetch('/api/servers');
                    if (response.ok) {
                        const servers = await response.json();
                        const serverCount = Array.isArray(servers) ? servers.length : 0;
                        
                        // 如果当前系统没有服务器，但缓存中有数据，说明可能是重装后的情况
                        if (serverCount === 0) {
                            const cachedData = this.loadFromCache();
                            if (cachedData && Object.keys(cachedData).length > 0) {
                                log('检测到系统无服务器但缓存有数据，判断为重装场景');
                                return false; // 不使用缓存数据
                            }
                        }
                        
                        return true; // 正常情况，可以使用缓存
                    }
                } catch (error) {
                    log('服务器验证失败，使用缓存数据:', error.message);
                    return true; // 网络错误时默认使用缓存
                }
                
                return true;
            },

            // 更新函数（简化版）
            async update() {
                try {
                    if (!window.initializationCompleted) {
                        await this.performInitialUpdate();
                        window.initializationCompleted = true;
                    }

                    // 检查WebSocket连接状态并尝试重连
                    if (window.sharedClient) {
                        window.sharedClient.requestLastData();
                    } else if (!window.statsWs || window.statsWs.readyState !== WebSocket.OPEN) {
                        if (typeof SharedWorker !== 'undefined' && typeof initSharedWorkerClient === 'function') {
                            initSharedWorkerClient().catch(() => {
                                if (typeof initWebSocket === 'function') {
                                    initWebSocket();
                                }
                            });
                        } else if (typeof initWebSocket === 'function') {
                            initWebSocket();
                        }
                    }
                } catch (error) {
                    console.error('更新失败:', error);
                    this.scheduleRetry();
                }
            },

            performInitialUpdate() {
                return new Promise(async resolve => {
                    // 先验证当前系统是否有服务器数据
                    const shouldUseCachedData = await this.validateCacheAgainstServer();
                    
                    // 先尝试从缓存加载数据
                    const cachedData = this.loadFromCache();
                    if (cachedData && shouldUseCachedData) {
                        log('使用缓存数据进行初始显示');
                        // 立即使用缓存数据
                        if (window.handleWsMessage) {
                            window.handleWsMessage({ type: 'stats', data: cachedData }, true);
                        }
                    } else if (cachedData && !shouldUseCachedData) {
                        log('检测到系统重装，清理过期缓存数据');
                        localStorage.removeItem(window.CACHE_CONFIG.KEY);
                    }
                    
                    // 设置监听器等待真实数据
                    const listener = () => {
                        document.removeEventListener('statsSyncComplete', listener);
                        resolve();
                    };
                    document.addEventListener('statsSyncComplete', listener);
                    
                    // 如果有 SharedWorker 客户端，立即请求最新数据
                    if (window.sharedClient && typeof window.sharedClient.requestLastData === 'function') {
                        setTimeout(() => {
                            window.sharedClient.requestLastData();
                        }, 100);
                    }
                });
            },

            scheduleRetry(delay = 3000) {
                setTimeout(() => this.update(), delay);
            }
        };

        log('StatsController初始化完成');
    }

    /**
     * 动态加载脚本
     * @param {string} src - 脚本路径
     * @returns {Promise} 加载Promise
     */
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            // 检查脚本是否已加载
            if (document.querySelector(`script[src="${src}"]`)) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
        });
    }

    /**
     * 初始化连接管理
     */
    async function initConnectionManager() {
        try {
            // 检查ConnectionManager是否存在
            if (window.ConnectionManager) {
                log('ConnectionManager已存在');
                initState.modules.connectionManager = true;
                return true;
            }

            // 尝试加载ConnectionManager
            await loadScript('/js/ConnectionManager.js');

            if (window.ConnectionManager) {
                // 初始化ConnectionManager
                window.connectionManager = window.ConnectionManager.init({
                    onMessage: window.handleWsMessage || function() {},
                    onConnected: function() {
                        log('WebSocket连接已建立');
                    },
                    onDisconnected: function() {
                        log('WebSocket连接已断开');
                    }
                });

                initState.modules.connectionManager = true;
                log('ConnectionManager初始化成功');
                return true;
            }
        } catch (error) {
            log('ConnectionManager初始化失败，使用后备方案:', error);
            initState.errors.push({ module: 'connectionManager', error });
        }

        return false;
    }

    /**
     * 初始化数据处理器
     */
    async function initDataProcessor() {
        try {
            // 检查DataProcessor是否存在
            if (window.DataProcessor) {
                log('DataProcessor已存在');
                initState.modules.dataProcessor = true;
                return true;
            }

            // 尝试加载DataProcessor
            await loadScript('/js/DataProcessor.js');

            if (window.DataProcessor) {
                // 配置DataProcessor回调
                window.DataProcessor.setCallbacks({
                    onDataProcessed: function(result) {
                        // 可以在这里添加数据处理完成后的逻辑
                        log('数据处理完成:', result.nodeCount, '个节点');
                    },
                    onError: function(error) {
                        console.error('数据处理错误:', error);
                    }
                });

                initState.modules.dataProcessor = true;
                log('DataProcessor初始化成功');
                return true;
            }
        } catch (error) {
            log('DataProcessor初始化失败:', error);
            initState.errors.push({ module: 'dataProcessor', error });
        }

        return false;
    }

    /**
     * 初始化排序管理器
     */
    async function initSortManager() {
        try {
            // 检查SortManager是否存在
            if (window.SortManager) {
                log('SortManager已存在');
                initState.modules.sortManager = true;
                return true;
            }

            // 尝试加载SortManager
            await loadScript('/js/SortManager.js');

            if (window.SortManager) {
                // 初始化SortManager
                window.SortManager.init({
                    initialSort: GLOBAL_CONFIG.sort,
                    realtimeSort: true,
                    callbacks: {
                        onSortChange: function(type, direction, count) {
                            log(`排序变更: ${type} ${direction}, 处理: ${count}`);
                        },
                        onError: function(error) {
                            console.error('排序错误:', error);
                        }
                    }
                });

                initState.modules.sortManager = true;
                log('SortManager初始化成功');
                return true;
            }
        } catch (error) {
            log('SortManager初始化失败:', error);
            initState.errors.push({ module: 'sortManager', error });
        }

        return false;
    }

    /**
     * 初始化仪表板模块
     */
    async function initDashboardModule() {
        try {
            // 检查DashboardModule是否存在
            if (window.DashboardModule) {
                log('DashboardModule已存在');
                initState.modules.dashboardModule = true;
                return true;
            }

            // 尝试加载DashboardModule（如果存在）
            await loadScript('/js/DashboardModule.js').catch(() => {
                // DashboardModule可能不存在，这是正常的
                log('DashboardModule不存在（可选模块）');
            });

            if (window.DashboardModule) {
                initState.modules.dashboardModule = true;
                log('DashboardModule初始化成功');
                return true;
            }
        } catch (error) {
            log('DashboardModule初始化失败:', error);
            initState.errors.push({ module: 'dashboardModule', error });
        }

        return false;
    }

    /**
     * 初始化标签页生命周期处理器
     */
    async function initTabLifecycleHandler() {
        try {
            // 检查TabLifecycleHandler是否存在
            if (window.TabLifecycleHandler) {
                log('TabLifecycleHandler已存在');
                initState.modules.tabLifecycleHandler = true;
                return true;
            }

            // 尝试加载TabLifecycleHandler
            await loadScript('/js/tab-lifecycle-handler.js').catch(() => {
                log('TabLifecycleHandler不存在（可选模块）');
            });

            if (window.TabLifecycleHandler) {
                initState.modules.tabLifecycleHandler = true;
                log('TabLifecycleHandler初始化成功');
                return true;
            }
        } catch (error) {
            log('TabLifecycleHandler初始化失败:', error);
            initState.errors.push({ module: 'tabLifecycleHandler', error });
        }

        return false;
    }

    /**
     * 初始化地区统计模块
     */
    async function initRegionStatsModule() {
        try {
            // 检查RegionStatsModule是否存在
            if (window.RegionStatsModule) {
                log('RegionStatsModule已存在');

                // 检查是否已初始化
                if (window.RegionStatsModule._initialized) {
                    log('RegionStatsModule已初始化');
                    initState.modules.regionStatsModule = true;
                    return true;
                }

                // 尝试初始化
                log('尝试初始化RegionStatsModule');
                window.RegionStatsModule.init();

                // 再次检查初始化状态
                if (window.RegionStatsModule._initialized) {
                    log('RegionStatsModule初始化成功');
                    initState.modules.regionStatsModule = true;
                    return true;
                } else {
                    log('RegionStatsModule初始化失败');
                    return false;
                }
            } else {
                log('RegionStatsModule不存在，尝试加载');

                // 尝试加载RegionStatsModule
                await loadScript('/js/region-stats.js').catch(() => {
                    log('无法加载RegionStatsModule');
                });

                // 检查加载后是否存在
                if (window.RegionStatsModule) {
                    log('RegionStatsModule加载成功，尝试初始化');
                    window.RegionStatsModule.init();

                    // 检查初始化状态
                    if (window.RegionStatsModule._initialized) {
                        log('RegionStatsModule初始化成功');
                        initState.modules.regionStatsModule = true;
                        return true;
                    } else {
                        log('RegionStatsModule初始化失败');
                        return false;
                    }
                } else {
                    log('无法加载RegionStatsModule');
                    return false;
                }
            }
        } catch (error) {
            log('RegionStatsModule初始化失败:', error);
            initState.errors.push({ module: 'regionStatsModule', error });
            return false;
        }
    }

    /**
     * 初始化所有模块
     */
    async function initializeModules() {
        log('开始初始化模块...');

        // 并行初始化独立模块
        await Promise.allSettled([
            initDataProcessor(),
            initTabLifecycleHandler(),
            initDashboardModule()
        ]);

        // 初始化依赖顺序重要的模块
        // 1. 先初始化RegionStatsModule
        await initRegionStatsModule();

        // 2. 再初始化SortManager（排序功能）
        await initSortManager();

        // 3. 最后初始化ConnectionManager
        await initConnectionManager();

        log('模块初始化完成');
    }

    /**
     * 初始化页面元素
     */
    function initPageElements() {
        // 生成唯一的标签页ID
        const tabId = 'tab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('connection_tab_id', tabId);

        // 初始化网络数据显示为加载状态
        const loadingText = '--';
        const networkElements = {
            downloadSpeed: document.querySelectorAll('[id$="_NET_IN"]'),
            uploadSpeed: document.querySelectorAll('[id$="_NET_OUT"]'),
            totalDownload: document.querySelectorAll('[id$="_NET_IN_TOTAL"]'),
            totalUpload: document.querySelectorAll('[id$="_NET_OUT_TOTAL"]')
        };

        // 设置加载状态
        Object.values(networkElements).forEach(elements => {
            elements.forEach(el => el.textContent = loadingText);
        });

        // 设置进度条初始宽度
        document.querySelectorAll('.progress-bar').forEach(bar => {
            bar.style.width = '0%';
        });

        log('页面元素初始化完成');
    }

    /**
     * 处理页面卸载清理
     */
    function initPageUnloadHandler() {
        window.addEventListener('beforeunload', () => {
            // 保存滚动位置
            sessionStorage.setItem('scroll_position', window.scrollY.toString());

            // 通知连接管理器页面即将卸载
            if (window.connectionManager && window.connectionManager.handlePageUnload) {
                window.connectionManager.handlePageUnload();
            }

            // 兼容旧版本的清理
            if (window.statsWs) {
                window.statsWs.close();
            }
            if (window.statsReconnectTimer) {
                clearTimeout(window.statsReconnectTimer);
            }
        });

        log('页面卸载处理器初始化完成');
    }

    /**
     * 恢复页面滚动位置
     */
    function restoreScrollPosition() {
        const scrollPosition = sessionStorage.getItem('scroll_position');
        if (scrollPosition) {
            setTimeout(() => {
                window.scrollTo(0, parseInt(scrollPosition));
                sessionStorage.removeItem('scroll_position');
            }, 100);
        }
    }

    /**
     * 主初始化函数
     */
    async function initialize(options = {}) {
        if (initState.initializationStarted) {
            log('初始化已在进行中');
            return;
        }

        initState.initializationStarted = true;
        log('开始初始化StatsInitializer...');

        // 应用配置选项
        if (options.debug !== undefined) {
            GLOBAL_CONFIG.debug = options.debug;
        }

        try {
            // 1. 初始化全局变量
            initGlobalVariables();

            // 2. 初始化工具函数
            initUtilityFunctions();

            // 3. 初始化StatsController
            initStatsController();

            // 4. 初始化页面元素
            initPageElements();

            // 5. 初始化页面卸载处理
            initPageUnloadHandler();

            // 6. 恢复滚动位置
            restoreScrollPosition();

            // 7. 初始化各个模块
            await initializeModules();

            // 8. 标记初始化完成
            initState.isInitialized = true;

            log('StatsInitializer初始化完成');

            // 触发初始化完成事件
            document.dispatchEvent(new CustomEvent('statsInitializerReady', {
                detail: {
                    modules: initState.modules,
                    errors: initState.errors
                }
            }));

        } catch (error) {
            console.error('StatsInitializer初始化失败:', error);
            initState.errors.push({ module: 'main', error });
            throw error;
        }
    }

    /**
     * 获取初始化状态
     */
    function getInitState() {
        return {
            ...initState,
            config: GLOBAL_CONFIG
        };
    }

    /**
     * 销毁初始化器
     */
    function destroy() {
        // 清理模块
        Object.keys(initState.modules).forEach(moduleName => {
            const module = window[moduleName];
            if (module && typeof module.destroy === 'function') {
                try {
                    module.destroy();
                    log(`模块 ${moduleName} 已销毁`);
                } catch (error) {
                    console.error(`销毁模块 ${moduleName} 失败:`, error);
                }
            }
        });

        // 重置状态
        initState.isInitialized = false;
        initState.initializationStarted = false;
        Object.keys(initState.modules).forEach(key => {
            initState.modules[key] = false;
        });
        initState.errors = [];

        log('StatsInitializer已销毁');
    }

    // 导出公共API
    return {
        initialize,
        getInitState,
        destroy,

        // 工具函数
        loadScript,

        // 配置访问
        getConfig: () => ({ ...GLOBAL_CONFIG })
    };
})();