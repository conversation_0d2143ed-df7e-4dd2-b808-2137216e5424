/**
 * @file dashboard.js
 * @description 仪表盘相关功能模块，负责仪表盘数据的更新和交互
 */

// 🐛 调试日志 - 检查DashboardModule是否加载

// ============================================
// 公共工具：从 Dashboard 跳转到服务器列表并附带筛选参数
// ============================================
console.log('[Dashboard] DashboardModule 开始加载');

// 仪表盘模块
window.DashboardModule = {
    // 存储进度条实例
    progressBars: {
        download: null,
        upload: null,
        downloadMobile: null,
        uploadMobile: null
    },

    /**
     * 初始化进度条
     */
    initProgressBars() {
        // 检查ProgressBar.js是否已加载
        if (typeof ProgressBar === 'undefined') {
            console.warn('ProgressBar.js未加载，将使用默认进度条');
            return;
        }

        // 使用原始进度条
        this.useOriginalProgressBars = true;
        console.log('使用原始进度条');
    },

    /**
     * 更新进度条值
     * @param {number} downloadPercent 下载百分比
     * @param {number} uploadPercent 上传百分比
     * @param {Object} [config] 动画配置
     */
    updateProgressBars(downloadPercent, uploadPercent, config) {
        // 获取动画配置
        const animConfig = config || (window.AnimationSpeedControl ? window.AnimationSpeedControl.getConfig() : {
            duration: 1500,
            downloadUpdateInterval: 200,
            uploadUpdateInterval: 300,
            smoothFactor: 0.3
        });

        // 设置动画时间（毫秒）
        const animationDuration = animConfig.duration;

        // 存储当前值以检测变化
        if (!this.lastValues) {
            this.lastValues = {
                download: 0,
                upload: 0
            };
        }

        // 检测值是否变化
        const downloadChanged = Math.abs(this.lastValues.download - downloadPercent) > 1;
        const uploadChanged = Math.abs(this.lastValues.upload - uploadPercent) > 1;

        // 检查是否有SmoothTransition工具
        const hasSmoothTransition = typeof window.SmoothTransition !== 'undefined';

        // 更新PC端进度条
        if (hasSmoothTransition && downloadChanged) {
            // 使用平滑过渡进度条
            window.SmoothTransition.progressBar(
                'download-speed-progress',
                this.lastValues.download,
                downloadPercent,
                animationDuration,
                null,
                animConfig.downloadUpdateInterval // 使用配置的下载速度更新间隔
            );
        } else {
            // 回退到原始方法
            const downloadProgress = document.getElementById('download-speed-progress');
            if (downloadProgress) {
                // 移除所有动画模式类
                downloadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        downloadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        downloadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        downloadProgress.classList.add('progress-bar-normal');
                    }
                }

                downloadProgress.style.width = `${downloadPercent}%`;
                if (downloadChanged) {
                    downloadProgress.classList.remove('progress-highlight');
                    void downloadProgress.offsetWidth;
                    downloadProgress.classList.add('progress-highlight');
                }
            }
        }

        if (hasSmoothTransition && uploadChanged) {
            // 使用平滑过渡进度条
            window.SmoothTransition.progressBar(
                'upload-speed-progress',
                this.lastValues.upload,
                uploadPercent,
                animationDuration,
                null,
                animConfig.uploadUpdateInterval // 使用配置的上传速度更新间隔
            );
        } else {
            // 回退到原始方法
            const uploadProgress = document.getElementById('upload-speed-progress');
            if (uploadProgress) {
                // 移除所有动画模式类
                uploadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        uploadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        uploadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        uploadProgress.classList.add('progress-bar-normal');
                    }
                }

                uploadProgress.style.width = `${uploadPercent}%`;
                if (uploadChanged) {
                    uploadProgress.classList.remove('progress-highlight');
                    void uploadProgress.offsetWidth;
                    uploadProgress.classList.add('progress-highlight');
                }
            }
        }

        // 更新移动端进度条
        if (hasSmoothTransition && downloadChanged) {
            window.SmoothTransition.progressBar(
                'mobile-download-speed-progress',
                this.lastValues.download,
                downloadPercent,
                animationDuration,
                null,
                animConfig.downloadUpdateInterval // 使用配置的下载速度更新间隔
            );
        } else {
            const mobileDownloadProgress = document.getElementById('mobile-download-speed-progress');
            if (mobileDownloadProgress) {
                // 移除所有动画模式类
                mobileDownloadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        mobileDownloadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        mobileDownloadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        mobileDownloadProgress.classList.add('progress-bar-normal');
                    }
                }

                mobileDownloadProgress.style.width = `${downloadPercent}%`;
                if (downloadChanged) {
                    mobileDownloadProgress.classList.remove('progress-highlight');
                    void mobileDownloadProgress.offsetWidth;
                    mobileDownloadProgress.classList.add('progress-highlight');
                }
            }
        }

        if (hasSmoothTransition && uploadChanged) {
            window.SmoothTransition.progressBar(
                'mobile-upload-speed-progress',
                this.lastValues.upload,
                uploadPercent,
                animationDuration,
                null,
                animConfig.uploadUpdateInterval // 使用配置的上传速度更新间隔
            );
        } else {
            const mobileUploadProgress = document.getElementById('mobile-upload-speed-progress');
            if (mobileUploadProgress) {
                // 移除所有动画模式类
                mobileUploadProgress.classList.remove('progress-bar-no-animation', 'progress-bar-normal', 'progress-bar-fast');

                // 检查是否使用CSS过渡
                if (animConfig.useCssTransition) {
                    // 根据不同模式添加相应的CSS类
                    if (animConfig.enabled === false) {
                        // 关闭模式
                        mobileUploadProgress.classList.add('progress-bar-no-animation');
                    } else if (animConfig.duration <= 300) {
                        // 迅速模式
                        mobileUploadProgress.classList.add('progress-bar-fast');
                    } else {
                        // 正常模式
                        mobileUploadProgress.classList.add('progress-bar-normal');
                    }
                }

                mobileUploadProgress.style.width = `${uploadPercent}%`;
                if (uploadChanged) {
                    mobileUploadProgress.classList.remove('progress-highlight');
                    void mobileUploadProgress.offsetWidth;
                    mobileUploadProgress.classList.add('progress-highlight');
                }
            }
        }

        // 更新存储的值
        this.lastValues.download = downloadPercent;
        this.lastValues.upload = uploadPercent;
    },

    /**
     * 更新仪表盘网络数据
     * @param {Object} netStats 网络统计数据
     * @param {boolean} fromCache 是否从缓存加载
     * @param {boolean} forceUpdate 是否强制更新（忽略恢复状态）
     */
    updateDashboardNetwork(netStats, fromCache = false, forceUpdate = false) {
        if (!netStats) return;

        // 检查标签页是否处于恢复状态
        const isRecovering = window.TabLifecycleHandler && window.TabLifecycleHandler.isRecoveringFromSleep;
        const shouldBlock = window.TabLifecycleHandler && window.TabLifecycleHandler.blockDataUpdates;

        // 如果正在从休眠中恢复且应该阻止数据更新，且不是强制更新
        if (isRecovering && shouldBlock && !forceUpdate) {
            console.log('[仪表盘] 标签页正在从休眠中恢复，暂停网络数据更新');

            // 如果标签页正在从休眠中恢复，缓存最新数据而不更新UI
            if (window.TabLifecycleHandler.cachedNetworkData) {
                // 如果新数据的时间戳更新，替换缓存的数据
                const cachedTimestamp = window.TabLifecycleHandler.cachedNetworkTimestamp || 0;
                const currentTimestamp = Date.now();

                if (currentTimestamp > cachedTimestamp) {
                    window.TabLifecycleHandler.cachedNetworkData = netStats;
                    window.TabLifecycleHandler.cachedNetworkTimestamp = currentTimestamp;
                    console.log(`[仪表盘] 缓存最新网络数据，时间戳: ${currentTimestamp}`);
                }
            } else {
                // 初始化缓存
                window.TabLifecycleHandler.cachedNetworkData = netStats;
                window.TabLifecycleHandler.cachedNetworkTimestamp = Date.now();
                console.log('[仪表盘] 初始化网络数据缓存');
            }

            return; // 不继续更新UI
        }

        // 如果是从缓存加载或强制更新，应用平滑过渡
        if (fromCache || forceUpdate) {
            // 应用平滑过渡样式
            const transitionElements = document.querySelectorAll('.network-speed, .network-total');
            transitionElements.forEach(el => {
                if (!el) return;
                // 保存原始过渡样式
                const originalTransition = el.style.transition;
                // 应用新的过渡样式
                el.style.transition = `all 1500ms ease-in-out`;
                // 恢复原始样式
                setTimeout(() => {
                    el.style.transition = originalTransition;
                }, 1600);
            });
            console.log(`[仪表盘] 应用网络数据平滑过渡样式`);
        }

        // 禁用日志输出
        // 如果需要调试，请手动打开以下代码
        /*
        if (window.DEBUG_MODE) {
            console.log('更新仪表盘网络数据:', netStats);
        }
        */

        // 检查页面可见性状态
        const isPageVisible = !document.hidden;
        const now = Date.now();

        // 获取页面可见性变化时间
        const lastVisibilityChange = parseInt(localStorage.getItem('last_visibility_change') || '0', 10);
        const timeSinceVisibilityChange = now - lastVisibilityChange;

        // 检测页面是否刚刚变为可见
        const justBecameVisible = isPageVisible && timeSinceVisibilityChange < 1000;

        // 确保所有值都是有效的数字
        const safeNetStats = {
            downloadSpeed: Math.max(0, Number(netStats.downloadSpeed) || 0),
            uploadSpeed: Math.max(0, Number(netStats.uploadSpeed) || 0),
            totalDownload: Math.max(0, Number(netStats.totalDownload) || 0),
            totalUpload: Math.max(0, Number(netStats.totalUpload) || 0)
        };

        // 获取动画配置
        let animConfig = window.AnimationSpeedControl ? window.AnimationSpeedControl.getConfig() : {
            duration: 1500,
            downloadUpdateInterval: 200,
            uploadUpdateInterval: 300,
            smoothFactor: 0.3
        };

        // 如果页面刚刚变为可见，并且不可见时间超过5秒，应用平滑过渡
        if (justBecameVisible && timeSinceVisibilityChange > 5000 && this.lastSpeedValues) {
            // 应用平滑因子，避免数据突变
            const smoothFactor = animConfig.smoothFactor; // 平滑因子，越小过渡越平滑

            // 平滑过渡到新值
            safeNetStats.downloadSpeed = this.lastSpeedValues.download / 8 * (1 - smoothFactor) + safeNetStats.downloadSpeed * smoothFactor;
            safeNetStats.uploadSpeed = this.lastSpeedValues.upload / 8 * (1 - smoothFactor) + safeNetStats.uploadSpeed * smoothFactor;

            // 禁用日志输出
            // if (window.DEBUG_MODE) {
            //     console.log('应用平滑过渡，调整后的数据:', safeNetStats);
            // }
        }

        // 缓存网络数据，便于视图切换时使用
        try {
            const networkDataCache = {
                timestamp: Date.now(),
                download: safeNetStats.downloadSpeed,
                upload: safeNetStats.uploadSpeed,
                downloadTotal: safeNetStats.totalDownload,
                uploadTotal: safeNetStats.totalUpload
            };
            localStorage.setItem('network_data_cache', JSON.stringify(networkDataCache));
        } catch (error) {
            console.error('缓存网络数据失败:', error);
        }

        // 计算带宽进度条宽度（基于速度动态计算百分比）
        // 这里使用1Gbps作为100%的基准，可以根据实际情况调整
        const maxBandwidth = 1000 * 1024 * 1024; // 1Gbps
        const downloadPercent = Math.min(100, (netStats.downloadSpeed * 8 / maxBandwidth) * 100);
        const uploadPercent = Math.min(100, (netStats.uploadSpeed * 8 / maxBandwidth) * 100);

        // 禁用日志输出
        // 如果需要调试，请手动打开以下代码
        /*
        if (window.DEBUG_MODE) {
            console.log('计算的进度条百分比:', { downloadPercent, uploadPercent });
        }
        */

        // 更新进度条
        // 将animConfig传递给updateProgressBars函数
        this.updateProgressBars(downloadPercent, uploadPercent, animConfig);

        // 存储上一次的值
        if (!this.lastSpeedValues) {
            this.lastSpeedValues = {
                download: 0,
                upload: 0
            };
        }

        // 使用之前定义的animConfig变量

        // 设置动画时间（毫秒）
        let animationDuration = animConfig.duration;

        // 禁用日志输出
        // 如果需要调试，请手动打开以下代码
        /*
        if (window.DEBUG_MODE) {
            console.debug('当前动画配置:', {
                duration: animationDuration,
                downloadUpdateInterval: animConfig.downloadUpdateInterval,
                uploadUpdateInterval: animConfig.uploadUpdateInterval,
                smoothFactor: animConfig.smoothFactor
            });
        }
        */

        // 如果页面刚刚变为可见，并且不可见时间超过5秒，使用更长的动画时间
        if (justBecameVisible && timeSinceVisibilityChange > 5000) {
            animationDuration = animConfig.duration * 1.5; // 使用更长的动画时间，使过渡更加平滑
            // 禁用日志输出
            // if (window.DEBUG_MODE) {
            //     console.log('页面刚刚变为可见，使用更长的动画时间:', animationDuration);
            // }
        }

        // 检查是否有SmoothTransition工具
        const hasSmoothTransition = typeof window.SmoothTransition !== 'undefined';

        // 准备数据
        const newDownloadSpeed = netStats.downloadSpeed * 8;
        const newUploadSpeed = netStats.uploadSpeed * 8;
        const downloadSpeedText = window.strbps(newDownloadSpeed);
        const uploadSpeedText = window.strbps(newUploadSpeed);
        const totalDownloadText = window.strB(netStats.totalDownload);
        const totalUploadText = window.strB(netStats.totalUpload);

        // 检测值是否变化
        // 如果是从缓存加载，总是触发动画效果
        const downloadSpeedChanged = fromCache || Math.abs(this.lastSpeedValues.download - newDownloadSpeed) > 1024;
        const uploadSpeedChanged = fromCache || Math.abs(this.lastSpeedValues.upload - newUploadSpeed) > 1024;

        // 更新PC端实时带宽 - 使用新的 MetricFormatter 系统
        this.updateDesktopSpeed(newDownloadSpeed, newUploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);

        // 更新移动端实时带宽 - 使用新的 MetricFormatter 系统
        this.updateMobileSpeed(newDownloadSpeed, newUploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);

        // 更新PC端总流量
        this.updateTotalTraffic(netStats.totalDownload, netStats.totalUpload);

        // 更新移动端总流量
        const mobileTotalDownloads = document.querySelectorAll('.mobile-total-download');
        const mobileTotalUploads = document.querySelectorAll('.mobile-total-upload');

        mobileTotalDownloads.forEach(element => {
            element.innerHTML = totalDownloadText;
        });

        mobileTotalUploads.forEach(element => {
            element.innerHTML = totalUploadText;
        });

        // 更新存储的值
        this.lastSpeedValues.download = newDownloadSpeed;
        this.lastSpeedValues.upload = newUploadSpeed;
    },

    /**
     * 更新桌面端速度显示 - 使用新的 MetricFormatter 系统
     * @param {number} downloadSpeed - 下载速度 (bps)
     * @param {number} uploadSpeed - 上传速度 (bps)
     * @param {boolean} hasSmoothTransition - 是否启用平滑过渡
     * @param {boolean} downloadSpeedChanged - 下载速度是否变化
     * @param {boolean} uploadSpeedChanged - 上传速度是否变化
     * @param {number} animationDuration - 动画持续时间
     */
    updateDesktopSpeed(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        this.updateDesktopSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);
    },

    /**
     * 简化的桌面端速度更新方法 - 使用独立的数值和单位元素
     */
    updateDesktopSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        // 获取容器元素
        const downloadContainer = document.getElementById('current-download-speed');
        const uploadContainer = document.getElementById('current-upload-speed');
        
        // 确保容器存在
        if (!downloadContainer || !uploadContainer) {
            console.error('Speed containers not found');
            return;
        }
        
        // 获取或创建子元素
        let downloadValueEl = document.getElementById('current-download-speed-value');
        let downloadUnitEl = document.getElementById('current-download-speed-unit');
        let uploadValueEl = document.getElementById('current-upload-speed-value');
        let uploadUnitEl = document.getElementById('current-upload-speed-unit');
        
        // 如果子元素不存在，重新创建它们
        if (!downloadValueEl || !downloadUnitEl) {
            downloadContainer.innerHTML = `
                <span id="current-download-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                <span id="current-download-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
            `;
            downloadValueEl = document.getElementById('current-download-speed-value');
            downloadUnitEl = document.getElementById('current-download-speed-unit');
        }
        
        if (!uploadValueEl || !uploadUnitEl) {
            uploadContainer.innerHTML = `
                <span id="current-upload-speed-value" class="text-xl font-medium metric-number tabular-nums">--</span>
                <span id="current-upload-speed-unit" class="text-xs opacity-70 ml-1 w-10 text-left"></span>
            `;
            uploadValueEl = document.getElementById('current-upload-speed-value');
            uploadUnitEl = document.getElementById('current-upload-speed-unit');
        }

        // 格式化速度数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(downloadSpeed) : 
            this.formatSpeedSimple(downloadSpeed);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(uploadSpeed) : 
            this.formatSpeedSimple(uploadSpeed);
            

        // 更新下载速度
        if (downloadValueEl && downloadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            downloadUnitEl.textContent = downloadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && downloadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'current-download-speed-value',
                    this.lastSpeedValues.download,
                    downloadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        // 动画过程中也更新单位（如果变化）
                        if (downloadUnitEl.textContent !== result.unit) {
                            downloadUnitEl.textContent = result.unit;
                        }
                        return result.value;
                    }
                );
            } else {
                downloadValueEl.textContent = downloadResult.value;
            }
        }

        // 更新上传速度
        if (uploadValueEl && uploadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            uploadUnitEl.textContent = uploadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && uploadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'current-upload-speed-value',
                    this.lastSpeedValues.upload,
                    uploadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        // 动画过程中也更新单位（如果变化）
                        if (uploadUnitEl.textContent !== result.unit) {
                            uploadUnitEl.textContent = result.unit;
                        }
                        return result.value;
                    }
                );
            } else {
                uploadValueEl.textContent = uploadResult.value;
            }
        }

        // 添加调试信息
    },

    /**
     * 简单的速度格式化函数（备用）
     */
    formatSpeedSimple(bps) {
        if (isNaN(bps) || bps === 0) return { value: '0', unit: 'bps' };
        const k = 1024;
        const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps'];
        const i = Math.floor(Math.log(Math.abs(bps)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const value = (bps / Math.pow(k, unitIndex)).toFixed(2);
        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex]
        };
    },

    /**
     * 更新总流量显示 - 使用简化版系统
     */
    updateTotalTraffic(totalDownload, totalUpload) {
        // 获取元素
        const downloadValueEl = document.getElementById('total-download-value');
        const downloadUnitEl = document.getElementById('total-download-unit');
        const uploadValueEl = document.getElementById('total-upload-value');
        const uploadUnitEl = document.getElementById('total-upload-unit');

        // 格式化流量数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatBytes(totalDownload) : 
            this.formatBytesSimple(totalDownload);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatBytes(totalUpload) : 
            this.formatBytesSimple(totalUpload);

        // 更新下载总流量
        if (downloadValueEl && downloadUnitEl) {
            downloadValueEl.textContent = downloadResult.value;
            downloadUnitEl.textContent = downloadResult.unit;
        } else {
            // 回退到旧方式
            const totalDownloadEl = document.getElementById('total-download');
            if (totalDownloadEl && !downloadValueEl) {
                totalDownloadEl.innerHTML = window.strB ? window.strB(totalDownload) : totalDownload + ' B';
            }
        }

        // 更新上传总流量
        if (uploadValueEl && uploadUnitEl) {
            uploadValueEl.textContent = uploadResult.value;
            uploadUnitEl.textContent = uploadResult.unit;
        } else {
            // 回退到旧方式
            const totalUploadEl = document.getElementById('total-upload');
            if (totalUploadEl && !uploadValueEl) {
                totalUploadEl.innerHTML = window.strB ? window.strB(totalUpload) : totalUpload + ' B';
            }
        }
    },

    /**
     * 简单的字节格式化函数（备用）
     */
    formatBytesSimple(bytes) {
        if (isNaN(bytes) || bytes === 0) return { value: '0', unit: 'B' };
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        const unitIndex = Math.min(i, sizes.length - 1);
        const value = (bytes / Math.pow(k, unitIndex)).toFixed(2);
        return {
            value: parseFloat(value).toString(),
            unit: sizes[unitIndex]
        };
    },

    /**
     * 更新移动端速度显示 - 使用新的 MetricFormatter 系统
     * @param {number} downloadSpeed - 下载速度 (bps)
     * @param {number} uploadSpeed - 上传速度 (bps)
     * @param {boolean} hasSmoothTransition - 是否启用平滑过渡
     * @param {boolean} downloadSpeedChanged - 下载速度是否变化
     * @param {boolean} uploadSpeedChanged - 上传速度是否变化
     * @param {number} animationDuration - 动画持续时间
     */
    updateMobileSpeed(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        this.updateMobileSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration);
    },

    /**
     * 简化的移动端速度更新方法 - 使用独立的数值和单位元素（与PC端统一）
     */
    updateMobileSpeedSimplified(downloadSpeed, uploadSpeed, hasSmoothTransition, downloadSpeedChanged, uploadSpeedChanged, animationDuration) {
        // 获取新的元素结构
        const downloadValueEl = document.getElementById('mobile-download-speed-value');
        const downloadUnitEl = document.getElementById('mobile-download-speed-unit');
        const uploadValueEl = document.getElementById('mobile-upload-speed-value');
        const uploadUnitEl = document.getElementById('mobile-upload-speed-unit');

        // 格式化速度数据
        const downloadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(downloadSpeed) : 
            this.formatSpeedSimple(downloadSpeed);
        const uploadResult = window.MetricFormatter ? 
            window.MetricFormatter.formatSpeed(uploadSpeed) : 
            this.formatSpeedSimple(uploadSpeed);

        // 更新下载速度
        if (downloadValueEl && downloadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            downloadUnitEl.textContent = downloadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && downloadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'mobile-download-speed-value',
                    this.lastSpeedValues.download,
                    downloadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        return result.value;
                    }
                );
            } else {
                downloadValueEl.textContent = downloadResult.value;
            }
        }

        // 更新上传速度
        if (uploadValueEl && uploadUnitEl) {
            // 始终更新单位，确保初始化时有内容
            uploadUnitEl.textContent = uploadResult.unit;

            // 动画或直接更新数值
            if (hasSmoothTransition && uploadSpeedChanged && window.SmoothTransition) {
                window.SmoothTransition.speedtestStyleTransition(
                    'mobile-upload-speed-value',
                    this.lastSpeedValues.upload,
                    uploadSpeed,
                    animationDuration,
                    (value) => {
                        const result = window.MetricFormatter ? 
                            window.MetricFormatter.formatSpeed(value) : 
                            this.formatSpeedSimple(value);
                        return result.value;
                    }
                );
            } else {
                uploadValueEl.textContent = uploadResult.value;
            }
        }

    },

    /**
     * 更新总体统计数据
     * @param {Object} totals 总体统计数据
     */
    updateTotalStats(totals) {
        try {
            // 🐛 调试日志 - 验证数据是否到达
            console.log('[Dashboard] updateTotalStats 被调用，数据:', {
                totals: totals,
                nodes: totals?.nodes ? Object.keys(totals.nodes).length : 'undefined',
                online: totals?.online,
                offline: totals?.offline
            });
            
            // 1. 数据验证
            if (!totals || typeof totals !== 'object') {
                console.log('[Dashboard] 数据验证失败，totals无效');
                return;
            }

            // 2. 确保所有数值有效
            const stats = {
                // 兼容两种格式：直接数字或对象格式
                nodes: typeof totals.nodes === 'object' ?
                      Object.keys(totals.nodes || {}).length :
                      Math.max(0, Number(totals.nodes) || 0),
                online: Math.max(0, Number(totals.online) || 0),
                offline: Math.max(0, Number(totals.offline) || 0),
                download: Math.max(0, Number(totals.download) || 0),
                upload: Math.max(0, Number(totals.upload) || 0),
                downloadTotal: Math.max(0, Number(totals.downloadTotal) || 0),
                uploadTotal: Math.max(0, Number(totals.uploadTotal) || 0)
            };
            
            // 🐛 调试日志 - 验证处理后的统计数据
            console.log('[Dashboard] 处理后的统计数据:', stats);

            // 3. 更新显示元素
            const elements = {
                totalNodes: document.getElementById('total-nodes'),
                onlineNodes: document.getElementById('online-nodes'),
                offlineNodes: document.getElementById('offline-nodes'),
                currentNetIn: document.getElementById('current-download-speed'),
                currentNetOut: document.getElementById('current-upload-speed'),
                totalNetIn: document.getElementById('total-download'),
                totalNetOut: document.getElementById('total-upload'),
                expiringNodes3: document.getElementById('expiring-nodes-3'),
                expiringNodes7: document.getElementById('expiring-nodes-7'),
                expiringNodes30: document.getElementById('expiring-nodes-30'),
                regionStats: document.getElementById('region-stats')
            };

            // 4. 更新基础统计
            // 🐛 调试日志 - 验证DOM元素更新
            console.log('[Dashboard] 开始更新DOM元素:', {
                totalNodesEl: !!elements.totalNodes,
                onlineNodesEl: !!elements.onlineNodes,
                offlineNodesEl: !!elements.offlineNodes,
                statsValues: { nodes: stats.nodes, online: stats.online, offline: stats.offline }
            });
            
            if (elements.totalNodes) elements.totalNodes.textContent = stats.nodes;
            if (elements.onlineNodes) elements.onlineNodes.textContent = stats.online;
            if (elements.offlineNodes) elements.offlineNodes.textContent = stats.offline;
            if (elements.currentNetIn) elements.currentNetIn.textContent = window.strbps(stats.download * 8);
            if (elements.currentNetOut) elements.currentNetOut.textContent = window.strbps(stats.upload * 8);
            if (elements.totalNetIn) elements.totalNetIn.textContent = window.strB(stats.downloadTotal);
            if (elements.totalNetOut) elements.totalNetOut.textContent = window.strB(stats.uploadTotal);

            // 6. 计算不同天数内到期的节点
            const now = Math.floor(Date.now() / 1000);
            const threeDaysFromNow = now + (3 * 24 * 60 * 60);
            const sevenDaysFromNow = now + (7 * 24 * 60 * 60);
            const thirtyDaysFromNow = now + (30 * 24 * 60 * 60);
            let expiringCount3Days = 0;
            let expiringCount7Days = 0;
            let expiringCount30Days = 0;

            // 7. 处理每个节点 - 提取到期时间检查
            Object.entries(totals.nodes || {}).forEach(([, node]) => {
                // 跳过非节点数据
                if (!node || typeof node !== 'object' || !node.name) return;

                // 检查到期时间
                if (node.expire_time && node.expire_time > now) {
                    // 3天内到期
                    if (node.expire_time <= threeDaysFromNow) {
                        expiringCount3Days++;
                        expiringCount7Days++;
                        expiringCount30Days++;
                    }
                    // 7天内到期
                    else if (node.expire_time <= sevenDaysFromNow) {
                        expiringCount7Days++;
                        expiringCount30Days++;
                    }
                    // 30天内到期
                    else if (node.expire_time <= thirtyDaysFromNow) {
                        expiringCount30Days++;
                    }
                }
            });

            // 8. 处理节点地区信息，便于后续筛选
            let regionCount = 0;
            Object.entries(totals.nodes || {}).forEach(([, node]) => {
                if (!node || typeof node !== 'object' || !node.name) return;

                // 将地区信息添加到节点数据上，用于后续筛选
                if (!node.regionCode) {
                    if (node.data?.location?.code) {
                        node.regionCode = node.data.location.code;
                        regionCount++;
                    }
                }
            });

            // 9. 更新地区统计
            if (window.RegionStatsModule) {
                // 不再主动调用update，而是让RegionStatsModule自行决定何时更新
                // RegionStatsModule从window.lastNodeData获取数据
                window.lastNodeData = totals.nodes || {};
            }

            // 10. 更新分组统计和到期时间显示
            if (totals.groups) {
                Object.entries(totals.groups).forEach(([groupId, groupStats]) => {
                    const countElement = document.getElementById(`group-${groupId}-count-tab`);
                    if (countElement) {
                        countElement.textContent = `${groupStats.online}/${groupStats.total}`;
                    }
                });
            }

            // 更新到期时间显示
            if (elements.expiringNodes3) {
                elements.expiringNodes3.textContent = expiringCount3Days;
            }
            if (elements.expiringNodes7) {
                elements.expiringNodes7.textContent = expiringCount7Days;
            }
            if (elements.expiringNodes30) {
                elements.expiringNodes30.textContent = expiringCount30Days;
            }
        } catch (error) {
            console.error('更新总体统计出错:', error);
        }
    },

    /**
     * 初始化地区统计功能
     */
    initRegionStats() {
        // 检查是否已经加载RegionStatsModule
        if (!window.RegionStatsModule) {
            // 检查当前页面是否需要地区统计功能
            // 只有仪表板页面才需要，定义为存在region-stats元素的页面
            const needRegionStats = document.getElementById('region-stats') || document.getElementById('region-stats-mobile');

            if (needRegionStats) {
                console.warn('RegionStatsModule未加载，地区统计功能可能不可用');
            }
        }
    },

    /**
     * 初始化到期节点筛选功能
     */
    initExpiryFilters() {
        // 获取所有到期筛选按钮
        const expiryFilters = document.querySelectorAll('.expiry-filter');

        // 为每个到期筛选按钮添加点击事件
        expiryFilters.forEach(filter => {
            filter.addEventListener('click', (event) => {
                // 防止事件冒泡到文档级别
                event.stopPropagation();

const days = parseInt(filter.dataset.days, 10);
if (window.UF) {
                    window.UF.filterByExpiry(isNaN(days) ? '' : days);
                }
            });
        });

        // 添加文档级别的点击事件监听器
        document.addEventListener('click', (event) => {
            // 检查是否有激活的到期筛选
            if (window.activeExpiryFilter) {
                // 检查点击的元素是否是到期筛选按钮或其子元素
                const isExpiryFilter = event.target.closest('.expiry-filter');

                // 如果不是到期筛选按钮，则取消筛选
                if (!isExpiryFilter) {
                    if (window.UF) {
                        window.UF.filterByExpiry('');
                    }
                }
            }
        });
    },

    /**
     * 初始化节点状态筛选功能
     */
    initStatusFilters() {
        // 获取所有状态筛选按钮
        const statusFilters = document.querySelectorAll('.status-filter');

        // 设置默认激活状态为'ALL'
        const allButton = document.querySelector('.status-filter[data-status="ALL"]');
        if (allButton) {
            allButton.classList.add('active');
        }

        // 为每个状态筛选按钮添加点击事件
        statusFilters.forEach(filter => {
            filter.addEventListener('click', (event) => {
                event.stopPropagation();
                const status = filter.dataset.status;
                
                // 🐛 调试日志
                console.log('[Dashboard] 状态筛选按钮点击:', status);
                
                // 更新激活状态
                statusFilters.forEach(f => f.classList.remove('active'));
                filter.classList.add('active');
                
                if (window.UF) {
                    console.log('[Dashboard] 调用 UF.filterByStatus:', status);
                    window.UF.filterByStatus(status);
                } else {
                    console.error('[Dashboard] window.UF 未定义！');
                }
            });
        });

        // 点击其他地方时重置为ALL
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.status-filter') && !event.target.closest('.dashboard-card')) {
                const allBtn = document.querySelector('.status-filter[data-status="ALL"]');
                if (allBtn && !allBtn.classList.contains('active')) {
                    statusFilters.forEach(f => f.classList.remove('active'));
                    allBtn.classList.add('active');
                    
                    if (window.UF) {
                        console.log('[Dashboard] 点击外部，重置为ALL');
                        window.UF.filterByStatus('ALL');
                    }
                }
            }
        });
    },

    /**
     * 初始化页面可见性监听
     */
    initVisibilityTracking() {
        // 记录初始可见性状态
        localStorage.setItem('last_visibility_change', Date.now().toString());

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            const now = Date.now();
            const isVisible = !document.hidden;

            if (window.DEBUG_MODE) {
                console.log(`页面可见性变化: ${isVisible ? '可见' : '不可见'}`);
            }

            // 更新可见性变化时间戳
            localStorage.setItem('last_visibility_change', now.toString());

            // 如果页面变为可见，清除缓存的带宽数据
            if (isVisible) {
                // 检查不可见时间是否超过5秒
                const lastHiddenTime = parseInt(localStorage.getItem('last_hidden_time') || '0', 10);
                const hiddenDuration = now - lastHiddenTime;

                if (hiddenDuration > 5000) {
                    if (window.DEBUG_MODE) {
                        console.log(`页面不可见时间超过5秒: ${hiddenDuration}ms，将应用平滑过渡`);
                    }
                }
            } else {
                // 记录页面变为不可见的时间
                localStorage.setItem('last_hidden_time', now.toString());
            }
        });
    },

    /**
     * 初始化网络数据显示
     */
    initNetworkDisplay() {
        // 显示加载状态
        const loadingText = '--';

        // 设置所有网络数据显示为加载状态
        const elements = {
            downloadSpeed: document.getElementById('current-download-speed'),
            uploadSpeed: document.getElementById('current-upload-speed'),
            totalDownload: document.getElementById('total-download'),
            totalUpload: document.getElementById('total-upload'),
            mobileDownloadSpeeds: document.querySelectorAll('.mobile-download-speed'),
            mobileUploadSpeeds: document.querySelectorAll('.mobile-upload-speed'),
            mobileTotalDownloads: document.querySelectorAll('.mobile-total-download'),
            mobileTotalUploads: document.querySelectorAll('.mobile-total-upload')
        };

        // 设置进度条初始宽度为0
        const progressBars = {
            downloadProgress: document.getElementById('download-speed-progress'),
            uploadProgress: document.getElementById('upload-speed-progress'),
            mobileDownloadProgress: document.getElementById('mobile-download-speed-progress'),
            mobileUploadProgress: document.getElementById('mobile-upload-speed-progress')
        };

        // 设置文本内容
        if (elements.downloadSpeed) elements.downloadSpeed.textContent = loadingText;
        if (elements.uploadSpeed) elements.uploadSpeed.textContent = loadingText;
        if (elements.totalDownload) elements.totalDownload.textContent = loadingText;
        if (elements.totalUpload) elements.totalUpload.textContent = loadingText;

        elements.mobileDownloadSpeeds.forEach(el => el.textContent = loadingText);
        elements.mobileUploadSpeeds.forEach(el => el.textContent = loadingText);
        elements.mobileTotalDownloads.forEach(el => el.textContent = loadingText);
        elements.mobileTotalUploads.forEach(el => el.textContent = loadingText);

        // 设置进度条
        if (progressBars.downloadProgress) progressBars.downloadProgress.style.width = '0%';
        if (progressBars.uploadProgress) progressBars.uploadProgress.style.width = '0%';
        if (progressBars.mobileDownloadProgress) progressBars.mobileDownloadProgress.style.width = '0%';
        if (progressBars.mobileUploadProgress) progressBars.mobileUploadProgress.style.width = '0%';
    },

    /**
     * 初始化动画速度变化事件监听器
     */
    initAnimationSpeedListener() {
        // 监听动画速度变化事件
        document.addEventListener('animation:speed:changed', (e) => {
            if (e.detail && e.detail.config) {
                if (window.DEBUG_MODE) {
                    console.log('动画速度已变化:', e.detail.speed);
                }
                // 如果需要，可以在这里添加其他处理逻辑
            }
        });
    },

    /**
     * 初始化仪表盘
     */
    init() {
        // 检查新的格式化系统
        if (window.METRIC_FORMATTER_AVAILABLE && window.MetricFormatter) {
            console.log('✅ Dashboard initialized with MetricFormatter system');
            // 启用调试模式用于测试
            window.DEBUG_MODE = true;
        } else {
            console.log('⚠️ Dashboard initialized with legacy formatting system');
        }

        // 初始化网络数据显示
        this.initNetworkDisplay();

        this.initRegionStats();
        this.initExpiryFilters();
        this.initStatusFilters();
        this.initVisibilityTracking();
        this.initAnimationSpeedListener();

        // 初始化节点状态筛选
        this.initNodeStatsFilters();

        // 加载ProgressBar.js库
        this.loadProgressBarLibrary();

        // 从缓存加载网络数据
        this.loadNetworkDataFromCache();

        // 地区统计刷新按钮事件由 region-refresh.js 处理
    },

    /**
     * 从缓存加载网络数据
     */
    loadNetworkDataFromCache() {
        try {
            // 尝试从缓存加载网络数据
            const cachedNetworkData = localStorage.getItem('network_data_cache');
            if (cachedNetworkData) {
                const networkData = JSON.parse(cachedNetworkData);

                // 检查缓存是否过期
                const now = Date.now();
                if (now - networkData.timestamp < 60000) { // 1分钟过期
                    if (window.DEBUG_MODE) {
                        console.log('从缓存加载网络数据:', networkData);
                    }

                    // 初始化lastSpeedValues，确保动画效果正常
                    if (!this.lastSpeedValues) {
                        this.lastSpeedValues = {
                            download: 0,
                            upload: 0
                        };
                    }

                    // 确保所有值都是有效的数字
                    const safeNetworkData = {
                        download: Math.max(0, Number(networkData.download) || 0),
                        upload: Math.max(0, Number(networkData.upload) || 0),
                        downloadTotal: Math.max(0, Number(networkData.downloadTotal) || 0),
                        uploadTotal: Math.max(0, Number(networkData.uploadTotal) || 0)
                    };

                    // 延迟一点时间再更新，确保动画效果能正常显示
                    setTimeout(() => {
                        // 立即更新仪表盘网络数据
                        this.updateDashboardNetwork({
                            downloadSpeed: safeNetworkData.download,
                            uploadSpeed: safeNetworkData.upload,
                            totalDownload: safeNetworkData.downloadTotal,
                            totalUpload: safeNetworkData.uploadTotal
                        }, true); // 添加第二个参数标记这是从缓存加载的
                    }, 100);
                } else {
                    console.log('网络数据缓存已过期');
                    localStorage.removeItem('network_data_cache');
                }
            }
        } catch (error) {
            console.error('加载缓存网络数据失败:', error);
            localStorage.removeItem('network_data_cache');
        }
    },

    /**
     * 加载ProgressBar.js库
     * 注意：我们已经决定使用原始进度条，不再加载ProgressBar.js库
     */
    loadProgressBarLibrary() {
        // 直接使用原始进度条
        console.log('使用原始进度条，不加载ProgressBar.js库');
    },

    /**
     * 初始化节点统计筛选器
     */
    initNodeStatsFilters() {
        const statsContainer = document.querySelector('.dashboard-card');
        if (!statsContainer) return;

        statsContainer.addEventListener('click', (event) => {
            const target = event.target.closest('.status-filter');
            if (target && window.UF) {
                const status = target.dataset.status;
                window.UF.filterByStatus(status);
            }
        });
    }
};

// 当DOM加载完成时初始化仪表盘
document.addEventListener('DOMContentLoaded', () => {
    if (window.DashboardModule) {
        window.DashboardModule.init();
    }
});