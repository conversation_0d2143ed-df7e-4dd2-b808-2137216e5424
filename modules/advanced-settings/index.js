"use strict";
const express = require("express");

module.exports = (svr) => {
    const { db, pr } = svr.locals;
    const router = express.Router();

    // 定义 requireAdmin 中间件
    const requireAdmin = (req, res, next) => {
        if (!req.admin) {
            return res.status(403).json({ error: '需要管理员权限' });
        }
        next();
    };

    // GET /admin/advanced-settings - 显示高级设置页面
    router.get('/admin/advanced-settings', requireAdmin, async (req, res) => {
        try {
            console.log('[高级设置] 访问高级设置页面');

            // 从数据库获取设置值
            const pollingInterval = await db.setting.get('polling_interval') || 3000;
            const websocketInterval = await db.setting.get('websocket_interval') || 4000;
            
            // 获取数据保留设置
            const dataRetentionArchiveHours = await db.setting.get('data_retention_archive_hours') || 3;
            const dataRetentionMinuteDays = await db.setting.get('data_retention_minute_days') || 14;
            const dataRetentionHourDays = await db.setting.get('data_retention_hour_days') || 90;
            
            // 获取PostgreSQL配置
            const postgresConfig = await db.postgresConfig.get();
            const postgresStatus = db.adapter.getStatus ? db.adapter.getStatus() : null;

            // 将毫秒转换为秒以便在前端显示
            const pollingIntervalSeconds = pollingInterval / 1000;
            const websocketIntervalSeconds = websocketInterval / 1000;

            console.log('[高级设置] 当前配置:', {
                polling_interval: `${pollingInterval}ms (${pollingIntervalSeconds}s)`,
                websocket_interval: `${websocketInterval}ms (${websocketIntervalSeconds}s)`,
                data_retention: {
                    archive_hours: dataRetentionArchiveHours,
                    minute_days: dataRetentionMinuteDays,
                    hour_days: dataRetentionHourDays
                }
            });

            // 渲染高级设置页面
            res.render('admin/advanced-settings', {
                title: '高级设置',
                setting: {
                    ...svr.locals.setting,  // 包含完整的设置（包括 site.name）
                    polling_interval: pollingInterval,
                    websocket_interval: websocketInterval,
                    data_retention_archive_hours: dataRetentionArchiveHours,
                    data_retention_minute_days: dataRetentionMinuteDays,
                    data_retention_hour_days: dataRetentionHourDays,
                    postgresql_config: postgresConfig,
                    postgresql_status: postgresStatus
                },
                admin: req.admin
            });
        } catch (error) {
            console.error('[高级设置] 加载页面失败:', error);
            res.status(500).send('加载高级设置页面失败');
        }
    });

    // POST /admin/advanced-settings/update - 更新高级设置
    router.post('/admin/advanced-settings/update', requireAdmin, async (req, res) => {
        try {
            console.log('[高级设置] 收到更新请求:', req.body);

            const { polling_interval, websocket_interval } = req.body;

            // 处理前端发送的数据
            // 前端可能发送毫秒值（隐藏字段）或秒值（输入字段）
            let pollingIntervalMs, websocketIntervalMs;
            
            // 优先使用毫秒值（如果存在）
            if (polling_interval && !isNaN(parseFloat(polling_interval))) {
                pollingIntervalMs = parseInt(polling_interval);
            } else if (req.body.polling_interval_seconds && !isNaN(parseFloat(req.body.polling_interval_seconds))) {
                // 否则从秒值转换
                pollingIntervalMs = Math.round(parseFloat(req.body.polling_interval_seconds) * 1000);
            } else {
                return res.json(pr(0, '无效的轮询间隔输入'));
            }

            if (websocket_interval && !isNaN(parseFloat(websocket_interval))) {
                websocketIntervalMs = parseInt(websocket_interval);
            } else if (req.body.websocket_interval_seconds && !isNaN(parseFloat(req.body.websocket_interval_seconds))) {
                websocketIntervalMs = Math.round(parseFloat(req.body.websocket_interval_seconds) * 1000);
            } else {
                return res.json(pr(0, '无效的WebSocket间隔输入'));
            }

            // 验证范围（1500ms到60000ms）
            if (pollingIntervalMs < 1500 || pollingIntervalMs > 60000) {
                return res.json(pr(0, '轮询间隔必须在1.5到60秒之间'));
            }

            if (websocketIntervalMs < 1500 || websocketIntervalMs > 60000) {
                return res.json(pr(0, 'WebSocket间隔必须在1.5到60秒之间'));
            }

            console.log('[高级设置] 保存新配置:', {
                polling_interval: `${pollingIntervalMs}ms`,
                websocket_interval: `${websocketIntervalMs}ms`
            });

            // 保存到数据库
            await db.setting.set('polling_interval', pollingIntervalMs);
            await db.setting.set('websocket_interval', websocketIntervalMs);

            // 调用更新函数使配置立即生效
            if (svr.locals.updatePollingInterval) {
                svr.locals.updatePollingInterval(pollingIntervalMs);
                console.log('[高级设置] 轮询间隔已动态更新');
            } else {
                console.warn('[高级设置] 警告：updatePollingInterval 函数未找到，轮询间隔需要重启才能生效');
            }

            if (svr.locals.updateWebSocketInterval) {
                svr.locals.updateWebSocketInterval(websocketIntervalMs);
                console.log('[高级设置] WebSocket间隔已动态更新');
            } else {
                console.warn('[高级设置] 警告：updateWebSocketInterval 函数未找到，WebSocket间隔需要重启才能生效');
            }


            res.json(pr(1, {
                message: '高级设置已更新并立即生效',
                polling_interval: pollingIntervalMs,
                websocket_interval: websocketIntervalMs
            }));

        } catch (error) {
            console.error('[高级设置] 更新设置失败:', error);
            res.json(pr(0, '更新设置失败: ' + error.message));
        }
    });

    // POST /admin/postgresql-config - 保存PostgreSQL配置
    router.post('/admin/postgresql-config', requireAdmin, async (req, res) => {
        try {
            console.log('[PostgreSQL配置] 收到配置请求:', req.body);
            
            const { enabled, host, port, database, username, password } = req.body;
            
            if (enabled && (!host || !port || !database || !username || !password)) {
                return res.json(pr(0, '启用PostgreSQL时所有字段都是必填的'));
            }
            
            if (enabled) {
                // 验证连接参数
                try {
                    console.log('[PostgreSQL配置] 测试连接...');
                    const connectionString = `postgresql://${username}:${password}@${host}:${port}/${database}`;
                    const testConfig = { connection: connectionString };
                    const PostgresAdapter = require('../../database/adapters/postgresql');
                    const testAdapter = new PostgresAdapter(testConfig);
                    
                    await testAdapter.connect();
                    await testAdapter.disconnect();
                    console.log('[PostgreSQL配置] 连接测试成功');
                } catch (testError) {
                    console.error('[PostgreSQL配置] 连接测试失败:', testError.message);
                    return res.json(pr(0, `PostgreSQL连接测试失败: ${testError.message}`));
                }
                
                // 保存配置到专用表
                console.log('[PostgreSQL配置] 开始保存配置到专用表...');
                await db.postgresConfig.set({
                    enabled: true,
                    host: host,
                    port: parseInt(port),
                    database: database,
                    username: username,
                    password: password
                });
                
                console.log('[PostgreSQL配置] 配置已保存到专用表');
            } else {
                // 禁用PostgreSQL
                await db.postgresConfig.set({
                    enabled: false
                });
                console.log('[PostgreSQL配置] PostgreSQL已禁用');
            }
            
            console.log('[PostgreSQL配置] 准备发送成功响应...');
            
            // 配置保存成功，不自动触发迁移
            res.json(pr(1, {
                message: '配置已保存，重启后生效',
                needRestart: true,
                config: { enabled, host, port, database, username }
            }));
            console.log('[PostgreSQL配置] 成功响应已发送');
            
        } catch (error) {
            console.error('[PostgreSQL配置] 保存配置失败:', error);
            res.json(pr(0, '保存配置失败: ' + error.message));
        }
    });
    
    // GET /admin/postgresql-status - 获取PostgreSQL状态
    router.get('/admin/postgresql-status', requireAdmin, async (req, res) => {
        try {
            const config = await db.postgresConfig.get();
            const status = db.adapter.getStatus ? db.adapter.getStatus() : { type: 'unknown' };
            
            // 隐藏密码信息
            if (config.password) {
                config.password = '***';
            }
            
            // 获取迁移状态
            const migrationStatus = await db.setting.get('migration_status') || 'idle';
            const migrationMessage = await db.setting.get('migration_message') || '';
            
            res.json(pr(1, {
                config: config,
                status: status,
                migration_status: migrationStatus,
                migration_message: migrationMessage,
                timestamp: new Date().toISOString()
            }));
        } catch (error) {
            console.error('[PostgreSQL状态] 获取状态失败:', error);
            res.json(pr(0, '获取状态失败: ' + error.message));
        }
    });

    // POST /admin/migrate-to-postgresql - 手动触发数据迁移
    router.post('/admin/migrate-to-postgresql', requireAdmin, async (req, res) => {
        try {
            console.log('[手动迁移] 用户触发数据迁移...');
            
            // 获取当前PostgreSQL配置
            const pgConfig = await db.postgresConfig.get();
            if (!pgConfig || !pgConfig.enabled) {
                return res.json(pr(0, 'PostgreSQL未配置或未启用，无法迁移'));
            }
            
            // 验证配置完整性
            const { host, port, database, username, password } = pgConfig;
            if (!host || !port || !database || !username || !password) {
                return res.json(pr(0, 'PostgreSQL配置不完整，请先完善配置'));
            }
            
            // 启动迁移
            const AutoMigrationManager = require('../../database/migration/auto-migration-manager');
            const migrationManager = new AutoMigrationManager(db);
            
            // 异步启动迁移任务
            migrationManager.triggerAutoMigration({
                host, port: parseInt(port), database, username, password, enabled: true
            }).catch(error => {
                console.error('[手动迁移] 迁移失败:', error.message);
            });
            
            res.json(pr(1, {
                message: '数据迁移已启动，请查看进度条...',
                migrationStarted: true,
                timestamp: new Date().toISOString()
            }));
            
        } catch (error) {
            console.error('[手动迁移] 启动迁移失败:', error);
            res.json(pr(0, '启动迁移失败: ' + error.message));
        }
    });

    // GET /admin/migration-progress - 获取迁移进度日志
    router.get('/admin/migration-progress', requireAdmin, async (req, res) => {
        try {
            const fs = require('fs');
            const path = require('path');
            
            const progressLogPath = path.join(process.cwd(), 'logs', 'migration-progress.log');
            
            if (!fs.existsSync(progressLogPath)) {
                return res.json(pr(1, {
                    progress: 0,
                    message: '迁移尚未开始',
                    details: '',
                    timestamp: new Date().toISOString(),
                    debug: `日志文件不存在: ${progressLogPath}`
                }));
            }
            
            // 读取最后几行日志
            const logContent = fs.readFileSync(progressLogPath, 'utf8');
            const lines = logContent.trim().split('\n').filter(line => line.trim());
            
            if (lines.length === 0) {
                return res.json(pr(1, {
                    progress: 0,
                    message: '迁移尚未开始',
                    details: '',
                    timestamp: new Date().toISOString(),
                    debug: '日志文件为空'
                }));
            }
            
            // 解析最后一行日志
            const lastLine = lines[lines.length - 1];
            const progressMatch = lastLine.match(/\[([^\]]+)\] PROGRESS: (\d+)% - (.+?)(\s*\(([^)]+)\))?$/);
            
            if (progressMatch) {
                const [, timestamp, progress, message, , details] = progressMatch;
                res.json(pr(1, {
                    progress: parseInt(progress),
                    message: message.trim(),
                    details: details || '',
                    timestamp: timestamp,
                    allLogs: lines.slice(-10) // 返回最后10行日志供调试
                }));
            } else {
                res.json(pr(1, {
                    progress: 0,
                    message: '无法解析进度信息',
                    details: lastLine,
                    timestamp: new Date().toISOString(),
                    debug: {
                        lastLine: lastLine,
                        totalLines: lines.length,
                        allLines: lines.slice(-5) // 最后5行用于调试
                    }
                }));
            }
            
        } catch (error) {
            console.error('[迁移进度] 获取进度失败:', error);
            res.json(pr(0, '获取进度失败: ' + error.message));
        }
    });

    // 注册路由
    svr.use(router);

    console.log('[高级设置] 模块已加载');
};