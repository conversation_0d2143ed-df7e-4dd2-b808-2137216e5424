"use strict";
const fetch=require("node-fetch"),
    schedule=require("node-schedule"),
    { IPLocationService } = require('./iplocation');
const createNotificationManager = require('../notification');
const MonthlyTraffic = require('./monthly-traffic');
const { isNodeCooling, updateNodeCoolingStatus } = require('../api/cooling_logic');
const TimeFunctionUtils = require('../../database/adapters/time-function-utils');
const { batchExecute } = require('../../utils/concurrency');
const { logger } = require('../utils/logger');

// 创建IP地理位置服务实例
const ipLocationService = new IPLocationService();

// 紧急止血：数据库查询监控
let queryCount = 0;
let lastQueryCountReset = Date.now();

// 监控查询频率（每分钟报告一次）
setInterval(() => {
    const elapsed = Date.now() - lastQueryCountReset;
    const qps = (queryCount / elapsed) * 1000;
    if (queryCount > 0) {
        logger.info(`[DB Monitor] 查询频率: ${qps.toFixed(2)} QPS, 总查询数: ${queryCount}`);
    }
    queryCount = 0;
    lastQueryCountReset = Date.now();
}, 60000);

// 流量数据缓存管理器
const trafficCache = new Map();
const TRAFFIC_CACHE_TTL = 60000; // 1分钟缓存

// 全局统计数据缓存
const statsDataCache = {
    admin: { data: null, timestamp: 0 },
    normal: { data: null, timestamp: 0 }
};
const STATS_CACHE_TTL = 2000; // 2秒缓存，减少数据库查询压力

// 将IP位置服务添加到svr.locals中，便于其他模块使用

/**
 * 获取缓存的流量统计数据
 * @param {string} sid - 服务器ID
 * @param {Object} nodeData - 节点数据
 * @returns {Object} 流量统计结果
 */
function getCachedTrafficStats(sid, nodeData) {
    // 构建缓存键，包含影响计算结果的关键参数
    const cacheKey = `${sid}_${nodeData.traffic_reset_day}_${nodeData.traffic_limit}_${nodeData.traffic_calibration_value}_${nodeData.traffic_direction || 'both'}`;
    const cached = trafficCache.get(cacheKey);
    
    // 检查缓存是否存在且未过期
    if (cached && (Date.now() - cached.timestamp) < TRAFFIC_CACHE_TTL) {
        return cached.data;
    }
    
    // 重新计算流量统计
    const trafficStats = MonthlyTraffic.calculateMonthlyUsage({
        ds: nodeData.ds || [], // 31天流量数据
        traffic_limit: nodeData.traffic_limit || 0,
        traffic_reset_day: nodeData.traffic_reset_day || 1,
        calibration_date: nodeData.traffic_calibration_date || 0,
        calibration_value: nodeData.traffic_calibration_value || 0,
        traffic_direction: nodeData.traffic_direction || 'both'
    });
    
    // 缓存计算结果
    trafficCache.set(cacheKey, {
        data: trafficStats,
        timestamp: Date.now()
    });
    
    // 定期清理过期缓存（保持内存使用合理）
    if (trafficCache.size > 1000) {
        const now = Date.now();
        for (const [key, value] of trafficCache.entries()) {
            if (now - value.timestamp > TRAFFIC_CACHE_TTL) {
                trafficCache.delete(key);
            }
        }
    }
    
    return trafficStats;
}

function sleep(ms){return new Promise(resolve=>setTimeout(()=>resolve(),ms));};

// 调试日志函数
function debugLog(message, obj) {
    // 只有在调试模式下才输出日志
    if (global.debugMode) {
        if (obj) {
            logger.debug(`[调试] ${message}`, obj);
        } else {
            logger.debug(`[调试] ${message}`);
        }
    }
}
module.exports=async(svr)=>{
// 将IP位置服务添加到svr.locals中
svr.locals.ipLocationService = ipLocationService;

const {db,pr,bot,setting}=svr.locals;
var stats={},fails={},highcpu={},highDown={},updating=new Set(),noticed={};

// 记录服务器状态
const serverStatusCache = {};

// 服务器配置缓存 - 避免重复查询数据库
const serverConfigCache = new Map();

/**
 * 更新服务器配置缓存
 * @param {string} sid - 服务器ID
 * @param {Object} serverData - 服务器数据，如果为null则删除缓存
 */
function updateServerConfigCache(sid, serverData = null) {
    if (serverData === null) {
        // 删除服务器缓存
        serverConfigCache.delete(sid);
        logger.info(`[缓存管理] 删除服务器配置缓存: ${sid}`);
    } else {
        // 更新服务器缓存
        serverConfigCache.set(sid, {
            name: serverData.name,
            status: serverData.status,
            expire_time: serverData.expire_time,
            group_id: serverData.group_id,
            top: serverData.top,
            traffic_limit: serverData.traffic_limit || 0,
            traffic_reset_day: serverData.traffic_reset_day || 1,
            traffic_calibration_date: serverData.traffic_calibration_date || 0,
            traffic_calibration_value: serverData.traffic_calibration_value || 0,
            last_online: serverData.last_online || 0,
            data: serverData.data
        });
        logger.info(`[缓存管理] 更新服务器配置缓存: ${serverData.name} (${sid})`);
    }
}

/**
 * 重新加载所有服务器配置缓存
 */
async function reloadServerConfigCache() {
    logger.info('[缓存管理] 重新加载所有服务器配置缓存');
    serverConfigCache.clear();
    
    const servers = await db.getServers();
    for (let server of servers) {
        updateServerConfigCache(server.sid, server);
    }
    
    logger.info(`[缓存管理] 配置缓存重新加载完成，共 ${serverConfigCache.size} 个服务器`);
}
// 记录系统启动时间，用于特殊处理启动初始阶段
const SYSTEM_START_TIME = Date.now();
// 系统初始化阶段标志（启动后30秒内视为初始化阶段）
const INITIALIZATION_PERIOD = 30 * 1000;
// 在初始化阶段，记录服务器的初始状态，但不发送通知
let initialStatusCollectionComplete = false;
// 存储初始化定时器的引用，便于在需要时清除
let initializationTimer = null;

// 初始化服务器的状态（包括主动模式和被动模式）
async function initializeServersState() {
    let passiveOfflineCount = 0;
    let activeOnlineCount = 0;
    
    // 清空并重新加载服务器配置缓存
    serverConfigCache.clear();
    
    for(let server of await db.getServers()) {
        // 将服务器配置存入缓存
        serverConfigCache.set(server.sid, {
            name: server.name,
            status: server.status,
            expire_time: server.expire_time,
            group_id: server.group_id,
            top: server.top,
            traffic_limit: server.traffic_limit || 0,
            traffic_reset_day: server.traffic_reset_day || 1,
            traffic_calibration_date: server.traffic_calibration_date || 0,
            traffic_calibration_value: server.traffic_calibration_value || 0,
            last_online: server.last_online || 0,
            data: server.data
        });
        // 处理所有启用的服务器
        if(server.status > 0) {
            const sid = server.sid;
            const isActiveMode = server.data?.api?.mode === true;
            
            // 主动模式服务器
            if(isActiveMode) {
                // 如果有last_online记录，使用它作为last_report_time
                if(server.last_online) {
                    stats[sid] = {
                        name: server.name,
                        stat: false, // 初始状态设为离线，等待第一次上报
                        last_report_time: server.last_online * 1000, // 转换为毫秒
                        expire_time: server.expire_time,
                        traffic_used: 0,
                        traffic_limit: server.traffic_limit || 0,
                        traffic_reset_day: server.traffic_reset_day || 1,
                        traffic_calibration_date: server.traffic_calibration_date || 0,
                        traffic_calibration_value: server.traffic_calibration_value || 0
                    };
                    logger.info(`[状态监控] 主动模式服务器 ${server.name} 初始化完成，最后在线时间: ${new Date(server.last_online * 1000).toLocaleString()}`);
                }
            } else {
                // 被动模式服务器：如果当前无状态，先尝试检查实际状态
                if(!stats[sid]) {
                    // 不立即设置为离线，而是让定时轮询去检查状态
                    // 如果有last_online记录，记录日志但不预设离线状态
                    if(server.last_online) {
                        if (await db.setting.get('debug')) {
                            logger.info(`[状态监控] 被动模式服务器 ${server.name} 等待状态检查，最后在线时间: ${new Date(server.last_online * 1000).toLocaleString()}`);
                        }
                    }
                    // 初始化失败计数为0，允许正常检查
                    fails[sid] = 0;
                }
            }
        }
    }
    
    // 输出汇总信息
    const totalServers = (await db.getServers()).filter(s => s.status > 0).length;
    const passiveServers = (await db.getServers()).filter(s => s.status > 0 && s.data?.api?.mode !== true).length;
    logger.info(`[状态监控] 初始化完成: 共 ${totalServers} 个服务器，其中 ${passiveServers} 个被动模式服务器等待状态检查`);
}

// 在模块初始化时调用
(async () => {
    await initializeServersState();
})().catch(logger.error);

// 清除可能存在的旧定时器
if (initializationTimer) {
    clearTimeout(initializationTimer);
}

// 在初始化阶段结束后自动标记状态收集完成
initializationTimer = setTimeout(async () => {
    initialStatusCollectionComplete = true;
    try {
        if (svr.locals.ipLocationService) {
            await svr.locals.ipLocationService.checkAndUpdateMissingLocations(db);
        }
    } catch (error) {
        logger.error(`[状态监控] 检查服务器位置信息时出错:`, error);
    }
}, INITIALIZATION_PERIOD);

// 将通知管理器添加到 svr.locals 中
if (!svr.locals.notification) {
    svr.locals.notification = createNotificationManager(db);
    if (svr.locals.notification && svr.locals.bot) {
        svr.locals.notification.setBot(svr.locals.bot);
    }
}

// 使用 svr.locals 中的通知管理器
const notification = svr.locals.notification;

/**
 * 纯内存的状态数据获取（避免数据库查询）
 * @param {boolean} isAdmin - 是否为管理员
 * @returns {Object} 状态数据
 */
function getStatsFromMemory(isAdmin = false) {
    const perfStart = Date.now();
    // 移除：高频调用的性能监控日志
    
    let Stats = {};
    
    // 直接遍历内存中的服务器配置缓存，按照 top 值重新排序，保证前端展示顺序与数据库保持一致（top DESC）
    const sortedEntries = Array.from(serverConfigCache.entries()).sort(([, a], [, b]) => (b.top || 0) - (a.top || 0));
    for (let [sid, serverConfig] of sortedEntries) {
        if (serverConfig.status == 1 || (serverConfig.status == 2 && isAdmin)) {
            const serverStats = stats[sid];
            
            // 状态判断逻辑：
            // - 如果没有stats记录，返回-1（初始状态）
            // - 如果stats.stat === false，说明连接失败
            // - 如果有具体数据，说明在线
            const stat = !serverStats ? -1 :
                        serverStats.stat === false ? 0 :
                        serverStats.stat;

            // 创建返回对象
            Stats[sid] = {
                name: serverConfig.name,
                stat: stat,
                expire_time: serverConfig.expire_time,
                group_id: serverConfig.group_id,
                top: serverConfig.top,
                traffic_used: serverStats?.traffic_used || 0,
                traffic_limit: serverConfig.traffic_limit,
                traffic_reset_day: serverConfig.traffic_reset_day,
                traffic_calibration_date: serverConfig.traffic_calibration_date,
                traffic_calibration_value: serverConfig.traffic_calibration_value,
                calibration_base_traffic: serverStats?.calibration_base_traffic || null,
                last_online: serverConfig.last_online,
                data: serverConfig.data
            };

            // 如果stat是对象，确保它包含完整的mem和swap数据
            if (typeof stat === 'object' && stat !== null) {
                // 确保mem对象存在
                if (!Stats[sid].stat.mem) {
                    Stats[sid].stat.mem = {
                        virtual: { used: 0, total: 1, usedPercent: 0 },
                        swap: { used: 0, total: 1, usedPercent: 0 }
                    };
                } else if (serverStats && serverStats.stat && serverStats.stat.mem) {
                    // 确保从原始数据中复制完整的mem对象
                    Stats[sid].stat.mem = JSON.parse(JSON.stringify(serverStats.stat.mem));
                }

                // 确保swap对象存在
                if (!Stats[sid].stat.mem.swap) {
                    Stats[sid].stat.mem.swap = { used: 0, total: 1, usedPercent: 0 };
                }
            }
        }
    }
    
    // 移除：高频调用的性能监控日志
    return Stats;
}

/**
 * 统一的状态数据获取接口
 * @param {boolean} isAdmin - 是否为管理员
 * @param {boolean} shouldFilter - 是否需要过滤敏感数据
 * @returns {Object} 处理后的状态数据
 */
async function getStatsData(isAdmin = false, shouldFilter = true) {
    const perfStart = Date.now();
    
    // 检查缓存
    const cacheKey = isAdmin ? 'admin' : 'normal';
    const cached = statsDataCache[cacheKey];
    const now = Date.now();
    
    if (cached.data && (now - cached.timestamp) < STATS_CACHE_TTL) {
        logger.debug(`[性能监控] getStatsData 缓存命中 (${cacheKey}), 跳过查询`);
        return cached.data;
    }
    
    logger.debug('\n[性能监控] getStatsData 开始执行（缓存未命中）');
    
    try {
        const getStatsStart = Date.now();
        const statsData = getStatsFromMemory(isAdmin); // 使用纯内存版本，避免数据库查询
        logger.debug(`[性能监控] getStatsFromMemory 耗时: ${Date.now() - getStatsStart}ms`); // 显示纯内存耗时



        // 处理每个节点的数据
        // console.log(`[性能监控] 开始处理 ${Object.keys(statsData).length} 个节点的数据`); // 性能日志已禁用
        const nodeProcessStart = Date.now();
        
        // 使用 Promise.all 并行处理所有节点
        const nodeProcessingPromises = Object.entries(statsData).map(async ([sid, node]) => {
            try {                     // 过滤敏感数据
                    if (shouldFilter && node.data) {
                        let processedData = null;
                         // 管理员可以看到更多信息，但需要过滤敏感数据
                        if (isAdmin) {
                            processedData = {...node.data};
                             // 处理SSH信息 - 移除所有敏感凭据
                            if (processedData.ssh) {
                                const ssh = {...processedData.ssh};
                                // 移除SSH敏感信息
                                if (ssh.password) delete ssh.password;
                                if (ssh.privateKey) delete ssh.privateKey;
                                if (ssh.passphrase) delete ssh.passphrase;
                                if (ssh.pri) delete ssh.pri; // 一些旧版本可能使用pri字段
                                processedData.ssh = ssh;
                            }
                             // 处理API信息 - 部分模糊API密钥
                            if (processedData.api && processedData.api.key) {
                                const api = {...processedData.api};
                                // 只保留API密钥的前4个和后4个字符，中间用*替代
                                const key = api.key;
                                if (key.length > 8) {
                                    api.key = key.slice(0, 4) + '********' + key.slice(-4);
                                } else if (key.length > 0) {
                                    api.key = key.slice(0, 1) + '****' + (key.length > 1 ? key.slice(-1) : '');
                                }
                                processedData.api = api;
                            }
                        } else {
                            // 非管理员保留地区信息和标签信息（只读）
                            let locationCode = null;
                            if (node.data.location) {
                                locationCode = node.data.location.code ||
                                             (node.data.location.country ? node.data.location.country.code : null);
                            }
                             // 替换data对象，保留地区信息和标签
                            processedData = {
                                location: locationCode ? {
                                    code: locationCode
                                } : null,
                                tags: node.data.tags || []  // 保留标签供游客查看
                            };
                        }
                         // 更新node.data
                        node.data = processedData;
                    }
                     // 处理stat对象
                    if (typeof node.stat === 'number' || !node.stat) {
                        // 如果stat是数字或不存在，转换为标准对象结构
                        const isOffline = !node.stat || node.stat <= 0;
                        statsData[sid] = {
                            ...node,
                            stat: {
                                cpu: { multi: 0 },
                                mem: {
                                    virtual: {
                                        used: 0,
                                        total: 1,
                                        usedPercent: 0
                                    }
                                },
                                net: {
                                    delta: { in: 0, out: 0 },
                                    total: { in: 0, out: 0 }
                                },
                                offline: isOffline
                            }
                        };
                    } else if (typeof node.stat === 'object') {
                        // 确保对象结构完整性并处理无效值
                        const cpuMulti = Number(node.stat.cpu?.multi) || 0;
                        const memUsed = Number(node.stat.mem?.virtual?.used) || 0;
                        const memTotal = Number(node.stat.mem?.virtual?.total) || 1;
                        const memPercent = Number(node.stat.mem?.virtual?.usedPercent) || (memTotal > 0 ? (memUsed / memTotal * 100) : 0);
                         // 处理swap数据
                        const swapData = node.stat.mem?.swap || { used: 0, total: 1, usedPercent: 0 };
                         statsData[sid] = {
                            ...node,
                            stat: {
                                ...node.stat,
                                cpu: {
                                    multi: cpuMulti >= 0 ? cpuMulti : 0,
                                    single: Array.isArray(node.stat.cpu?.single) ?
                                           node.stat.cpu.single.map(v => Number(v) >= 0 ? Number(v) : 0) :
                                           [0]
                                },
                                mem: {
                                    virtual: {
                                        used: memUsed,
                                        total: memTotal,
                                        usedPercent: memPercent >= 0 ? memPercent : 0
                                    },
                                    // 确保包含swap数据
                                    swap: swapData
                                },
                                net: {
                                    delta: {
                                        in: Math.max(0, Number(node.stat.net?.delta?.in) || 0),
                                        out: Math.max(0, Number(node.stat.net?.delta?.out) || 0)
                                    },
                                    total: {
                                        in: Math.max(0, Number(node.stat.net?.total?.in) || 0),
                                        out: Math.max(0, Number(node.stat.net?.total?.out) || 0)
                                    }
                                }
                            }
                        };
                      }
                     // 添加延迟和丢包率数据
                    // [性能优化] 延迟查询已移除，改为异步加载
                    /* 原延迟查询代码已注释，节省13秒加载时间
                    if (sid && db && db.monitor) {
                        // ... 延迟查询代码 ...
                    }
                    */
                    // 添加流量统计预计算
                    const trafficStart = Date.now();
                    const server = await db.servers.get(sid);
                    if (server) {
                        // 获取traffic表中的数据
                        const trafficData = await db.traffic.get(sid);
                        
                        if (server.traffic_limit > 0 && trafficData) {
                            // 预计算流量统计，使用缓存优化
                            const trafficStatsData = {
                                ds: trafficData.ds || [],
                                traffic_limit: server.traffic_limit,
                                traffic_reset_day: server.traffic_reset_day || 1,
                                traffic_calibration_date: server.traffic_calibration_date || 0,
                                traffic_calibration_value: server.traffic_calibration_value || 0,
                                traffic_direction: server.traffic_direction || 'both'
                            };
                            
                            statsData[sid].traffic_stats = getCachedTrafficStats(sid, trafficStatsData);
                        } else {
                            // 无限制流量或无数据
                            statsData[sid].traffic_stats = { 
                                unlimited: true,
                                used: 0,
                                remaining: -1,
                                limit: 0,
                                ratio: 0
                            };
                        }
                        // console.log(`[性能监控] 节点 ${sid} 流量处理耗时: ${Date.now() - trafficStart}ms`); // 性能日志已禁用
                    }
                
            } catch (error) {
                logger.error(`[性能监控] 处理节点 ${sid} 时出错:`, error);
                // 发生错误时，保留原始节点数据
                statsData[sid] = node;
            }
        });
        
        // 等待所有节点处理完成
        await Promise.all(nodeProcessingPromises);
        // console.log(`[性能监控] 节点数据处理总耗时: ${Date.now() - nodeProcessStart}ms`); // 性能日志已禁用
        // console.log(`[性能监控] getStatsData 总耗时: ${Date.now() - perfStart}ms\n`); // 性能日志已禁用
        
        // 更新缓存
        statsDataCache[cacheKey] = { 
            data: statsData, 
            timestamp: Date.now() 
        };
        logger.debug(`[性能监控] 已更新 ${cacheKey} 缓存`);
        
        return statsData;
    } catch (error) {
        logger.error('获取状态数据失败:', error);
        // console.log(`[性能监控] getStatsData 失败，耗时: ${Date.now() - perfStart}ms\n`); // 性能日志已禁用
        return {};
    }
}

async function getStats(isAdmin=false){
    const perfStart = Date.now();
    console.log('[性能监控] getStats 开始执行');
    
    queryCount++; // 添加查询计数
    let Stats = {};
    
    const serversStart = Date.now();
    const servers = await db.getServers();
    console.log(`[性能监控] db.getServers 耗时: ${Date.now() - serversStart}ms, 服务器数量: ${servers.length}`);
    
    for(let server of servers) {
        if(server.status == 1 || (server.status == 2 && isAdmin)){
            const serverStats = stats[server.sid];
            // 状态判断逻辑：
            // - 如果没有stats记录，返回-1（初始状态）
            // - 如果stats.stat === false，说明连接失败
            // - 如果有具体数据，说明在线
            const stat = !serverStats ? -1 :
                        serverStats.stat === false ? 0 :
                        serverStats.stat;

            // 创建返回对象
            Stats[server.sid] = {
                name: server.name,
                stat: stat,
                expire_time: server.expire_time,
                group_id: server.group_id,
                top: server.top,
                traffic_used: serverStats?.traffic_used || 0,
                traffic_limit: server.traffic_limit || 0,
                traffic_reset_day: server.traffic_reset_day || 1,
                traffic_calibration_date: server.traffic_calibration_date || 0,
                traffic_calibration_value: server.traffic_calibration_value || 0,
                calibration_base_traffic: serverStats?.calibration_base_traffic || null,
                last_online: server.last_online || 0, // 添加最后在线时间
                data: server.data
            };

            // 如果stat是对象，确保它包含完整的mem和swap数据
            if (typeof stat === 'object' && stat !== null) {
                // 确保mem对象存在
                if (!Stats[server.sid].stat.mem) {
                    Stats[server.sid].stat.mem = {
                        virtual: { used: 0, total: 1, usedPercent: 0 },
                        swap: { used: 0, total: 1, usedPercent: 0 }
                    };
                } else if (serverStats && serverStats.stat && serverStats.stat.mem) {
                    // 确保从原始数据中复制完整的mem对象
                    Stats[server.sid].stat.mem = JSON.parse(JSON.stringify(serverStats.stat.mem));
                }

                // 确保swap对象存在
                if (!Stats[server.sid].stat.mem.swap) {
                    Stats[server.sid].stat.mem.swap = { used: 0, total: 1, usedPercent: 0 };
                } else if (serverStats && serverStats.stat && serverStats.stat.mem && serverStats.stat.mem.swap) {
                    // 确保从原始数据中复制完整的swap对象
                    Stats[server.sid].stat.mem.swap = JSON.parse(JSON.stringify(serverStats.stat.mem.swap));
                }
            }


        }
    }
    
    console.log(`[性能监控] getStats 总耗时: ${Date.now() - perfStart}ms`);
    return Stats;
}

/**
 * 获取全局stats对象，用于其他模块访问
 * @returns {Object} 全局stats对象
 */
function getGlobalStats() {
    return stats;
}

// 将stats相关函数添加到svr.locals中
svr.locals.stats = { 
    getStats, 
    getStatsData, 
    getStatsFromMemory,  // 新增纯内存版本
    ipLocationService, 
    getGlobalStats,
    // 缓存管理函数
    updateServerConfigCache,
    reloadServerConfigCache
};

// 清理函数，用于优雅关闭
function cleanup() {
    if (pollingTimer) {
        clearInterval(pollingTimer);
        pollingTimer = null;
        logger.info('[监控] 已停止状态轮询定时器');
    }
    if (archiveTimer) {
        clearInterval(archiveTimer);
        archiveTimer = null;
        logger.info('[监控] 已停止归档定时器');
    }
    
    // 取消所有scheduled jobs
    const jobs = schedule.scheduledJobs;
    for (const jobName in jobs) {
        jobs[jobName].cancel();
    }
    logger.info('[监控] 已取消所有计划任务');
}

// 导出函数供其他模块使用
module.exports.getGlobalStats = getGlobalStats;
module.exports.cleanup = cleanup;

// 更新路由处理
svr.get("/", async (req,res)=>{
    try {
        const theme = req.query.theme || await db.setting.get("theme") || "card";
        const isAdmin = req.admin;

        res.render(`stats/${theme}` ,{
            stats: await getStatsData(isAdmin),
            groups: await db.groups.getWithCount(),
            theme,
            admin: isAdmin,
            isAdminPage: false  // 标记这不是管理后台页面
        });
    } catch (error) {
        logger.error('首页渲染错误:', error);
        res.status(500).send('服务器内部错误');
    }
});

svr.get("/stats/data", async (req,res)=>{
    try {
        const isAdmin = req.admin;
        res.json(await getStatsData(isAdmin));
    } catch (error) {
        logger.error('数据API错误:', error);
        res.status(500).json({ error: '获取数据失败' });
    }
});

svr.get("/stats/:sid", async (req,res)=>{
    let {sid}=req.params;
    const statsData = await getStats(req.admin);
    const node = statsData[sid];
    if (!node) {
        return res.status(404).send('Node not found');
    }

    // 获取服务器完整信息
    const server = await db.servers.get(sid);
    if (server) {
        // 添加校准数据到node对象
        node.traffic_calibration_date = server.traffic_calibration_date || 0;
        node.traffic_calibration_value = server.traffic_calibration_value || 0;
        node.traffic_limit = server.traffic_limit || 0;
        node.traffic_reset_day = server.traffic_reset_day || 1;

        // 预处理数据，确保所有值都有默认值
        node.traffic_used = node.traffic_used || 0;
        node.traffic_limit = node.traffic_limit || 0;
        node.traffic_reset_day = node.traffic_reset_day || 1;
    }

    // 添加预处理的JSON数据
    const preProcessedData = {
        traffic_used: node.traffic_used || 0,
        traffic_limit: node.traffic_limit || 0,
        traffic_reset_day: node.traffic_reset_day || 1,
        traffic_calibration_date: node.traffic_calibration_date || 0,
        traffic_calibration_value: node.traffic_calibration_value || 0,
        calibration_base_traffic: node.calibration_base_traffic || null
    };

    res.render('stat',{
        sid,
        node,
        preProcessedData: JSON.stringify(preProcessedData),
        traffic: await db.traffic.get(sid),
        load_m: await db.load_m.selectRaw(sid),  // 使用selectRaw，不填充
        load_h: await db.load_h.selectRaw(sid),  // 使用selectRaw，不填充
        admin: req.admin,
        isAdminPage: false,  // 标记这不是管理后台页面
        setting: svr.locals.setting // 传递最新的设置对象，包含调试模式状态
    });
});
svr.get("/stats/:sid/data",(req,res)=>{
    let {sid}=req.params;
    res.json({sid,...stats[sid]});
});

// 流量统计API
svr.get("/stats/:sid/traffic", async (req, res) => {
    const { sid } = req.params;
    const server = await db.servers.get(sid);

    if (!server) {
        return res.json({
            error: '服务器不存在',
            data: null
        });
    }

    try {
        // 获取traffic表中的完整数据
        const trafficData = await db.traffic.get(sid);

        // 计算月度流量使用情况
        const monthlyTraffic = MonthlyTraffic.calculateMonthlyUsage({
            ds: trafficData?.ds || [],
            traffic_reset_day: server.traffic_reset_day || 1,
            traffic_limit: server.traffic_limit || 0,
            calibration_date: server.traffic_calibration_date || 0,
            calibration_value: server.traffic_calibration_value || 0
        });

        res.json({
            data: {
                // 基础流量数据
                ds: trafficData?.ds || [],  // 天级流量记录数据（31天）
                hs: trafficData?.hs || [],  // 小时流量记录数据（24小时）
                ms: trafficData?.ms || [],  // 月度流量记录数据（12个月）

                // 月度流量统计结果
                monthly: monthlyTraffic
            }
        });
    } catch (error) {
        logger.error('获取流量统计失败:', error);
        res.status(500).json({
            error: '获取流量统计失败',
            message: error.message
        });
    }
});

// 批量获取流量数据的API端点
svr.post("/stats/batch-traffic", async (req, res) => {
    const { serverIds = [] } = req.body;
    
    if (!Array.isArray(serverIds) || serverIds.length === 0) {
        return res.json({
            success: false,
            message: '请提供有效的服务器ID数组',
            data: {}
        });
    }

    console.log(`[批量流量API] 接收到批量请求，服务器数量: ${serverIds.length}`);
    
    const batchData = {};
    let serversWithData = 0;
    
    try {
        // 创建并发查询任务
        const tasks = serverIds.map(sid => {
            return async () => {
                try {
                    const server = await db.servers.get(sid);
                    
                    if (!server) {
                        console.warn(`[批量流量API] 服务器 ${sid} 不存在（可能是浏览器缓存过期）`);
                        batchData[sid] = null;
                        return;
                    }

                    // 获取traffic表中的完整数据
                    const trafficData = await db.traffic.get(sid);
                    
                    // 计算月度流量使用情况
                    const monthlyTraffic = MonthlyTraffic.calculateMonthlyUsage({
                        ds: trafficData?.ds || [],
                        traffic_reset_day: server.traffic_reset_day || 1,
                        traffic_limit: server.traffic_limit || 0,
                        calibration_date: server.traffic_calibration_date || 0,
                        calibration_value: server.traffic_calibration_value || 0,
                        traffic_direction: server.traffic_direction || 'both'
                    });

                    batchData[sid] = {
                        // 基础流量数据（仅返回月度统计，减少数据传输量）
                        monthly: monthlyTraffic,
                        // 可选：如果需要详细数据，可以添加参数控制
                        // ds: trafficData?.ds || [],  // 天级流量记录数据（31天）
                        // hs: trafficData?.hs || [],  // 小时流量记录数据（24小时）
                        // ms: trafficData?.ms || [],  // 月度流量记录数据（12个月）
                    };
                    
                    if (monthlyTraffic.limit > 0 || monthlyTraffic.used > 0) {
                        serversWithData++;
                    }
                    
                } catch (error) {
                    logger.error(`[批量流量API] 处理服务器 ${sid} 失败:`, error);
                    batchData[sid] = null;
                }
            };
        });
        
        // 使用并发控制执行批量查询（限制为20个并发）
        console.log(`[批量流量API] 开始并发查询，并发限制: 20`);
        await batchExecute(tasks, 20);

        console.log(`[批量流量API] 处理完成，有数据的服务器: ${serversWithData}/${serverIds.length}`);

        // 统计无效ID数量
        const invalidIds = serverIds.filter(sid => batchData[sid] === null);
        const response = {
            success: true,
            data: batchData,
            summary: {
                requestedServers: serverIds.length,
                serversWithData: serversWithData,
                invalidServers: invalidIds.length,
                timestamp: Date.now()
            }
        };
        
        // 如果有无效ID，添加友好提示
        if (invalidIds.length > 0) {
            response.warning = '部分服务器ID不存在，可能是浏览器缓存过期导致，请刷新页面';
            console.log(`[批量流量API] 发现${invalidIds.length}个无效服务器ID，建议用户刷新页面清理缓存`);
        }
        
        res.json(response);
        
    } catch (error) {
        logger.error('[批量流量API] 批量查询失败:', error);
        res.status(500).json({
            success: false,
            message: '批量查询失败',
            error: error.message
        });
    }
});

svr.post("/stats/update", async (req, res) => {
    let {sid, data} = req.body;
    if (svr.locals.reporting) {
        const result = await svr.locals.reporting.processReportData(sid, data, req);
        if (result.success) {
            res.json(pr(1, 'update success'));
        } else {
            res.json(pr(0, result.message));
        }
    } else {
        // 兼容旧版本的处理方式
        // 移除所有 console.log 日志，仅保留错误输出
        try {
            // 简单摘要信息
            const summary = { server_id: sid };

            // CPU信息
            if (data.cpu && typeof data.cpu === 'object') {
                summary.cpu = {
                    usage: data.cpu.multi ? `${(data.cpu.multi * 100).toFixed(2)}%` : 'unknown'
                };
            }

            // 内存信息
            if (data.mem && data.mem.virtual) {
                summary.memory = {
                    used: formatSize(data.mem.virtual.used),
                    total: formatSize(data.mem.virtual.total),
                    percent: data.mem.virtual.usedPercent ? `${data.mem.virtual.usedPercent.toFixed(2)}%` : 'unknown'
                };
            }

            // 网络信息
            if (data.net && data.net.delta) {
                summary.network = {
                    in: formatSize(data.net.delta.in) + '/s',
                    out: formatSize(data.net.delta.out) + '/s'
                };
            }

            console.log(`[状态模块] 数据摘要:`, JSON.stringify(summary, null, 2));
        } catch (error) {
            logger.error(`[状态模块] 打印数据摘要时出错:`, error);
        }
        // 检查是否为主动模式服务器
        const server = await db.servers.get(sid);
        const isActiveMode = server && server.data?.api?.mode === true;
        
        // 保存主动模式服务器的最后上报时间
        if (isActiveMode) {
            stats[sid] = {
                ...data,
                last_report_time: Date.now(),
                lastValidStat: data // 保存有效的状态数据，用于恢复
            };
        } else {
            stats[sid] = data;
        }
        
        // 格式化文件大小的辅助函数
        function formatSize(bytes) {
            if (bytes === 0) return '0 B';
            if (!bytes || isNaN(bytes)) return 'unknown';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 如果有事件发射器，发出数据上报事件
        if (svr.locals.eventEmitter) {
            // 异步获取服务器数据，但不阻塞响应
            (async () => {
                try {
                    const server = await db.servers.get(sid);
                    svr.locals.eventEmitter.emit('server:stats:update', {
                        sid,
                        data,
                        server,
                        request: req
                    });
                } catch (error) {
                    console.error('[Stats] 发送服务器更新事件时获取服务器数据失败:', error);
                }
            })();
        }

        res.json(pr(1, 'update success'));
    }
});

async function getStat(server){
    let res;
    const debugMode = await db.setting.get('debug') || false;
    
    // 数据字段验证
    if (!server || !server.data) {
        if (debugMode) {
            console.error(`[被动模式] 服务器数据缺失: ${server?.name || 'unknown'}`);
        }
        return false;
    }
    
    if (!server.data.ssh || !server.data.ssh.host) {
        if (debugMode) {
            console.error(`[被动模式] 服务器主机地址缺失: ${server.name} (${server.sid})`);
        }
        return false;
    }
    
    if (!server.data.api || !server.data.api.port) {
        if (debugMode) {
            console.error(`[被动模式] 服务器API端口缺失: ${server.name} (${server.sid})`);
        }
        return false;
    }
    
    if (!server.data.api.key) {
        if (debugMode) {
            console.error(`[被动模式] 服务器API密钥缺失: ${server.name} (${server.sid})`);
        }
        return false;
    }
    
    // 处理 IPv6 地址格式 - IPv6 地址需要用方括号包围
    const host = server.data.ssh.host;
    const formattedHost = host.includes(':') && !host.startsWith('[') ? `[${host}]` : host;
    const apiUrl = `http://${formattedHost}:${server.data.api.port}/stat`;
    
    try{
        if (debugMode) {
            logger.debug(`[被动模式] 正在检查服务器状态: ${server.name} -> ${apiUrl}`);
        }
        
        res=await fetch(apiUrl,{
            method:"GET",
            headers:{key:server.data.api.key},
            timeout:2000, // 优化：2秒超时，保证1.5秒轮询的实时性
        }).then(res=>res.json());

        if (debugMode) {
            logger.debug(`[被动模式] API响应成功: ${server.name}, success: ${res.success}`);
        }

    }catch(e){
        // 只在首次失败或调试模式下记录错误
        if (!fails[server.sid] || fails[server.sid] === 0 || debugMode) {
            console.error(`[被动模式] API调用失败: ${server.name} (${server.data.ssh.host}:${server.data.api.port}), 错误: ${e.message}`);
            if (debugMode) {
                console.error(`[被动模式] 详细错误信息: ${server.name}`, e);
            }
        }
        res={success:false,msg:e.message || 'timeout'};
    }
    
    if(res.success) {
        if (debugMode) {
            logger.debug(`[被动模式] 服务器在线: ${server.name}`);
        }
        return res.data;
    } else {
        // 只在首次失败或调试模式下记录警告
        if (!fails[server.sid] || fails[server.sid] === 0 || debugMode) {
            logger.warn(`[被动模式] 服务器离线或响应失败: ${server.name}, 消息: ${res.msg || 'unknown error'}`);
        }
        return false;
    }
}
async function update(server){
    let {sid}=server;

    // 如果是首次更新，初始化服务器状态缓存
    if (serverStatusCache[sid] === undefined) {
        if (stats[sid] && stats[sid].stat !== false) {
            serverStatusCache[sid] = true;
        } else {
            serverStatusCache[sid] = null;
            const hasLocation = server.data && server.data.location && server.data.location.code && server.data.location.code !== '--';
            if (!hasLocation && svr.locals.ipLocationService) {
                setTimeout(async () => {
                    try {
                        await svr.locals.ipLocationService.updateServerLocation(server, db);
                    } catch (error) {
                        console.error(`[状态监控] 异步获取服务器位置信息失败: ${server.name}`, error);
                    }
                }, 100);
            }
        }
    }

    // 如果服务器状态为禁用，删除状态并返回
    if(server.status<=0){
        delete stats[sid];
        serverStatusCache[sid] = null;
        return;
    }

    // 检查是否为主动模式服务器
    const isActiveMode = server.data?.api?.mode === true;

    // 如果是主动模式服务器，跳过常规离线检测
    // 主动模式服务器的离线检测由定时任务处理
    if (isActiveMode) {
        return;
    }

    // 被动模式服务器的冷却检查
    if (!isActiveMode && isNodeCooling(sid)) {
        // 服务器处于冷却状态，跳过探测
        return;
    }

    let stat=await getStat(server);
    
    // 获取调试模式设置
    const debugMode = await db.setting.get('debug') || false;
    
    // 更新冷却状态（成功为true，失败为false）
    updateNodeCoolingStatus(sid, !!stat, debugMode);
    if(stat){
        let notice = false;
        // 检查是否需要发送上线通知：之前状态为离线或初始状态，现在状态为在线
        if(!stats[sid] || stats[sid].stat === false) notice = true;

        // 1. 确保基础网络数据结构完整
        if (!stat.net || typeof stat.net !== 'object') {
            stat.net = {
                delta: { in: 0, out: 0 },
                total: { in: 0, out: 0 },
                devices: {}
            };
        }

        // 2. 处理网络设备数据
        let deviceData = null;
        if (stat.net.devices && server.data.device) {
            deviceData = stat.net.devices[server.data.device];
            if (deviceData) {
                // 深拷贝设备数据，避免引用问题
                deviceData = {
                    total: {
                        in: Number(deviceData.total?.in || 0),
                        out: Number(deviceData.total?.out || 0)
                    },
                    delta: {
                        in: Number(deviceData.delta?.in || 0),
                        out: Number(deviceData.delta?.out || 0)
                    }
                };
            }
        }

        // 3. 构建标准化的网络数据结构
        const networkData = {
            delta: {
                in: deviceData ? deviceData.delta.in : Number(stat.net.delta?.in || 0),
                out: deviceData ? deviceData.delta.out : Number(stat.net.delta?.out || 0)
            },
            total: {
                in: deviceData ? deviceData.total.in : Number(stat.net.total?.in || 0),
                out: deviceData ? deviceData.total.out : Number(stat.net.total?.out || 0)
            },
            // 保留原始devices数据，确保前端可以访问网络设备信息
            devices: stat.net.devices || {}
        };

        // 确保内存和SWAP数据结构完整
        if (!stat.mem || typeof stat.mem !== 'object') {
            stat.mem = {
                virtual: { used: 0, total: 1, usedPercent: 0 },
                swap: { used: 0, total: 1, usedPercent: 0 }
            };
        } else {
            // 确保virtual数据结构完整
            if (!stat.mem.virtual || typeof stat.mem.virtual !== 'object') {
                stat.mem.virtual = { used: 0, total: 1, usedPercent: 0 };
            } else {
                // 确保virtual属性有默认值
                stat.mem.virtual.used = stat.mem.virtual.used !== undefined ? stat.mem.virtual.used : 0;
                stat.mem.virtual.total = stat.mem.virtual.total !== undefined ? stat.mem.virtual.total : 1;

                // 如果有used和total但没有usedPercent，计算usedPercent
                if (stat.mem.virtual.usedPercent === undefined) {
                    stat.mem.virtual.usedPercent = stat.mem.virtual.total > 0 ?
                        (stat.mem.virtual.used / stat.mem.virtual.total) * 100 : 0;
                }
            }

            // 确保swap数据结构完整
            if (!stat.mem.swap || typeof stat.mem.swap !== 'object') {
                stat.mem.swap = { used: 0, total: 1, usedPercent: 0 };
            } else {
                // 确保swap属性有默认值
                stat.mem.swap.used = stat.mem.swap.used !== undefined ? stat.mem.swap.used : 0;
                stat.mem.swap.total = stat.mem.swap.total !== undefined ? stat.mem.swap.total : 1;

                // 如果没有usedPercent，计算usedPercent
                if (stat.mem.swap.usedPercent === undefined) {
                    stat.mem.swap.usedPercent = stat.mem.swap.total > 0 ?
                        (stat.mem.swap.used / stat.mem.swap.total) * 100 : 0;
                }
            }

            // 如果swap对象是空的或者所有属性都是undefined，设置默认值
            if (stat.mem.swap && Object.values(stat.mem.swap).every(val => val === undefined)) {
                stat.mem.swap = { used: 0, total: 1, usedPercent: 0 };
            }


        }

        // 4. 更新服务器状态
        stats[sid] = {
            name: server.name,
            stat: {
                ...stat,
                net: networkData,  // 使用标准化后的网络数据
                // 确保mem对象完整
                mem: {
                    ...stat.mem,
                    // 确保swap对象完整
                    swap: stat.mem?.swap || { used: 0, total: 1, usedPercent: 0 }
                }
            },
            expire_time: server.expire_time,
            traffic_used: stats[sid]?.traffic_used || 0,
            traffic_limit: server.traffic_limit || 0,
            traffic_reset_day: server.traffic_reset_day || 1,
            traffic_calibration_date: server.traffic_calibration_date || 0,
            traffic_calibration_value: server.traffic_calibration_value || 0,
            calibration_base_traffic: stats[sid]?.calibration_base_traffic || null
        };
        fails[sid]=0;
    } else {
        let notice = false;
        fails[sid] = (fails[sid] || 0) + 1;

        // 不再记录掉线计数到日志
        // console.log(`[状态监控] 服务器 ${server.name} 掉线计数: ${fails[sid]}/5`);

        if(fails[sid] > 5) {
            if(stats[sid] && stats[sid].stat !== false) {
                notice = true;
                // 记录最后在线时间
                const now = Math.floor(Date.now() / 1000);
                await db.servers.upd_last_online(sid, now);
                console.log(`[状态监控] 记录服务器最后在线时间: ${server.name}, 时间: ${new Date(now * 1000).toLocaleString()}`);
            }
            stats[sid] = {
                name: server.name,
                stat: false,
                expire_time: server.expire_time,
                traffic_used: stats[sid]?.traffic_used || 0
            };
        }
        if(notice) {
            const notifyMessage = `#掉线 ${server.name} ${new Date().toLocaleString()}`;
            const telegramSetting = db.setting.get('telegram');

            // 判断是否需要发送通知
            const currentTime = Date.now();

            // 判断是否处于初始化阶段
            const isInitialPeriod = !initialStatusCollectionComplete;

            // 更新状态缓存前的旧状态
            const oldStatus = serverStatusCache[sid];

            // 更新状态缓存
            serverStatusCache[server.sid] = false;

            // 离线通知逻辑 - 支持长期离线节点首次检测
            // 基于 TDD 测试验证的修复方案
            const STARTUP_GRACE_PERIOD = 10 * 60 * 1000; // 10分钟启动缓冲期
            const timeSinceStartup = Date.now() - SYSTEM_START_TIME;
            const isAfterGracePeriod = timeSinceStartup > STARTUP_GRACE_PERIOD;

            const shouldNotify = !isInitialPeriod && (
                oldStatus === true ||                           // 从在线变为离线（原有逻辑）
                (oldStatus === null && isAfterGracePeriod)      // 启动10分钟后的首次离线检测（修复长期离线节点问题）
            );

            if (shouldNotify && telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0) {
                // 检查服务器下线通知是否启用
                if (telegramSetting?.notificationTypes?.serverOffline) {
                    console.log(`[状态监控] 发送服务器掉线通知(状态从在线变为离线): ${server.name}`);
                    if (notification && notification.bot) {
                        try {
                            // 添加重试机制
                            const maxRetries = 3;
                            let retryCount = 0;
                            let success = false;

                            while (!success && retryCount < maxRetries) {
                                try {
                                    // 修改：正确处理通知系统的返回值
                                    const notificationResult = await notification.sendNotification('服务器掉线', notifyMessage, telegramSetting.chatIds);

                                    // 检查通知系统返回的结果
                                    if (notificationResult.success) {
                                        success = true;
                                        console.log(`[状态监控] 服务器掉线通知发送成功: ${server.name}`);
                                    } else {
                                        // 通知系统返回失败
                                        throw new Error(notificationResult.error || '通知系统返回失败');
                                    }
                                } catch (err) {
                                    retryCount++;
                                    console.error(`[状态监控] 服务器掉线通知发送失败(尝试 ${retryCount}/${maxRetries}): ${server.name}`, err.message);
                                    // 短暂等待后重试
                                    await sleep(2000);
                                }
                            }

                            // 恢复重要日志信息
                            if (!success) {
                                console.error(`[状态监控] 服务器掉线通知发送失败，已达到最大重试次数: ${server.name}`);
                            }
                        } catch (error) {
                            console.error(`[状态监控] 服务器掉线通知处理错误: ${server.name}`, error);
                        }
                    } else {
                        console.log(`[状态监控] 通知系统未初始化，跳过发送: ${server.name}`);
                    }
                } else {
                    console.log(`[状态监控] 服务器掉线通知已禁用，跳过发送: ${server.name}`);
                }
            } else if (!shouldNotify && notice) {
                if (isInitialPeriod) {
                    console.log(`[状态监控] 系统初始化阶段，记录服务器离线状态但不发送通知: ${server.name}`);
                } else {
                    console.log(`[状态监控] 服务器掉线但跳过通知 (状态未变化): ${server.name}`);
                }
            }
        }
    }
}

async function get() {
    let s = new Set(), tasks = [];
    
    // 优化：使用缓存而非数据库查询
    const servers = [];
    for (let [sid, serverConfig] of serverConfigCache) {
        if (serverConfig.status > 0) {
            servers.push({ sid, ...serverConfig });
            
            // 🔧 自愈式状态初始化：确保所有启用服务器都有状态记录
            if (!stats[sid]) {
                const isActiveMode = serverConfig.data?.api?.mode === true;
                stats[sid] = {
                    name: serverConfig.name,
                    stat: isActiveMode ? false : null, // 主动模式初始为离线，被动模式待检查
                    expire_time: serverConfig.expire_time,
                    traffic_used: 0,
                    traffic_limit: serverConfig.traffic_limit || 0,
                    traffic_reset_day: serverConfig.traffic_reset_day || 1,
                    traffic_calibration_date: serverConfig.traffic_calibration_date || 0,
                    traffic_calibration_value: serverConfig.traffic_calibration_value || 0,
                    last_report_time: isActiveMode ? (serverConfig.last_online * 1000 || 0) : undefined
                };
                fails[sid] = 0;
                logger.info(`[状态监控] 自动初始化服务器状态: ${serverConfig.name} (${isActiveMode ? '主动' : '被动'}模式)`);
            }
        }
    }
    
    // 统计信息
    let coolingCount = 0;
    let updatingCount = 0;
    let tasksCreated = 0;
    
    // 收集需要更新的服务器，提前过滤冷却和正在更新的服务器
    for(let server of servers) {
        s.add(server.sid);
        
        // 跳过正在更新的服务器
        if(updating.has(server.sid)) {
            updatingCount++;
            continue;
        }
        
        // 🔥 关键优化：提前检查冷却状态，避免创建无用任务
        const isActiveMode = server.data?.api?.mode === true;
        if (!isActiveMode && isNodeCooling(server.sid)) {
            coolingCount++;
            continue;
        }
        
        // 创建任务函数（不立即执行）
        tasks.push(() => {
            return (async () => {
                updating.add(server.sid);
                try {
                    await update(server);
                } finally {
                    updating.delete(server.sid);
                }
            })();
        });
        tasksCreated++;
    }
    
    // 清理已删除的服务器统计数据
    for(let sid in stats) if(!s.has(sid)) delete stats[sid];
    
    // 使用无限制并发执行所有任务
    if (tasksCreated > 0) {
        // 精简日志：仅在数量有显著变化时输出
        const totalServers = servers.length;
        const activeRatio = Math.round((tasksCreated / totalServers) * 100);
        
        if (tasksCreated !== servers.length || coolingCount > 5) {
            logger.info(`[Stats] 更新 ${tasksCreated}/${totalServers} 个服务器 (活跃率${activeRatio}%)，跳过 ${coolingCount} 个冷却服务器`);
        }
        
        return batchExecute(tasks, 0); // 0 = 无并发限制
    } else {
        // 仅在长时间无活跃服务器时输出警告
        if (servers.length > 0) {
            logger.warn(`[Stats] 警告：所有 ${servers.length} 个服务器均处于冷却或更新状态`);
        }
    }
}
async function calc(){
    try {
        const calcStart = Date.now();
        
        // 1. 从缓存获取服务器列表，避免数据库查询
        const servers = [];
        for (let [sid, serverConfig] of serverConfigCache) {
            if (serverConfig.status > 0) {
                servers.push({ sid, ...serverConfig });
            }
        }
        
        if (servers.length === 0) return;
        
        // 2. 预处理：筛选有效的服务器数据
        const validServers = [];
        let skipCount = 0;
        
        for (let server of servers) {
            const { sid } = server;
            const stat = stats[sid];
            
            if (!stat || !stat.stat || stat.stat === -1) {
                skipCount++;
                continue;
            }
            
            // 验证网络数据结构
            if (!stat.stat.net || typeof stat.stat.net !== 'object' ||
                !stat.stat.net.total || typeof stat.stat.net.total !== 'object') {
                logger.warn(`[Stats] 服务器 ${server.name} 网络数据异常，跳过流量计算`);
                skipCount++;
                continue;
            }

            const ni = Number(stat.stat.net.total.in) || 0;
            const no = Number(stat.stat.net.total.out) || 0;
                
            // 验证流量数据的合理性
            if (ni < 0 || no < 0) {
                logger.warn(`[Stats] 服务器 ${server.name} 流量数据为负值，跳过处理`);
                skipCount++;
                continue;
            }
            
            // 防止异常大的流量值（大于1TB）
            const MAX_REASONABLE_TRAFFIC = 1024 * 1024 * 1024 * 1024;
            if (ni > MAX_REASONABLE_TRAFFIC || no > MAX_REASONABLE_TRAFFIC) {
                logger.warn(`[Stats] 服务器 ${server.name} 流量数据异常大，跳过处理`);
                skipCount++;
                continue;
            }
            
            validServers.push({ ...server, ni, no });
        }
        
        if (validServers.length === 0) return;
        
        // 3. 批量获取流量记录
        const sids = validServers.map(s => s.sid);
        const ltMap = await db.lt.batchGet(sids);
        
        // 4. 处理流量计算和数据收集
        const ltUpdates = [];
        const trafficAdditions = [];
        const missingLtRecords = [];
        
        for (const server of validServers) {
            const { sid, name, ni, no } = server;
            let t = ltMap.get(sid);
            
            // 处理缺失的lt记录
            if (!t || !Array.isArray(t.traffic)) {
                logger.warn(`[Stats] calc() 检测到 ${name} 流量数据异常，记录为需要初始化`);
                missingLtRecords.push({ sid, traffic: [ni, no] });
                continue;
            }

            // 重启检测逻辑
            const veryLowTraffic = (ni < 1048576 && t.traffic[0] > 10485760) ||
                                   (no < 1048576 && t.traffic[1] > 10485760);
            const significantDecrease = (ni < t.traffic[0] * 0.1) || (no < t.traffic[1] * 0.1);
            const trafficReset = (ni === 0 && t.traffic[0] > 0) || (no === 0 && t.traffic[1] > 0);
            const possibleReboot = veryLowTraffic || significantDecrease || trafficReset;
            
            const abnormalIncrease = (ni > t.traffic[0] * 5 && t.traffic[0] > 0) ||
                                     (no > t.traffic[1] * 5 && t.traffic[1] > 0);

            if (possibleReboot) {
                console.log(`检测到节点 ${name} 可能重启`);
                console.log(`  当前流量: in=${ni}, out=${no}`);
                console.log(`  上次记录: in=${t.traffic[0]}, out=${t.traffic[1]}`);
                ltUpdates.push({ sid, traffic: [ni, no] });
                continue;
            }

            if (abnormalIncrease) {
                logger.warn(`检测到节点 ${name} 流量异常增长`);
                logger.warn(`  当前流量: in=${ni}, out=${no}`);
                logger.warn(`  上次记录: in=${t.traffic[0]}, out=${t.traffic[1]}`);
                logger.warn(`  增长倍数: in=${ni/t.traffic[0]}x, out=${no/t.traffic[1]}x`);
                ltUpdates.push({ sid, traffic: [ni, no] });
                continue;
            }

            // 正常计算流量差值
            let ti = ni < t.traffic[0] ? 0 : ni - t.traffic[0];
            let to = no < t.traffic[1] ? 0 : no - t.traffic[1];

            // 合理性检查
            if (ti < 0 || to < 0) {
                logger.warn(`[Stats] calc() 检测到节点 ${name} 流量计算异常: ti=${ti}, to=${to}`);
                ti = Math.max(0, ti);
                to = Math.max(0, to);
            }

            // 记录更新
            ltUpdates.push({ sid, traffic: [ni, no] });
            
            // 只在有实际流量变化时记录traffic增量
            if (ti > 0 || to > 0) {
                trafficAdditions.push({ sid, tf: [ti, to] });
            }
        }
        
        // 5. 批量执行数据库操作
        const dbStart = Date.now();
        
        // 初始化缺失的lt记录
        if (missingLtRecords.length > 0) {
            for (const { sid, traffic } of missingLtRecords) {
                try {
                    await db.lt.ins(sid, traffic);
                } catch (error) {
                    console.error(`[Stats] 初始化流量记录失败: ${sid}`, error);
                }
            }
        }
        
        // 批量更新lt记录
        if (ltUpdates.length > 0) {
            await db.lt.batchSet(ltUpdates);
        }
        
        // 批量添加traffic增量
        if (trafficAdditions.length > 0) {
            await db.traffic.batchAdd(trafficAdditions);
        }
        
        const dbTime = Date.now() - dbStart;
        const totalTime = Date.now() - calcStart;
        
        // 精简日志：仅在关键指标异常时输出
        if (totalTime > 5000 || trafficAdditions.length > 0) {
            logger.info(`[Stats] 流量计算完成: ${validServers.length}个服务器, ${trafficAdditions.length}个增量, 耗时${totalTime}ms`);
        }
        
    } catch (error) {
        console.error('[Stats] calc() 函数执行异常:', error);
        throw error;
    }
}
get();
// 存储定时器引用
let pollingTimer = null;
let calcTimer = null;
let archiveTimer = null; // 归档定时器

// 从数据库读取轮询间隔配置
let pollingInterval = parseInt(db.setting.get('polling_interval')) || 1500; // 默认1.5秒，保证实时性
logger.info(`[监控] 轮询间隔设置为：${pollingInterval}ms`);

// 更新轮询间隔的函数
function updatePollingInterval(newInterval) {
    if (typeof newInterval === 'number' && newInterval > 0) {
        logger.info(`[监控] 更新轮询间隔: ${pollingInterval}ms -> ${newInterval}ms`);
        pollingInterval = newInterval;
        
        // 清除旧定时器
        if (pollingTimer) {
            clearInterval(pollingTimer);
        }
        
        // 设置新定时器
        pollingTimer = setInterval(get, pollingInterval);
        
        // 归档间隔固定为15秒，不随轮询间隔变化
        if (archiveTimer) {
            clearInterval(archiveTimer);
            const newArchiveInterval = 15000; // 固定15秒归档间隔，不随轮询间隔变化
            archiveTimer = setInterval(saveAllLoadData, newArchiveInterval);
            logger.info(`[监控] 负载归档间隔保持固定：${newArchiveInterval}ms (固定15秒间隔，优化数据库性能)`);
        }
        
        // 立即执行一次，使效果立即可见
        get();
    }
}

// 将更新函数暴露给其他模块
svr.locals.updatePollingInterval = updatePollingInterval;

// 添加轮询状态管理，优化重叠处理
let pollingState = {
    isRunning: false,
    startTime: null,
    skipCount: 0,
    totalRuns: 0,
    totalTime: 0,
    lastWarningTime: null  // 添加警告限流时间戳
};

// 初始化定时器（带智能防重叠保护）
pollingTimer = setInterval(async () => {
    if (pollingState.isRunning) {
        pollingState.skipCount++;
        const runningTime = Date.now() - pollingState.startTime;
        
        // 添加限流：30秒内最多输出1次警告
        const now = Date.now();
        if (!pollingState.lastWarningTime || now - pollingState.lastWarningTime > 30000) {
            logger.warn(`[监控] 轮询阻塞：已运行${Math.round(runningTime/1000)}秒，累计跳过${pollingState.skipCount}次`);
            pollingState.lastWarningTime = now;
            
            // 如果轮询时间过长，输出诊断建议
            if (runningTime > pollingInterval * 3) {
                console.error(`[监控] 严重阻塞：建议检查网络连接或调整轮询间隔`);
            }
        }
        return;
    }
    
    pollingState.isRunning = true;
    pollingState.startTime = Date.now();
    
    try {
        await get();
        
        // 统计执行时间
        const executionTime = Date.now() - pollingState.startTime;
        pollingState.totalRuns++;
        pollingState.totalTime += executionTime;
        
        // 每20次轮询输出一次性能统计（精简频率）
        if (pollingState.totalRuns % 20 === 0) {
            const avgTime = Math.round(pollingState.totalTime / pollingState.totalRuns);
            const skipRate = Math.round((pollingState.skipCount/(pollingState.totalRuns+pollingState.skipCount))*100);
            
            // 仅在异常情况下输出日志
            if (avgTime > 3000 || skipRate > 20) {
                logger.warn(`[监控] 轮询异常：平均耗时${avgTime}ms，跳过率${skipRate}%`);
            }
            
            // 重置跳过计数，避免数字过大
            if (pollingState.skipCount > 100) {
                pollingState.skipCount = 0;
            }
        }
        
    } catch (error) {
        console.error('[监控] 轮询执行失败:', error);
    } finally {
        pollingState.isRunning = false;
        pollingState.startTime = null;
    }
}, pollingInterval);
// sleep(10000).then(calc);
// 🔥 并发写入队列：calc函数串行化保护
let calcInProgress = false;

calcTimer = setInterval(async () => {
    if (calcInProgress) {
        logger.info('[Stats] calc 函数正在执行中，跳过本次计算');
        return;
    }
    
    calcInProgress = true;
    try {
        await calc();
    } catch (error) {
        console.error('[Stats] calc 函数执行错误:', error);
    } finally {
        calcInProgress = false;
    }
}, 30*1000);

// 存储最后保存的值，用于减少重复数据
const lastSavedValues = new Map();

// 导入批量操作模块
const { batchInsertLoadArchive, createBatchOperationWithFallback, withPerformanceMonitoring } = require('./batch-operations');
// 导入负载数据处理工具
const { extractServerLoadData } = require('./load-data-utils');

/**
 * 批量插入负载数据到归档表
 * @param {Array} dataArray - 负载数据数组，格式：[{sid, cpu, mem, swap, ibw, obw}, ...]
 * @returns {Promise<number>} 成功插入的记录数量
 */
async function batchInsertLoadData(dataArray) {
    // 使用重构后的批量操作函数
    return await batchInsertLoadArchive(db, dataArray);
}

// 保存负载数据到归档表
async function saveLoadToArchive(sid, cpu, mem, swap, ibw, obw) {
    try {
        // 检查数据有效性 - 至少有一个指标大于0
        const hasValidData = cpu > 0 || mem > 0 || swap > 0;
        if (!hasValidData) {
            // 如果所有指标都是0或负数，跳过保存
            return;
        }

        // 获取上次保存的值
        const lastKey = `${sid}_last`;
        const lastTimeKey = `${sid}_time`;
        const lastValues = lastSavedValues.get(lastKey);
        const lastSaveTime = lastSavedValues.get(lastTimeKey) || 0;
        const now = Date.now();
        
        // 如果数据变化很小且距离上次保存不到30秒，跳过保存
        if (lastValues && (now - lastSaveTime) < 30000) {
            const cpuDiff = Math.abs(cpu - lastValues.cpu);
            const memDiff = Math.abs(mem - lastValues.mem);
            const swapDiff = Math.abs(swap - lastValues.swap);
            
            // 如果所有指标变化都小于0.5%，跳过保存
            if (cpuDiff < 0.5 && memDiff < 0.5 && swapDiff < 0.5) {
                return;
            }
        }
        
        // 更新最后保存的值
        lastSavedValues.set(lastKey, { cpu, mem, swap });
        lastSavedValues.set(lastTimeKey, now);

        // 直接保存数据，不再调用ins方法
        // 这样可以避免每次都插入一条无效记录
        try {
            // 使用自定义SQL直接插入数据，兼容不同数据库的时间函数
            const expireTimeSQL = TimeFunctionUtils.getUnixTimestampWithOffset(db.DB.type, 86400);
            await db.DB.run(`
                INSERT INTO load_archive (
                    sid, cpu, mem, swap, ibw, obw, expire_time
                ) VALUES (
                    ?, ?, ?, ?, ?, ?,
                    ${expireTimeSQL}
                )
            `, [sid, cpu, mem, swap, ibw, obw]);

            // 删除多余的记录，改为1小时
            const maxRecords = Math.ceil(3600000 / pollingInterval); // 1小时的记录数
            
            // 检查记录数量，只有超出时才删除
            const countResult = await db.DB.get(`SELECT COUNT(*) as count FROM load_archive WHERE sid = ?`, [sid]);
            const currentCount = countResult.count;
            
            if (currentCount > maxRecords) {
                const deleteCount = currentCount - maxRecords;
                await db.DB.run(`
                    DELETE FROM load_archive
                    WHERE id IN (
                        SELECT id FROM load_archive
                        WHERE sid = ?
                        ORDER BY created_at ASC
                        LIMIT ?
                    )
                `, [sid, deleteCount]);
            }
        } catch (dbError) {
            console.error('数据库操作失败:', dbError);
            // 如果直接操作失败，回退到使用原始方法
            await db.load_archive.shift(sid, {cpu, mem, swap, ibw, obw});
        }
    } catch (error) {
        console.error('保存负载数据到归档表失败:', error);
    }
}

// 🔥 并发写入队列：简单串行化处理
let saveAllLoadDataInProgress = false;
let saveAllLoadDataStartTime = 0;

// 负载数据归档函数 - 批量插入优化版本（简化后）
async function saveAllLoadData() {
    // 防重入检查
    if (saveAllLoadDataInProgress) {
        const elapsed = Date.now() - saveAllLoadDataStartTime;
        if (elapsed > 30000) { // 30秒超时
            console.error('[监控] 负载数据保存超时，强制重置标志');
            saveAllLoadDataInProgress = false;
        } else {
            logger.info('[监控] 上次负载数据保存仍在进行中，跳过本次执行');
            return;
        }
    }
    
    saveAllLoadDataInProgress = true;
    saveAllLoadDataStartTime = Date.now();
    
    const startTime = Date.now();
    logger.info('[监控] 开始批量保存负载数据');
    
    try {
        const servers = await db.getServers();
        const archiveInterval = 15000; // 固定15秒归档间隔
        
        // 收集所有服务器的负载数据
        const loadDataArray = [];
        let processedCount = 0;
        
        for (const server of servers) {
            const loadData = extractServerLoadData(server.sid, stats);
            if (loadData) {
                loadDataArray.push(loadData);
            }
            processedCount++;
        }
        
        // 执行批量插入
        let savedCount = 0;
        if (loadDataArray.length > 0) {
            savedCount = await batchInsertLoadData(loadDataArray);
            logger.info(`[监控] 批量插入成功，插入 ${savedCount} 条记录`);
        } else {
            logger.info(`[监控] 无有效数据需要插入`);
        }
        
        const errorCount = processedCount - savedCount;
        
        // 输出统计信息
        const elapsed = Date.now() - startTime;
        logger.info(`[监控] 批量保存负载数据完成：已处理${processedCount}个，已保存${savedCount}个，总耗时${elapsed}ms`);
        
        // 如果总耗时接近或超过归档间隔，输出警告
        if (elapsed > archiveInterval * 0.8) {
            logger.warn(`[监控] ⚠️ 负载数据保存耗时 ${elapsed}ms 接近归档间隔 ${archiveInterval}ms，建议优化性能`);
        }
        
    } catch (error) {
        console.error('[监控] 批量保存负载数据失败:', error);
    } finally {
        saveAllLoadDataInProgress = false;
        logger.info('[监控] 负载数据保存标志已重置');
    }
}

// 归档间隔 = 固定15秒，减少数据库写入频率，不影响前端实时性
const initialArchiveInterval = 15000; // 固定15秒归档间隔
archiveTimer = setInterval(saveAllLoadData, initialArchiveInterval);
logger.info(`[监控] 负载归档间隔设置为：${initialArchiveInterval}ms (固定15秒间隔，优化数据库写入性能)`);

schedule.scheduleJob({second:0}, async ()=>{
    for(let {sid} of await db.getServers()){
        let cpu=-1,mem=-1,swap=-1,ibw=-1,obw=-1;
        let stat=stats[sid];
        if(stat&&stat.stat&&stat.stat!=-1){
            cpu=stat.stat.cpu.multi*100;
            mem=stat.stat.mem.virtual.usedPercent;
            swap=stat.stat.mem.swap.usedPercent;
            ibw=stat.stat.net.delta.in;
            obw=stat.stat.net.delta.out;
        }
        await db.load_m.shift(sid,{cpu,mem,swap,ibw,obw});
    }
});
schedule.scheduleJob({minute:0,second:1}, async ()=>{
    await db.traffic.shift_hs();
    for(let {sid} of await db.getServers()){
        let Cpu=0,Mem=0,Swap=0,Ibw=0,Obw=0,tot=0;
        for(let {cpu,mem,swap,ibw,obw} of await db.load_m.select(sid))if(cpu!=-1){
            ++tot;
            Cpu+=cpu,Mem+=mem,Swap+=swap,Ibw+=ibw,Obw+=obw;
        }
        if(tot==0)await db.load_h.shift(sid,{cpu:-1,mem:-1,swap:-1,ibw:-1,obw:-1});
        else await db.load_h.shift(sid,{cpu:Cpu/tot,mem:Mem/tot,swap:Swap/tot,ibw:Ibw/tot,obw:Obw/tot});
    }
});
schedule.scheduleJob({hour:4,minute:0,second:2}, async ()=>{await db.traffic.shift_ds();});
schedule.scheduleJob({date:1,hour:4,minute:0,second:3}, async ()=>{await db.traffic.shift_ms();});

// 获取校准日期后的流量数据
async function getTrafficAfterCalibration(sid, calibrationDate) {
    try {
        // 获取traffic表中的ds数据
        const trafficData = await db.traffic.get(sid);
        if (!trafficData || !trafficData.ds) {
            return 0;
        }

        // 计算校准日期后的总流量
        let totalTraffic = 0;
        for (const record of trafficData.ds) {
            if (record.timestamp > calibrationDate) {
                // ds中的数据是[入站, 出站]格式
                totalTraffic += (record[0] + record[1]);
            }
        }
        return totalTraffic;
    } catch (error) {
        console.error('获取流量数据失败:', error);
        return 0;
    }
}

// 每小时更新一次流量统计
schedule.scheduleJob('0 * * * *', async () => {
    console.log('Updating traffic stats...');
    for(let server of await db.getServers()) {
        if(server.status <= 0) continue;

        // 更新流量统计
        const currentStats = stats[server.sid] || {};
        stats[server.sid] = {
            ...currentStats,
            traffic_used: currentStats.traffic_used || 0,
            traffic_limit: server.traffic_limit || 0
        };
    }
});

// 每分钟检查一次主动模式节点的上报时间
schedule.scheduleJob('* * * * *', async () => {
    // 主动模式节点的超时时间（毫秒）
    const ACTIVE_MODE_TIMEOUT = 5 * 60 * 1000; // 5分钟

    // 遍历所有服务器
    for(let server of await db.getServers()) {
        // 跳过禁用的服务器
        if(server.status <= 0) continue;

        // 检查是否为主动模式
        if(server.data?.api?.mode === true) {
            const sid = server.sid;
            const currentStats = stats[sid];

            // 如果没有状态数据，跳过
            if(!currentStats) continue;

            // 检查最后上报时间
            const lastReportTime = currentStats.last_report_time || 0;
            const now = Date.now();

            // 如果超过超时时间，标记为离线
            if(now - lastReportTime > ACTIVE_MODE_TIMEOUT) {
                // 如果当前不是离线状态，才记录日志和最后在线时间
                if(currentStats.stat !== false) {
                    console.log(`[状态监控] 主动模式节点 ${server.name} 超过 ${ACTIVE_MODE_TIMEOUT/1000} 秒未上报数据，标记为离线`);
                    debugLog(`主动模式节点超时详情: ${server.name}`, {
                        lastReportTime: new Date(lastReportTime).toISOString(),
                        now: new Date(now).toISOString(),
                        diff: (now - lastReportTime) / 1000 + '秒',
                        timeout: ACTIVE_MODE_TIMEOUT / 1000 + '秒'
                    });

                    // 记录最后在线时间
                    const lastOnlineTime = Math.floor(lastReportTime / 1000);
                    await db.servers.upd_last_online(sid, lastOnlineTime);
                    console.log(`[状态监控] 记录主动模式节点最后在线时间: ${server.name}, 时间: ${new Date(lastOnlineTime * 1000).toLocaleString()}`);
                }

                // 检查是否需要发送离线通知
                let notice = false;
                if(currentStats.stat !== false) notice = true;

                // 标记为离线
                stats[sid] = {
                    ...currentStats,
                    stat: false
                };

                // 更新状态缓存
                if(notice) {
                    // 更新状态缓存前的旧状态
                    const oldStatus = serverStatusCache[sid];

                    // 更新状态缓存
                    serverStatusCache[sid] = false;

                    // 判断是否需要发送通知
                    const isInitialPeriod = !initialStatusCollectionComplete;
                    const shouldNotify = !isInitialPeriod && oldStatus === true;

                    // 发送离线通知
                    if(shouldNotify) {
                        const telegramSetting = db.setting.get('telegram');
                        if (telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0 && telegramSetting?.notificationTypes?.serverOffline) {
                            const notifyMessage = `#掉线 [主动模式] ${server.name} ${new Date().toLocaleString()}`;
                            console.log(`[状态监控] 发送主动模式节点掉线通知: ${server.name}`);

                            if (notification && notification.bot) {
                                try {
                                    notification.sendNotification('服务器掉线', notifyMessage, telegramSetting.chatIds);
                                } catch (error) {
                                    console.error(`[状态监控] 发送主动模式节点掉线通知失败: ${server.name}`, error);
                                }
                            }
                        }
                    }
                }
            } else {
                // 如果在超时时间内有上报，但当前状态是离线，则标记为在线
                if(currentStats.stat === false) {
                    // 检查是否需要发送上线通知
                    let notice = true;

                    // 标记为在线，使用上次上报的数据
                    if(currentStats.lastValidStat) {
                        stats[sid] = {
                            ...currentStats,
                            stat: currentStats.lastValidStat,
                            lastValidStat: null
                        };
                        console.log(`[状态监控] 主动模式节点 ${server.name} 恢复在线状态`);
                    }

                    // 更新状态缓存
                    if(notice) {
                        // 更新状态缓存前的旧状态
                        const oldStatus = serverStatusCache[sid];

                        // 更新状态缓存
                        serverStatusCache[sid] = true;

                        // 判断是否需要发送通知
                        const isInitialPeriod = !initialStatusCollectionComplete;
                        const shouldNotify = !isInitialPeriod && oldStatus === false;

                        // 发送上线通知
                        if(shouldNotify) {
                            const telegramSetting = db.setting.get('telegram');
                            if (telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0 && telegramSetting?.notificationTypes?.serverOnline) {
                                const notifyMessage = `#恢复 [主动模式] ${server.name} ${new Date().toLocaleString()}`;
                                console.log(`[状态监控] 发送主动模式节点恢复通知: ${server.name}`);

                                if (notification && notification.bot) {
                                    try {
                                        notification.sendNotification('服务器恢复', notifyMessage, telegramSetting.chatIds);
                                    } catch (error) {
                                        console.error(`[状态监控] 发送主动模式节点恢复通知失败: ${server.name}`, error);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
});

/**
 * 初始化所有服务器的IP位置信息
 * 在服务启动时执行一次
 */
async function initializeServerLocations() {
    console.log(`[${new Date().toISOString()}] 开始初始化服务器IP位置信息...`);

    try {
        // 使用新添加的 checkAndUpdateMissingLocations 方法检查并更新没有位置信息的服务器
        const result = await ipLocationService.checkAndUpdateMissingLocations(db);

        console.log(`[${new Date().toISOString()}] 服务器IP位置初始化完成，共检查 ${result.totalChecked} 个服务器，更新 ${result.totalUpdated} 个，成功 ${result.totalSuccess} 个`);
    } catch (error) {
        console.error(`[${new Date().toISOString()}] 初始化服务器IP位置失败:`, error);
    }
}

/**
 * 定期重试失败的IP位置查询
 * 每 30 分钟执行一次
 */
schedule.scheduleJob('*/30 * * * *', async () => {
    console.log(`[${new Date().toISOString()}] 开始重试失败的IP位置查询...`);
    await ipLocationService.retryFailedUpdates(db);
});

// 服务启动时初始化IP位置
// 延迟 10 秒执行，确保其他服务已经初始化
sleep(10000).then(() => {
    initializeServerLocations();
});

// 添加手动刷新IP位置的API
svr.get("/stats/refresh-ip/:sid", async (req, res) => {
    try {
        const { sid } = req.params;
        const isAdmin = req.admin;

        // 调用 iplocation.js 中的 refreshServerLocation 方法处理 IP 位置更新
        const result = await ipLocationService.refreshServerLocation(sid, db, isAdmin);

        // 处理返回结果
        if (result.status) {
            // 如果有状态码，说明是错误状态
            return res.status(result.status).json({ success: false, message: result.message });
        }

        // 判断是否成功获取了位置信息
        if (result.server_data && result.server_data.location && result.server_data.location.code) {
            // 如果返回了位置信息，则认为更新成功，即使 result.success 为 false
            console.log(`[${new Date().toISOString()}] API 返回成功更新位置信息: ${result.server_data.name} -> ${result.server_data.location.code}`);
            return res.json({
                success: true,
                message: '刷新成功',
                server_data: result.server_data
            });
        }

        // 返回原始处理结果
        res.json(result);
    } catch (error) {
        console.error('刷新IP位置失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});
}

