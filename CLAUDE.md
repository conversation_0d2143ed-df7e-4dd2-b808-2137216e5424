# DStatus 项目配置 - Claude Code

## 🎯 项目核心信息
- **项目名称**: DStatus (nekonekostatus)
- **主要功能**: 分布式服务器监控 + 商业许可证管理
- **技术栈**: Node.js + Express + SQLite/PostgreSQL + Nunjucks + 原生JavaScript
- **架构模式**: 模块化单体应用，双数据库适配器
- **默认端口**: 5555
- **许可证服务器**: https://dstatus_api.vps.mom
- **版本**: 25.08.03
- **Node要求**: ≥20.17.0
- **主入口**: nekonekostatus.js → dstatus.js

## 🏗️ 系统架构图谱

### 整体架构概览
```
DStatus 监控系统
├── 🌐 前端层 (Nunjucks SSR + 原生JS)
│   ├── views/              # 服务端模板
│   ├── static/js/          # 客户端脚本  
│   └── static/css/         # TailwindCSS样式
│
├── 🔌 API层 (Express + WebSocket)
│   ├── modules/api/        # REST API接口
│   ├── express-ws          # WebSocket实时通信
│   └── 路由中间件          # 权限/限流/日志
│
├── 💼 业务逻辑层 (16个核心模块)
│   ├── license-enhanced/   # 💎 商业许可证系统 (核心)
│   ├── servers/           # 🖥️  服务器管理
│   ├── stats/             # 📊 统计数据处理
│   ├── analytics/         # 🧠 数据分析(含AI)
│   ├── notification/      # 📢 消息通知
│   ├── monitoring/        # 👁️  实时监控
│   └── ...               # 其他业务模块
│
├── 🗄️ 数据层 (双适配器架构)
│   ├── adapters/
│   │   ├── sqlite.js      # SQLite适配器 (WAL优化)
│   │   └── postgresql.js  # PostgreSQL适配器
│   ├── 业务数据表          # servers/groups/load/traffic
│   └── 缓存层             # Redis兼容缓存
│
└── 🔧 基础设施层
    ├── 日志系统           # 统一日志管理
    ├── 配置管理           # 环境配置中心
    ├── 定时任务           # node-schedule
    └── 进程管理           # PM2生产部署
```

### 核心模块依赖关系
```
nekonekostatus.js (主入口)
├── database/index.js ────────┐
│   ├── adapters/sqlite.js    │  数据库层
│   └── adapters/postgresql.js┘
│
├── modules/config.js ────────┐
├── modules/license-enhanced/ │  配置&许可层
└── config/*.js ──────────────┘
│
├── modules/api/ ─────────────┐
├── modules/servers/ ─────────│  业务核心层
├── modules/stats/ ───────────│
└── modules/analytics/ ───────┘
│
├── views/ ───────────────────┐
├── static/js/ ───────────────│  表现层
└── static/css/ ──────────────┘
```

## ⚡ 快速入门指南

### 🚀 5分钟启动项目
```bash
# 1. 克隆项目 (如果需要)
git clone <repository-url>
cd dzstatus

# 2. 安装依赖
npm install

# 3. 启动开发环境 (智能启动器)
npm run dev

# 4. 访问系统
# 浏览器打开: http://localhost:5555
# 管理面板: http://localhost:5555/login
# 默认密码: dstatus
```

### 📋 开发环境要求
- **Node.js**: ≥20.17.0 (推荐LTS版本)
- **npm**: ≥8.0.0
- **操作系统**: Linux/macOS/Windows
- **内存**: ≥2GB可用内存
- **磁盘**: ≥1GB可用空间
- **数据库**: SQLite 3.x 或 PostgreSQL 12+

### 🗂️ 项目目录结构快速导航
```
dzstatus/
├── 📄 nekonekostatus.js      # 🚪 主入口文件 (加载dstatus.js)
├── 📄 dstatus.js            # 💼 实际业务主文件
├── 📄 package.json          # 📦 项目配置
├── 📄 CLAUDE.md             # 📖 本文档
├── 📄 ecosystem.config.js   # 🔄 PM2配置
│
├── 📁 modules/              # 💼 业务模块 (16个)
│   ├── license-enhanced/    # 💎 许可证系统 (17个子模块)
│   ├── api/                # 🔌 API接口层
│   ├── servers/            # 🖥️  服务器管理
│   ├── stats/              # 📊 数据统计  
│   ├── analytics/          # 🧠 AI分析 (Gemini 1.5 Pro)
│   ├── notification/       # 📢 通知系统 (支持7种通知类型)
│   └── monitoring/         # 👁️  实时监控
│
├── 📁 database/            # 🗄️ 数据库层
│   ├── adapters/           # 适配器模式
│   ├── index.js            # 数据库工厂
│   └── [业务表].js         # 业务数据操作
│
├── 📁 views/               # 🌐 前端模板
├── 📁 static/              # 🎨 静态资源
│   ├── js/                 # JavaScript文件
│   └── css/                # 样式文件
│
├── 📁 config/              # ⚙️ 配置文件
├── 📁 scripts/             # 🔧 工具脚本 (含密码重置工具)
├── 📁 docs/                # 📚 项目文档
├── 📁 tdd-workspace/       # 🧪 TDD工作区
├── 📁 tests/               # ✅ 测试文件
├── 📁 data/                # 💾 数据目录 (db.db所在)
├── 📁 logs/                # 📝 日志目录
└── 📁 arm-beta/            # 🦾 ARM架构Beta版本
```

## 🚀 核心命令速查

### 开发环境命令
```bash
npm run dev          # 🔧 智能开发启动器 (自动端口检测+热重载)
npm run dev:raw      # 🔄 原始开发模式 (concurrently)
npm run watch:css    # 🎨 Tailwind CSS实时编译
npm run restart      # 🔄 重启服务 (先杀死再启动)
npm run kill:services # 🛑 杀死所有相关服务
```

### 测试命令套件
```bash
npm run test         # ✅ 运行完整测试套件
npm run test:migration    # 🔄 数据库迁移测试
npm run test:compatibility # 🔗 数据库兼容性测试
npm run test:performance  # ⚡ 性能基准测试
npm run test:stress       # 💪 并发压力测试
```

### 构建部署命令
```bash
npm run build             # 🔨 交互式本地构建
npm run build:local       # 🔨 本地构建脚本
npm run build:linux       # 🐧 Linux x64构建
npm run build:linux-arm64 # 🦾 Linux ARM64构建
npm run build:macos       # 🍎 macOS ARM64构建
npm run build:all         # 🌍 全平台构建
```

### 数据库管理命令
```bash
npm run migrate          # 📊 数据库迁移
npm run cleanup          # 🧹 数据库清理
npm run cleanup:dry-run  # 👁️ 清理预览 (不执行)
```

### 文档生成命令
```bash
npm run generate-docs    # 📚 生成API文档
npm run docs            # 📖 文档生成别名
```

### 🧪 测试版本构建命令
```bash
./arm-beta/scripts/build-arm.sh 123      # 🦾 构建ARM64测试版本
./arm-beta/scripts/package-arm.sh 123    # 📦 打包ARM64部署包
./scripts/build-docker-package.sh beta   # 🐳 构建Docker测试版本
./scripts/install-beta.sh                # ⬇️ 安装测试版本
./scripts/reset-password.sh              # 🔐 密码重置工具
```

## 🏗️ 关键技术约定
- **套餐值严禁硬编码** - 必须从数据库读取
- **使用软删除** - 添加 `deleted_at` 时间戳
- **双数据库架构** - SQLite/PostgreSQL适配器模式
- **SQLite WAL 模式** - ✅ 已完全优化，性能提升300-500%
- **实例绑定** - 硬件指纹验证
- **前端架构** - Nunjucks SSR + 原生JavaScript (非Vue/React)
- **WebSocket** - 使用 express-ws (非Socket.io)
- **AI集成** - Gemini 1.5 Pro分析功能
- **密码存储** - SQLite setting表，JSON格式存储
- **二进制打包** - 使用@yao-pkg/pkg构建可执行文件

## 📁 核心文件路径导航

### 🏠 项目核心文件
```
📁 项目根目录
├── 📄 nekonekostatus.js     # 🚪 主入口文件 (引导文件)
├── 📄 dstatus.js            # 💼 业务主文件 (Express应用)
├── 📄 core.js               # 🔧 核心工具函数
├── 📄 package.json          # 📦 项目配置
├── 📄 ecosystem.config.js   # 🔄 PM2生产配置
├── 📄 nodemon.json          # 🔥 开发热重载配置
└── 📄 .gitignore            # 🚫 Git忽略配置
```

### 🗄️ 数据库架构
```
📁 database/
├── 📄 index.js              # 🏭 数据库工厂 (适配器选择)
├── 📄 config.js             # ⚙️ 数据库配置管理
├── 📁 adapters/             # 🔌 数据库适配器
│   ├── 📄 base.js           # 📋 基础适配器接口
│   ├── 📄 sqlite.js         # 💾 SQLite适配器 (WAL优化)
│   └── 📄 postgresql.js     # 🐘 PostgreSQL适配器
├── 📄 servers.js            # 🖥️ 服务器数据操作
├── 📄 load.js               # 📊 负载数据操作
├── 📄 traffic.js            # 🌐 流量数据操作
└── 📁 migration/            # 🔄 数据库迁移工具
```

### 💼 业务模块 (16个核心模块)
```
📁 modules/
├── 📁 license-enhanced/     # 💎 商业许可证系统 (核心业务)
├── 📁 api/                  # 🔌 REST API接口层
├── 📁 servers/              # 🖥️ 服务器管理
├── 📁 stats/                # 📊 统计数据处理
├── 📁 analytics/            # 🧠 数据分析 (含AI)
├── 📁 notification/         # 📢 消息通知系统
├── 📁 groups/               # 👥 服务器分组
├── 📁 ssh_scripts/          # 🔐 SSH脚本管理
├── 📁 autodiscovery/        # 🔍 自动发现服务
├── 📁 admin/                # 👨‍💼 管理员功能
├── 📁 setting/              # ⚙️ 系统设置
├── 📁 advanced-settings/    # 🔧 高级设置
├── 📁 events/               # 📅 事件系统
├── 📁 api-docs/             # 📚 API文档生成
├── 📁 reporting/            # 📋 报告生成
├── 📁 restart/              # 🔄 重启管理
├── 📁 monitoring/           # 👁️  实时监控
├── 📁 config/               # ⚙️ 配置管理
└── 📁 discovery/            # 🔍 服务发现
```

### 🧪 TDD工作空间
```
📁 tdd-workspace/
├── 📄 README.md             # 📖 TDD工作空间说明
├── 📁 phases/               # 🚦 TDD阶段管理
│   ├── 📁 red/              # 🔴 红灯阶段 (编写失败测试)
│   ├── 📁 green/            # 🟢 绿灯阶段 (最小实现)
│   └── 📁 refactor/         # 🔄 重构阶段 (优化代码)
├── 📁 tasks/                # 📋 任务管理
├── 📁 diagnostics/          # 🔍 诊断报告
└── 📁 templates/            # 📝 模板文件
```

## 🗄️ 数据库配置 (双适配器架构)

### SQLite WAL模式优化 (默认)
```javascript
// 在 database/adapters/sqlite.js 中实现
DB.pragma('journal_mode = WAL');
DB.pragma('synchronous = NORMAL');
DB.pragma('cache_size = -64000');      // 64MB缓存
DB.pragma('wal_autocheckpoint = 1000'); // 自动checkpoint
DB.pragma('temp_store = MEMORY');       // 临时表存内存
DB.pragma('mmap_size = 268435456');     // 256MB内存映射
```

### PostgreSQL生产配置
```bash
# 环境变量配置
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_USER=dstatus
DB_PASSWORD=your_password
DB_NAME=dstatus
DB_SSL=false
```

### 数据库适配器切换
```javascript
// 在 database/config.js 中配置
const dbConfig = {
    type: process.env.DB_TYPE || 'sqlite',
    getConfig() {
        if (this.type === 'sqlite') {
            return { path: './data/db.db' };
        } else if (this.type === 'postgresql') {
            return {
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME
            };
        }
    }
};
```

## 🚨 重要提醒
- **只读模式**: 默认只读，需用户审核后才能编码
- **充分调研**: 先搜索代码了解现状，再提出方案
- **永久方案**: 使用永久解决方案，遵循最小化改动
- **主动沟通**: 不确定时使用 mcp feedback 工具联系用户
- **密码安全**: 默认密码 `dstatus`，存储格式为JSON字符串
- **二进制部署**: 生产环境使用pkg打包的二进制文件
- **Docker环境**: data目录持久化挂载，数据库独立于容器

## 🔍 常用工具优先级
- **代码调查**: Grep > Glob > Read (效率递减)
- **并行任务**: 使用 Task 工具多线程并行执行
- **测试验证**: 使用 Bash 工具执行命令
- **问题反馈**: 使用 mcp__interactive-feedback 工具

## 📝 TDD修复记录 (2025-07-30)

### 🎯 数据库同步问题修复完成 
经过系统性的TDD修复流程，已成功解决数据库await缺失问题：

#### ✅ 完成的修复任务
- **TASK-2025-006-001**: license-enhanced模块 (2处await修复)
  - `routes.js:305`: setCurrentLicense调用添加await  
  - `index.js:1238`: setCurrentLicense调用添加await
  - Git提交: be1a029

- **TASK-2025-006-002**: reporting模块 (完整TDD循环)
  - `index.js:145`: db.servers.get调用添加await
  - 重构优化: 函数长度↓69%, 性能↑30-60%
  - Git提交: 00608a7

- **TASK-2025-006-003**: 其他模块 (架构优化)  
  - `stats/batch-insert.js:58`: db.setting.get调用添加await
  - 架构优化: 6个同步调用问题系统性解决
  - Git提交: 784aae1

#### 🚀 修复成果统计
- **直接修复**: 4处await缺失问题
- **架构优化**: 6处同步/异步调用重构
- **性能提升**: 预估30-60%响应速度提升
- **稳定性**: 消除数据竞态条件和Promise未等待风险
- **代码质量**: 100%向后兼容，无功能破坏

#### 🔍 技术要点
- **TDD流程**: 严格遵循红-绿-重构三阶段
- **Git跟踪**: 每次修复都有独立提交记录
- **测试验证**: 所有修复都通过完整测试套件
- **架构改进**: 智能缓存、延迟更新、同步/异步分离

## 💪 专家级子代理团队

### 🔔 通知管理专家 (Notification Expert)
**位置**: `tdd-workspace/notification-expert.md`  
**核心职责**: 
- 统一管理7种通知类型：服务器上下线、流量限制、测试通知、状态摘要、新服务器发现、服务器审批
- 通过"系统设置-Telegram通知设置"集中控制所有通知
- 及时发现通知bug并使用feedback工具联系用户
- 维护NotificationManager核心架构的稳定性
- 支持自我进化机制，持续优化通知功能

**技术特点**:
- 掌握NotificationManager class的完整实现
- 了解去重机制、健康检查、异步处理架构
- 具备通知路由、模板渲染、错误恢复能力

### 💎 许可证功能专家 (License Expert)  
**位置**: `tdd-workspace/license-expert.md`  
**核心职责**:
- 防止许可证功能重复实现，确保开发一致性
- 管理17个子模块的复杂许可证系统架构
- 提供标准化的许可证功能使用模式
- 快速诊断许可证相关问题和冲突
- 维护三层缓存机制的稳定运行

**技术特点**:
- 深度理解17个许可证子模块的依赖关系
- 掌握位掩码权限系统和动态特性定义
- 具备实例绑定验证和License Server同步能力
- 熟悉三层缓存架构：1小时内存缓存 + 168小时离线缓存 + 文件缓存

### 💡 专家调用建议
```bash
# 通知相关问题 - 调用通知专家
Task(prompt="使用通知管理专家处理Telegram通知设置问题", subagent_type="tdd-workspace-manager")

# 许可证相关问题 - 调用许可证专家
Task(prompt="使用许可证功能专家分析功能权限冲突", subagent_type="tdd-workspace-manager")

# 数据库兼容性问题 - 调用数据库专家
Task(prompt="使用数据库兼容性专家解决SQL适配问题", subagent_type="tdd-workspace-manager")
```

## 🛠️ 开发工作流程

### 1. 功能开发流程
```bash
# 1. 查看plan文件恢复记忆
# 2. 分析需求给出方案
# 3. 用户审核后执行
# 4. 更新plan文件记录进展
# 5. 运行测试验证功能
# 6. 提交代码并更新文档
```

### 2. 代码调查流程
```bash
# 使用工具优先级进行调查
Grep -> Glob -> Read
# 并行任务使用Task工具
# 测试验证使用Bash工具
```

### 3. TDD 测试驱动开发流程
```bash
# 🔴 红灯阶段 - 编写失败测试
node tdd-workspace/phases/red/task-001-red-test.js

# 🟢 绿灯阶段 - 最小化实现
node tdd-workspace/phases/green/task-001-green.js

# 🔄 重构阶段 - 优化代码
node tdd-workspace/phases/refactor/task-001-refactor.js

# 🧹 清理TDD工作空间
node scripts/clean-tdd-workspace.js
```

### 4. AI分析功能使用
```bash
# Gemini AI分析配置 (需要API密钥)
export GEMINI_API_KEY=your_api_key

# 运行AI分析功能
node modules/analytics/ai/gemini-service.js
node scripts/summarize_docs_with_gemini.py  # 文档分析

# AI功能权限检查
const hasAI = await featureChecker.check('AI_ANALYTICS');
```

### 4. 许可证功能检查
```javascript
// 功能权限检查模式
const hasFeature = await featureChecker.check('FEATURE_NAME');
if (!hasFeature) return res.status(403).json({error: 'Feature not available'});
```

### 5. JSON安全处理
```javascript
// 使用安全解析函数
function safeJSONParse(str, defaultValue = null) {
  try {
    if (typeof str !== 'string') {
      return typeof str === 'object' ? str : defaultValue;
    }
    const cleaned = str.replace(/^\uFEFF/, '').trim();
    if (!cleaned) return defaultValue;
    return JSON.parse(cleaned);
  } catch (error) {
    console.error('JSON parse error:', error.message);
    return defaultValue;
  }
}
```

## 🧠 项目记忆管理

### 功能特性映射
```javascript
BASIC_MONITORING = 1     // 基础监控
WEBSSH = 2              // Web终端
AUTO_DISCOVERY = 4      // 自动发现
ADVANCED_ANALYTICS = 8   // 高级分析
API_ACCESS = 16         // API访问
CUSTOM_ALERTS = 32      // 自定义告警
AI_ANALYTICS = 64       // AI分析
```

### 许可证层级
- **免费版**: 5节点，基础功能
- **标准版**: 20节点，+WebSSH
- **专业版**: 50节点，+高级分析
- **企业版**: 无限节点，全功能

## 📚 技术栈详解&快速参考

### 🧬 核心技术栈
```
运行时环境: Node.js ≥20.17.0
Web框架: Express.js 4.18.2
模板引擎: Nunjucks (SSR)
数据库: SQLite (WAL) / PostgreSQL 8.12.0
实时通信: express-ws (WebSocket)
样式框架: TailwindCSS 3.4.1
图表库: ECharts
构建工具: @yao-pkg/pkg 6.5.1
进程管理: PM2 (生产环境)
AI分析: Google Gemini 1.5 Pro
```

### 🔌 核心API端点
```bash
# 监控数据API
GET  /api/latest           # 📊 最新监控数据
GET  /api/performance      # ⚡ 性能历史数据
POST /api/tcping           # 🌐 网络质量测试
GET  /api/version          # 📋 版本信息

# 服务器管理API  
GET    /api/servers        # 📋 服务器列表
POST   /api/servers        # ➕ 添加服务器
PUT    /api/servers/:id    # ✏️ 更新服务器
DELETE /api/servers/:id    # 🗑️ 删除服务器

# 统计分析API
GET /api/stats/load        # 📈 负载统计
GET /api/stats/traffic     # 🌐 流量统计
GET /api/stats/monthly     # 📅 月度统计

# WebSocket端点
WS  /ws/latest            # 🔄 实时数据推送
WS  /webssh               # 💻 Web终端连接
```

### 🔧 环境变量配置
```bash
# 核心配置
LICENSE_SERVER_URL=https://dstatus_api.vps.mom
DSTATUS_PORT=5555
NODE_ENV=development|production

# 数据库配置
DB_TYPE=sqlite|postgresql
DB_PATH=./data/db.db                # SQLite路径
DB_HOST=localhost                   # PostgreSQL主机
DB_PORT=5432                        # PostgreSQL端口
DB_USER=dstatus                     # PostgreSQL用户
DB_PASSWORD=your_password           # PostgreSQL密码
DB_NAME=dstatus                     # PostgreSQL数据库名
DB_SSL=false                        # PostgreSQL SSL连接

# AI功能配置
GEMINI_API_KEY=your_gemini_api_key  # Gemini AI密钥

# 性能调优
MAX_MEMORY_RESTART=1G               # PM2内存限制
UPDATE_INTERVAL=5000                # WebSocket更新间隔(ms)

# 通知配置
TELEGRAM_BOT_TOKEN=your_bot_token   # Telegram机器人令牌
TELEGRAM_CHAT_ID=your_chat_id       # Telegram聊天ID
```

### 🏷️ 许可证功能位掩码
```javascript
// 功能特性二进制标识
const FEATURES = {
    BASIC_MONITORING: 1,      // 0000001 - 基础监控
    WEBSSH: 2,               // 0000010 - Web终端  
    AUTO_DISCOVERY: 4,       // 0000100 - 自动发现
    ADVANCED_ANALYTICS: 8,   // 0001000 - 高级分析
    API_ACCESS: 16,          // 0010000 - API访问
    CUSTOM_ALERTS: 32,       // 0100000 - 自定义告警
    AI_ANALYTICS: 64         // 1000000 - AI分析
};

// 许可证层级配置
const LICENSE_PLANS = {
    FREE: 1 | 2,                    // 基础监控 + WebSSH
    STANDARD: 1 | 2 | 4 | 16,       // + 自动发现 + API
    PROFESSIONAL: 1 | 2 | 4 | 8 | 16 | 32,  // + 高级分析 + 告警
    ENTERPRISE: 127                  // 全功能 (所有位)
};
```

## 💻 代码风格规范
- **JavaScript标准**: 使用ES6+语法，const/let替代var
- **异步处理**: 使用async/await替代Promise链
- **模块化**: 遵循现有的模块化结构
- **命名约定**: 遵循项目现有命名规范
- **注释**: 为关键函数添加JSDoc注释
- **错误处理**: 使用try-catch和安全解析函数

## 🔄 工作流程指南
- **变更前**: 先用Grep/Glob工具调查现有代码
- **测试验证**: 使用Bash工具执行相关测试
- **提交规范**: 使用有意义的提交消息
- **功能分支**: 为新功能创建独立分支
- **质量保证**: 确保所有测试通过后再合并

## ⚡ 性能优化指南

### 🗄️ 数据库性能优化
```javascript
// SQLite WAL模式性能调优 (已实现)
- WAL模式启用: 300-500%性能提升
- 64MB缓存设置: cache_size = -64000
- 内存映射: mmap_size = 268435456 (256MB)
- 自动checkpoint: wal_autocheckpoint = 1000
- 临时表内存存储: temp_store = MEMORY

// PostgreSQL连接池优化
- 连接池大小: 20个连接
- 连接复用: keep-alive启用
- 查询超时: 30秒
- 连接监控: 每分钟状态输出
```

### 🌐 前端性能优化
```javascript
// WebSocket连接管理
- 连接复用: 单一连接多通道
- 心跳机制: 30秒间隔
- 断线重连: 指数退避算法
- 数据压缩: JSON最小化

// 静态资源优化  
- TailwindCSS: 生产环境purge
- JavaScript: 模块化懒加载
- 图片资源: SVG矢量优化
- 缓存策略: 浏览器缓存+ETag
```

### 📊 监控数据优化
```javascript
// 数据收集优化
- 批量插入: 每次100条记录
- 数据聚合: 15秒/1分钟/1小时级别
- 过期清理: 自动删除90天前数据
- 索引优化: 时间戳+服务器ID复合索引
```

## 🛠️ Claude Code工作流程指南

### 🎯 重要工作原则
- **只读模式**: 默认只读，需获得明确授权后才能修改代码
- **充分调研**: 使用Grep/Glob工具深入了解现有代码架构
- **永久方案**: 避免临时修补，采用可持续的解决方案
- **主动沟通**: 不确定时使用mcp__interactive-feedback工具咨询用户

### 🔍 代码调研优先级
```bash
1. Grep工具    # 🔍 全文搜索，最高效
2. Glob工具    # 📁 文件模式匹配
3. Read工具    # 📖 具体文件读取
4. Task工具    # 🚀 并行任务执行
5. Bash工具    # ⚙️ 命令执行和验证
```

### 📋 任务管理工具使用
- **TodoWrite工具**: 复杂任务分解和进度跟踪
- **Memory系统**: 跨会话关键信息存储
- **批量操作**: 多文件读写性能优化
- **并行执行**: --parallel标志提升效率

### 🧪 TDD开发循环
```bash
# 🔴 红灯阶段: 编写失败测试
# 🟢 绿灯阶段: 最小化实现通过测试  
# 🔄 重构阶段: 优化代码质量
# 🧹 清理阶段: 移除临时文件
```

## 📖 完整文档索引

### 📚 项目文档
- **API文档**: `docs/API快速参考.md`
- **系统指南**: `docs/DStatus 完整系统启动指南.md`
- **安全方案**: `docs/security/`
- **性能优化**: `docs/passive-mode-optimization.md`
- **数据库迁移**: `docs/database-migration.md`

### 🧠 AI功能文档
- **AI集成方案**: `docs/AI分析功能-Gemini集成方案.md`
- **AI任务指南**: `docs/AI任务执行指南-功能数组实现.md`
- **提示词模板**: `docs/prompts/LATEST_GEMINI_PROMPT.txt`

### 🔧 开发者资源
- **TDD指南**: `tdd-workspace/TDD-DOC-PLAYBOOK.md`
- **构建指南**: `docs/跨平台构建指南.md`
- **测试策略**: `docs/tcping/test-cases.md`
- **模板文件**: `docs/templates/`

---

## 🔐 密码管理与安全

### 密码存储机制
```javascript
// 密码存储在SQLite setting表
// key: "password"
// value: JSON字符串格式 (例: "\"dstatus\"")

// 前端MD5加密
password: md5(password)

// 后端验证
if(password == md5(currentPassword)) {
    // 登录成功
}
```

### 密码重置工具
```bash
# 一键重置密码
curl -fsSL https://down.vps.mom/scripts/reset-password.sh | bash -s -- -y

# 交互式重置
curl -O https://down.vps.mom/scripts/reset-password.sh
chmod +x reset-password.sh
./reset-password.sh
```

## 📊 项目统计信息
- **代码行数**: ~50,000+ 行
- **核心模块**: 16个业务模块
- **支持平台**: Linux (x64/ARM64), macOS (ARM64)
- **数据库**: SQLite/PostgreSQL双适配器
- **前端技术**: 原生JavaScript + Nunjucks模板
- **实时功能**: WebSocket + 5秒更新间隔
- **许可证层级**: 4个商业版本 (免费/标准/专业/企业)
- **构建工具**: @yao-pkg/pkg 6.5.1
- **依赖数量**: 生产依赖20个，开发依赖14个

**配置目的**: 为Claude Code提供DStatus项目的完整技术图谱，支持快速理解架构、高效开发调试和系统优化。

**文档维护**: 本文档随项目演进持续更新，确保技术信息的准确性和时效性。

**最后更新**: 2025-08-03  
**版本**: 2.2.0 (密码管理增强版)