# 统一筛选器迁移记录

## 修改概述
本次修改将所有旧的 `FilterSortManager` 筛选调用替换为新的统一筛选器 `window.UF`。

## 修改的文件列表

### 1. **static/js/FilterSortManager.js**
- 修改了 `filterByGroup`、`filterByStatus`、`filterByExpiry`、`filterByRegion` 函数
- 这些函数现在调用 `window.UF` 的对应方法
- 保留了原有的 API 接口以确保向后兼容
- 修改了 `resetAllFilters` 函数，调用 `window.UF.resetAll()`
- 移除了初始化时的 `classList.remove('hidden-by-*')` 调用

### 2. **static/js/stats.js**
- 替换了所有 `window.FilterSortManager.filterBy*` 调用为 `window.UF.filterBy*`
- 添加了后备机制：如果 `window.UF` 不存在，则使用 `FilterSortManager`
- 修改的函数：
  - `filterByGroup()`
  - `filterByStatus()`
  - `filterByExpiry()`
  - `filterByRegion()`
  - `applyGroupFilter()`

### 3. **static/js/region-stats.js**
- 修改了 `filterByRegion()` 方法，使用 `window.UF.filterByRegion()`
- 修改了 `resetFilter()` 方法，使用 `window.UF.filterByRegion('ALL')`
- 移除了 `classList.add/remove('hidden-by-region')` 调用

### 4. **static/js/region-performance-optimizer.js**
- 修改了 `_batchUpdateCardVisibility()` 方法
- 使用 `window.UF.filterByRegion()` 替代手动的 class 操作
- 保留了后备方案以确保兼容性

## 主要改动点

### 移除的内容
- 所有 `card.classList.add('hidden-by-*')` 调用
- 所有 `card.classList.remove('hidden-by-*')` 调用
- 对 `hidden-by-group`、`hidden-by-status`、`hidden-by-expiry`、`hidden-by-region` 类的依赖

### 保留的内容
- 所有公共 API 接口（函数签名不变）
- 排序功能完全不受影响
- 事件监听器和回调机制
- UI 状态更新逻辑

### 新增的内容
- 对 `window.UF` 的调用
- 后备机制以确保向后兼容

## 测试要点

1. **筛选功能测试**
   - 分组筛选是否正常工作
   - 状态筛选（在线/离线）是否正常
   - 到期时间筛选是否正常
   - 地区筛选是否正常

2. **组合筛选测试**
   - 多个筛选条件同时应用时是否正常
   - 重置筛选是否清除所有条件

3. **兼容性测试**
   - 如果 `window.UF` 未初始化，系统是否能正常降级
   - 排序功能是否不受影响

4. **性能测试**
   - 大量卡片时筛选性能是否正常
   - 页面加载时是否有错误

## 注意事项

1. 确保 `window.UF` 在这些脚本加载前已经初始化
2. 旧的 `hidden-by-*` CSS 类可以在确认没有问题后从样式表中移除
3. `stats-original.js` 文件未修改，因为它看起来是备份文件