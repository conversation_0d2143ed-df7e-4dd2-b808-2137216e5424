# Dashboard 节点筛选修复方案

## 背景
在「Dashboard → 节点情况」区域，按「在线状态」和「到期时间」两个维度进行筛选时，页面没有发生任何可见变化。业务侧反馈无法快速定位即将到期或离线节点。

经初步排查发现：
1. 页面使用 **Universal-Filter-Manager (UF)** 统一处理筛选逻辑，但当前 Dashboard 模板中的相关 DOM 不符合 UF 的预期：
   * 根容器应为 `id="groups-container"`，而实际使用 `id="node-status-container"`。
   * 节点卡片缺少 `data-status` / `data-expiry-days` 等关键数据属性。
2. 在线状态按钮仅切换选中样式，**未调用** `UF.filterByStatus()`；到期时间同理，只修改了下拉选项 UI，没有调用 `UF.filterByExpiry()`。

> 结论：UI 交互与 UF 之间存在 **绑定缺失**，导致筛选逻辑无法触发。即便手动调用 API，也因缺少必要 data-attributes 而无法生效。

---

## 修复目标
1. 使「在线状态」按钮正确过滤 ONLINE / OFFLINE / ALL 节点。
2. 使「到期时间」下拉框按 ≤3 / ≤7 / ≤30 天过滤节点。
3. 保持现有样式及交互无破坏性变更。

---

## 拟实施方案

### 1. 统一容器 & 数据属性
| 项目 | 现状 | 调整 |
| --- | --- | --- |
| 根容器 ID | `node-status-container` | 改为 / 或新增别名 `groups-container`（兼容老逻辑） |
| 节点卡片在线状态 | 无统一属性 | 在渲染时加入 `data-status="ONLINE" | "OFFLINE"` |
| 节点卡片到期天数 | `data-expire`(文本) | 统一为 `data-expiry-days="<int>"` （整数，-1=已过期, 999=永久） |

模板层（`views/stats/dashboard.html` & 相关 partial）新增：
```html
<div id="node-status-container" class="..." data-status="ALL" data-expiry="">
    <!-- 渲染服务器卡片时注入 -->
    <div class="server-card" data-status="ONLINE" data-expiry-days="5" ...>
```

### 2. 绑定交互事件 → UF API
* Online 状态按钮：
  ```js
  document.querySelectorAll('[data-filter-status]').forEach(btn => {
      btn.addEventListener('click', () => UF.filterByStatus(btn.dataset.filterStatus));
  });
  ```
* Expiry 下拉框：
  ```js
  document.getElementById('expiry-select').addEventListener('change', e => {
      UF.filterByExpiry(e.target.value); // '', '3', '7', '30'
  });
  ```

### 3. 样式 (CSS Attribute Selectors)
补充到 `static/css/components/monitor-dashboard.css`：
```css
/* 在线状态过滤 */
#groups-container[data-status="ONLINE"]  .server-card[data-status="OFFLINE"],
#groups-container[data-status="OFFLINE"] .server-card[data-status="ONLINE"]  { display:none; }
/* 到期时间过滤通过 JS data-hidden-by-expiry 控制，可保持现状 */
```

### 4. 回归测试
1. 本地启动 `dzstatus`，进入 Dashboard。
2. 切换「在线」/「离线」/「全部」，验证 DOM 过滤与计数。
3. 修改节点到期值模拟不同天数，验证 3/7/30 天过滤。
4. 打开浏览器控制台确保无报错，确保性能 OK（>500 节点时无明显卡顿）。

### 5. 迭代计划
| 阶段 | 负责人 | 预计耗时 |
| --- | --- | --- |
| 代码修改 & 单元测试 | FE 同学 A | 0.5d |
| 手动回归 / QA | QA 同学 B | 0.5d |
| 预发验证 | FE + QA | 0.5d |
| 正式发布 | 运维 | 0.1d |

---

## 风险与回滚
* 兼容性：保持旧 ID，或在 UF 中增加容器 ID 别名，降低改动面。
* 若发布后出现异常，可快速回滚前端资源版本，不涉及后端。

> 完成以上任务后，Dashboard 节点筛选应恢复正常。
