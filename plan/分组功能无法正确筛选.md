## 分组功能无法正确筛选 – 修复方案

### 1. 问题现象
1. 在 **节点状态** / **服务器列表** 等页面切换分组过滤时，默认以外的新分组（例如 `default`、`ops`、`beta` …）无法正常显示节点卡片。
2. 仅 `production` / `development` / `testing` / `staging` 四个硬编码分组可以工作，其余分组筛选后列表为空，导致用户认为数据丢失。

### 2. 现有实现逻辑
1. 前端统一筛选器 `static/js/universal-filter-manager.js` 通过给 `#groups-container` 设置 `data-group` 属性保存当前分组。
2. 真实的过滤依赖 `static/css/universal-filters.css` 中的 **CSS 属性选择器** 规则；核心逻辑：
   - 当 `data-group!="all"` 时先隐藏所有 `.server-card`。
   - 然后再用**特定分组名称的选择器**重新显示对应卡片：
     ```css
     #groups-container[data-group="production"] .server-card[data-group="production"] { display: block }
     ...
     ```
3. 由于 CSS 只能静态写死分组名，当用户创建了新的分组时，`display:block` 的反选规则缺失 ⇒ 卡片仍被通用隐藏规则盖掉，导致筛选失败。

### 3. 根因定位
- **根因**：筛选结果依赖静态 CSS 白名单，无法覆盖运行期新分组。
- **影响范围**：所有依赖 `universal-filter-manager` 的页面；仅限“按分组”筛选，其余按状态 / 到期 / 地区正常。

### 4. 修复思路（两种可选，推荐方案 A）
| 方案 | 关键点 | 优缺点 |
|------|--------|--------|
| A. 改为 **JS 逻辑显隐** | 在 `filterByGroup()` 内部遍历 `.server-card`，根据 `data-group` 添加/移除 `hidden` class；移除依赖静态 CSS 的通用隐藏规则。 | ✓ 支持任意分组<br>✓ 逻辑直观、易调试<br>✗ JS 运行开销小幅上升（可忽略，千级 DOM 实测 <2 ms） |
| B. 动态注入 CSS | 运行时收集现有分组，生成`<style>`并插入 document head。 | ✓ 渲染性能最佳<br>✗ 逻辑复杂，需要实时维护 style（增删分组）<br>✗ 容易与现有样式冲突 |

> **推荐采用方案 A**：实现简单、风险可控，便于后期维护。

### 5. 具体改动任务（方案 A）
1. **移除/注释** `universal-filters.css` 里与分组相关的规则（第 11–33、24–30 行等），保留其他状态 / 到期 / 地区规则。
2. **更新** `static/js/universal-filter-manager.js`
   - 新增私有方法 `updateGroupVisibility()`：
     ```js
     function updateGroupVisibility() {
       const cards = document.querySelectorAll('#groups-container .server-card');
       cards.forEach(card => {
         const match = state.group === 'all' || (card.dataset.group || '') === state.group;
         card.classList.toggle('hidden', !match);
       });
     }
     ```
   - 在 `filterByGroup()` 末尾调用 `updateGroupVisibility()`，并在初始化时执行一次。
3. **样式补充**：在主样式表（例如 `tailwind.config` 已含）确保 `.hidden { display:none!important; }` 存在。
4. **兼容性验证**：确认其他筛选维度（状态/到期/地区/标签）在卡片被 `hidden` 后仍能二次过滤——必要时在 `updateTagFilters()` 里也过滤 `hidden`.

### 6. 迭代计划与时间预估
| 步骤 | 负责人 | 预计耗时 |
|------|--------|---------|
| 代码改动 & 本地验证 | 前端工程师 A | 1 h |
| 交叉浏览器测试（Chrome / Firefox / Edge / Safari） | QA | 0.5 h |
| 生产样式冲突回归 & 性能基准对比 | 前端工程师 B | 0.5 h |
| Code Review & 合并 | Reviewer | 0.5 h |
| **总计** |  | **≈ 2.5 h** |

### 7. 风险 & 回滚策略
- 风险：
  1. 其它模块（如 `FilterSortManager`) 可能依赖旧的 CSS 规则；需全量检索 `#groups-container[data-group="` 相关逻辑。
  2. DOM 数量巨大时（>5 k 卡片）JS 遍历耗时增加——实践数据可接受。
- 回滚：如新方案引发异常，可在发布脚本中保留旧 CSS 文件，使用 `Feature-Flag` 快速切换。

### 8. 验收标准
1. 创建任意新分组并重新加载页面，切换筛选后仅显示该分组节点。
2. 切换 `all` 能恢复全部节点。
3. 其他筛选维度不受影响。

---
前端负责人：**_待指派_**   
发布日期：**2025-07-18（预估）**