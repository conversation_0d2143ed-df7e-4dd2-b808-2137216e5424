/**
 * 测试CacheManager修复效果
 * 验证所有接口是否正常工作
 */

console.log('=== CacheManager修复效果测试 ===');

// 测试1: 检查CacheManager是否存在
if (typeof window.CacheManager === 'undefined') {
    console.error('❌ CacheManager未定义');
} else {
    console.log('✅ CacheManager已定义');
}

// 测试2: 检查老接口
const oldMethods = ['save', 'load', 'clear'];
oldMethods.forEach(method => {
    if (typeof window.CacheManager[method] === 'function') {
        console.log(`✅ 老接口 ${method} 可用`);
    } else {
        console.error(`❌ 老接口 ${method} 不可用`);
    }
});

// 测试3: 检查新接口别名
const newMethods = ['set', 'get', 'remove'];
newMethods.forEach(method => {
    if (typeof window.CacheManager[method] === 'function') {
        console.log(`✅ 新接口 ${method} 可用`);
    } else {
        console.error(`❌ 新接口 ${method} 不可用`);
    }
});

// 测试4: 检查兼容方法
if (typeof window.CacheManager.cleanupLegacyCache === 'function') {
    console.log('✅ cleanupLegacyCache 方法可用');
} else {
    console.error('❌ cleanupLegacyCache 方法不可用');
}

// 测试5: 功能测试
try {
    // 测试保存和加载
    const testData = { test: 'data', timestamp: Date.now() };
    
    // 使用老接口
    window.CacheManager.save(testData);
    const loadedData = window.CacheManager.load();
    
    if (loadedData && loadedData.test === 'data') {
        console.log('✅ 老接口 save/load 功能正常');
    } else {
        console.error('❌ 老接口 save/load 功能异常');
    }
    
    // 使用新接口别名
    window.CacheManager.set(testData);
    const getData = window.CacheManager.get();
    
    if (getData && getData.test === 'data') {
        console.log('✅ 新接口 set/get 功能正常');
    } else {
        console.error('❌ 新接口 set/get 功能异常');
    }
    
    // 测试清理功能
    window.CacheManager.cleanupLegacyCache();
    console.log('✅ cleanupLegacyCache 执行成功');
    
} catch (error) {
    console.error('❌ 功能测试失败:', error);
}

console.log('=== 测试完成 ===');
