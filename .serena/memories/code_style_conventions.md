# DStatus 代码风格和约定

## JavaScript标准
- 使用ES6+语法，const/let替代var
- 异步处理使用async/await替代Promise链
- 模块化使用CommonJS (require/module.exports)

## 命名约定
- 文件名：小写横线分隔 (kebab-case)
- 变量/函数：驼峰命名 (camelCase)
- 常量：大写下划线分隔 (UPPER_SNAKE_CASE)
- 类名：帕斯卡命名 (PascalCase)

## 项目特定约定
- 套餐值严禁硬编码，必须从数据库读取
- 使用软删除，添加 deleted_at 时间戳
- 双数据库架构，使用适配器模式
- SQLite使用WAL模式优化性能

## 错误处理
- 使用try-catch包装异步操作
- 使用安全解析函数处理JSON
- 记录详细错误日志

## 注释规范
- 为关键函数添加JSDoc注释
- 复杂逻辑添加行内注释
- TODO注释格式：// TODO: 描述