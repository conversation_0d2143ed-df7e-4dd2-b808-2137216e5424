# DStatus 通知系统概览

## 核心架构
- **位置**: `modules/notification/index.js`
- **主类**: `NotificationManager`
- **通知渠道**: Telegram Bot API
- **架构特点**: 模块化设计，支持多种通知类型的独立控制

## 通知类型 (7种)
1. **serverOnline** - 服务器上线通知
2. **serverOffline** - 服务器下线通知
3. **trafficLimit** - 流量超限通知
4. **testNotification** - 测试通知
5. **statusSummary** - 状态汇总通知
6. **newServerDiscovered** - 新服务器发现通知
7. **serverApproved** - 服务器批准通知
8. **systemError** - 系统错误通知 (内部使用)

## 配置管理
- **数据库键**: `telegram`
- **配置项**:
  - `token` - Telegram Bot令牌
  - `chatIds` - 接收通知的聊天ID列表（逗号分隔）
  - `enabled` - 全局开关
  - `notificationTypes` - 各通知类型的独立开关
  - `webhook` - 是否使用Webhook模式
  - `baseApiUrl` - 自定义API URL（默认: https://api.telegram.org）

## 用户界面
- **设置页面**: `/admin/setting` → "Telegram通知设置"
- **功能**:
  - 每种通知类型的独立开关控制
  - 测试通知按钮
  - Webhook/Polling模式切换
  - 自定义API URL配置

## 核心方法
```javascript
// 发送通知
sendNotification(type, content, chatIds, options = {})

// 初始化通知类型配置
initNotificationTypes()

// 发送系统错误通知
sendSystemErrorNotification(errorMessage)
```

## 技术特性
- **去重机制**: 防止短时间内重复发送相同通知
- **健康检查**: 自动检测Bot状态
- **错误恢复**: 内置重试机制
- **异步处理**: 所有通知发送都是异步的
- **类型映射**: 中文类型名自动转换为英文标识符

## 集成要点
- NotificationManager 作为独立模块存在
- 通过统一的"系统设置-Telegram通知设置"页面管理所有通知类型
- 支持即时测试功能验证配置
- 具备完善的错误处理和日志记录机制