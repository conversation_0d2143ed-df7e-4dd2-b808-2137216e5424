# DStatus 开发常用命令

## 开发启动
```bash
npm run dev          # 智能开发启动器 (自动端口检测+热重载)
npm run dev:raw      # 原始开发模式 (concurrently)
npm run watch:css    # Tailwind CSS实时编译
npm run restart      # 重启服务
```

## 测试命令
```bash
npm run test         # 运行完整测试套件
npm run test:migration    # 数据库迁移测试
npm run test:compatibility # 数据库兼容性测试
npm run test:performance  # 性能基准测试
npm run test:stress       # 并发压力测试
```

## 构建部署
```bash
npm run build             # 交互式本地构建
npm run build:linux       # Linux x64构建
npm run build:linux-arm64 # Linux ARM64构建
npm run build:macos       # macOS ARM64构建
npm run build:all         # 全平台构建
```

## 数据库管理
```bash
npm run migrate          # 数据库迁移
npm run cleanup          # 数据库清理
npm run cleanup:dry-run  # 清理预览
```

## 文档生成
```bash
npm run generate-docs    # 生成API文档
```

## Git相关
```bash
git status
git add -A
git commit -m "描述"
git push origin main
```