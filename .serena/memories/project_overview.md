# DStatus 项目概览

## 项目目的
DStatus (nekonekostatus) 是一个分布式服务器监控系统，支持商业许可证管理。

## 技术栈
- **运行时**: Node.js ≥20.17.0
- **Web框架**: Express.js 4.18.2
- **模板引擎**: Nunjucks
- **数据库**: SQLite (better-sqlite3) / PostgreSQL
- **实时通信**: express-ws (WebSocket)
- **样式**: TailwindCSS
- **AI集成**: Google Generative AI
- **进程管理**: PM2 (生产环境)

## 项目结构
- `dstatus.js` - 主入口文件
- `modules/` - 业务模块
- `database/` - 数据库层
- `views/` - 前端模板
- `static/` - 静态资源
- `scripts/` - 工具脚本
- `tests/` - 测试文件